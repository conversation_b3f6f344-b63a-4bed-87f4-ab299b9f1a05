<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.RtvRjzjbdInfoDao">
    <resultMap id="RtvRjzjbdResultMap" type="com.gtja.faRtvData.model.GtjaSsgzZjgpywModel">
        <result column="BUSI_DATE" jdbcType="DECIMAL" property="busi_date"/>
        <result column="PROD_CODE" jdbcType="VARCHAR" property="prod_code"/>
        <result column="BUSI_FLOW_NO" jdbcType="VARCHAR" property="busi_flow_no"/>
        <result column="ACCOUNT_SET_NO" jdbcType="VARCHAR" property="account_set_no"/>
        <result column="DIGEST_ID" jdbcType="VARCHAR" property="digest_id"/>
        <result column="CCY_CODE" jdbcType="CHAR" property="ccy_code"/>
        <result column="SECUID" jdbcType="VARCHAR" property="secuid"/>
        <result column="MARKET_CODE" jdbcType="CHAR" property="market_code"/>
        <result column="SCR_CODE" jdbcType="VARCHAR" property="scr_code"/>
        <result column="FUNDEFFECT" jdbcType="DECIMAL" property="fundeffect"/>
        <result column="STKEFFECT" jdbcType="DECIMAL" property="stkeffect"/>
        <result column="BANK_ACCT" jdbcType="VARCHAR" property="bank_acct"/>
        <result column="PROVISION_ACCT" jdbcType="VARCHAR" property="provision_acct"/>
        <result column="CHECK_STATUS" jdbcType="VARCHAR" property="check_status"/>
        <result column="SEND_DATA" jdbcType="DATE" property="send_data"/>
        <result column="SEND_STATUS" jdbcType="VARCHAR" property="send_status"/>
        <result column="CHECK_DATA" jdbcType="DATE" property="check_data"/>
        <result column="BROKER_CODE" jdbcType="VARCHAR" property="broker_code"/>
        <result column="OPER" jdbcType="VARCHAR" property="oper"/>
    </resultMap>

    <sql id="Rjzjbd_Column_List">
        BUSI_DATE
        , BUSI_FLOW_NO, ACCOUNT_SET_NO, PROD_CODE,DIGEST_ID, CCY_CODE, SECUID, MARKET_CODE,
    SCR_CODE, FUNDEFFECT, STKEFFECT, BANK_ACCT, PROVISION_ACCT, CHECK_STATUS, SEND_DATA, 
    SEND_STATUS, CHECK_DATA, OPER
    </sql>
    <sql id="Rjzjbd_Mq_Column_List">
        BUSI_DATE        ,BUSI_FLOW_NO      ,ACCOUNT_SET_NO       ,DIGEST_ID
      ,CCY_CODE      ,SECUID      ,trim(MARKET_CODE) MARKET_CODE,SCR_CODE      ,FUNDEFFECT
      ,STKEFFECT      ,BROKER_CODE      , BANK_ACCT,PROVISION_ACCT ,BROKER_CODE
    </sql>

    <select id="queryRtvRjzjbdZjgpInfo" parameterType="com.gtja.faRtvData.model.GtjaSsgzZjgpywModel"
            resultMap="RtvRjzjbdResultMap">
        select
        <include refid="Rjzjbd_Mq_Column_List"/>
        from fbzx.gtja_ssgz_zjgpyw t
        where t.BUSI_DATE=#{busi_date}
        AND (t.send_status is null or t.send_status &lt;>'1')
    </select>

    <delete id="deleteByExample" parameterType="com.gtja.faRtvData.model.GtjaSsgzZjgpywModel">
        delete
        from GTJA_SSGZ_ZJGPYW
    </delete>
    <insert id="insert" parameterType="com.gtja.faRtvData.model.GtjaSsgzZjgpywModel">
        insert into GTJA_SSGZ_ZJGPYW (BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO,
                                      DIGEST_ID, CCY_CODE, SECUID,
                                      MARKET_CODE, SCR_CODE, FUNDEFFECT,
                                      STKEFFECT, BANK_ACCT, PROVISION_ACCT,
                                      CHECK_STATUS, SEND_DATA, SEND_STATUS,
                                      CHECK_DATA, OPER)
        values (#{busiDate,jdbcType=DECIMAL}, #{busiFlowNo,jdbcType=VARCHAR}, #{accountSetNo,jdbcType=VARCHAR},
                #{digestId,jdbcType=VARCHAR}, #{ccyCode,jdbcType=CHAR}, #{secuid,jdbcType=VARCHAR},
                #{marketCode,jdbcType=CHAR}, #{scrCode,jdbcType=VARCHAR}, #{fundeffect,jdbcType=DECIMAL},
                #{stkeffect,jdbcType=DECIMAL}, #{bankAcct,jdbcType=VARCHAR}, #{provisionAcct,jdbcType=VARCHAR},
                #{checkStatus,jdbcType=VARCHAR}, #{sendData,jdbcType=DATE}, #{sendStatus,jdbcType=VARCHAR},
                #{checkData,jdbcType=DATE}, #{oper,jdbcType=VARCHAR})
    </insert>
    <update id="updateByExample" parameterType="map">
        update GTJA_SSGZ_ZJGPYW
        set BUSI_DATE      = #{record.busiDate,jdbcType=DECIMAL},
            BUSI_FLOW_NO   = #{record.busiFlowNo,jdbcType=VARCHAR},
            ACCOUNT_SET_NO = #{record.accountSetNo,jdbcType=VARCHAR},
            DIGEST_ID      = #{record.digestId,jdbcType=VARCHAR},
            CCY_CODE       = #{record.ccyCode,jdbcType=CHAR},
            SECUID         = #{record.secuid,jdbcType=VARCHAR},
            MARKET_CODE    = #{record.marketCode,jdbcType=CHAR},
            SCR_CODE       = #{record.scrCode,jdbcType=VARCHAR},
            FUNDEFFECT     = #{record.fundeffect,jdbcType=DECIMAL},
            STKEFFECT      = #{record.stkeffect,jdbcType=DECIMAL},
            BANK_ACCT      = #{record.bankAcct,jdbcType=VARCHAR},
            PROVISION_ACCT = #{record.provisionAcct,jdbcType=VARCHAR},
            CHECK_STATUS   = #{record.checkStatus,jdbcType=VARCHAR},
            SEND_DATA      = #{record.sendData,jdbcType=DATE},
            SEND_STATUS    = #{record.sendStatus,jdbcType=VARCHAR},
            CHECK_DATA     = #{record.checkData,jdbcType=DATE},
            OPER           = #{record.oper,jdbcType=VARCHAR}

    </update>

    <update id="updateRtvRjzjbdZjgpInfo" >
        update GTJA_SSGZ_ZJGPYW
            set
        SEND_DATA = sysdate,
        SEND_STATUS = '1',
        OPER = 'SYS'
        <where>
            <if test="busiDate != null">
                and BUSI_DATE = #{busiDate,jdbcType=DECIMAL}
            </if>
            <if test="busiFlowNo != null">
                and BUSI_FLOW_NO = #{busiFlowNo,jdbcType=DECIMAL}
            </if>
        </where>
    </update>
</mapper>