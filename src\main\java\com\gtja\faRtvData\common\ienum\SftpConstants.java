package com.gtja.faRtvData.common.ienum;

/**
 * @Author: wang<PERSON><PERSON>
 * @Date: 2021/2/18
 */
public class SftpConstants {

    /**
     * sftp发送成功
     */
    public static final String SFTP_SEND_STATE_SUCCESS = "1";

    /**
     * sftp发送失败
     */
    public static final String SFTP_SEND_STATE_FAILED = "0";

    /**
     * sftp操作类型
     * 1-上传 2-下载 3-删除
     */
    public static final String SFTP_OPER_TYPE_UPLOAD = "1";

    public static final String SFTP_OPER_TYPE_DOWNLOAD = "2";

    public static final String SFTP_OPER_TYPE_DELETE = "3";

    // 设置第一次登陆的时候提示，可选值：(ask | yes | no)
    public static final String SESSION_CONFIG_STRICT_HOST_KEY_CHECKING = "StrictHostKeyChecking";

    public static final String SESSION_CONFIG_STRICT_HOST_KEY_CHECKING_NO = "no";

    public static final int SESSION_CONNECT_TIMEOUT = 120000;

    public static final int CHANNEL_CONNECTED_TIMEOUT = 120000;
    public static final String ENCODING ="UTF-8";

}
