package com.gtja.faRtvData.service;

import com.gtja.faRtvData.dao.fbzx.RealTimeValueDao;
import com.gtja.faRtvData.dao.fbzx.SsqsFileOutFogDao;
import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.model.RtvCommonPdfModel;
import com.gtja.faRtvData.model.SsqsFileOutFogModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;

/**
 * @Method: AbstractMailSortProcessor  邮件智能分拣 抽象类
 * @Descripton :DESC
 * <AUTHOR>
 * @Date :2021/7/2817:40
 * @Return:
 * @Exception :
 */
@Slf4j
public abstract class AbstractRtvProcessor implements IRealTimeValueJob {

    @Autowired
    private RealTimeValueDao realTimeValueDao;
    @Autowired
    private SsqsFileOutFogDao ssqsFileOutFogDao;

    /**
     * 实时估值 dbf生成调度任务 抽象刚刚方法
     *
     * @param param 分拣要素
     * @throws Exception
     */
    public void realTimeValueProcessor(RtvCommonPdfModel param) throws Exception {
        //是否需要产品层目录
        log.info("开始生成实时估值dbf:" + param.getRtvDbfName());
        String AbsolutePath = (param.getRtvDbfPath() +"CPDM\\"+ param.getRtvDbfName()).replace("YYYYMMDD", param.getYwrq());
        if (param.getType().equals(Constants.DBF_FILE_TYPE.CPDM)) {
            //需要根据产品生成pdf
            //1.查询 产品代码
            List<String> cpdms = realTimeValueDao.queryRealTimeValueCpConfig1();
            //2.查询数据
            cpdms.stream().forEach(cpdm -> {
                List<T> data = null;
                try {
                    data = queryRtvDataList(cpdm, param.getYwrq());
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                //生成pdf
                try {
                    pdfCreateMothod(AbsolutePath.replace("CPDM", cpdm), data, T.class);
                    insertLog(param,cpdm,"0");
                } catch (IOException e) {
                    insertLog(param,cpdm,"1");
                    throw new RuntimeException(e);
                }
            });
        } else if (param.getType().equals(Constants.DBF_FILE_TYPE.COMMON)) {
            //不需要根据产品生成pdf
            List<T> data = queryRtvDataList();
            try {
                pdfCreateMothod(AbsolutePath, data, T.class);
                insertLog(param,"GG","0");
            } catch (IOException e) {
                insertLog(param,"GG","1");
                throw new RuntimeException(e);
            }
        }
    }

    //记录日志 产品/公共
    public void insertLog(RtvCommonPdfModel param, String type, String successOrFail) {
        SsqsFileOutFogModel model=new SsqsFileOutFogModel();
        model.setCpdm(type);
        model.setStatus(successOrFail);
        model.setYwrq(param.getYwrq());
        model.setFileName(param.getRtvDbfName());
        model.setFilePath(param.getRtvDbfPath());
        ssqsFileOutFogDao.insertLog(model);
    }

    /**
     * pdf 生成的公共方法 单文件级别
     *
     * @param absolutePath
     * @param data
     * @param tclass
     * @throws IOException
     */
    public abstract void pdfCreateMothod(String absolutePath, List<T> data, Class tclass) throws IOException;

    //查询数据集的方法1
    protected abstract List<T> queryRtvDataList() throws Exception;

    //查询数据集的方法1
    protected abstract List<T> queryRtvDataList(String cpdm, String ywrq) throws Exception;
}
