package com.gtja.faRtvData.common.ienum;


import com.gtja.faRtvData.common.exception.IErrorCodeType;

public enum ErrorCode implements IErrorCodeType {

    QUERY_CONDITION_IS_NULL(000000,"查询条件为空"),
    QUERY_RESULT_IS_ERROR(999999,"查询失败"),
    REQUEST_PARAM_IS_NULL_ERROR(999998,"请求参数中必传参数为空"),
    REQUEST_RESULT_IS_NULL_ERROR(999997,"查询结果为空"),
    REQUEST_IS_NULL_ERROR(999996,"请求内容为空"),
    ADD_RESULT_ERROR(999995,"新增数据异常"),

    //衍生品文件下载、
    FILE_PATH_NOT_NULL_WRROR(100000,"下载文件的路径为空"),
    UPDATE_MAIL_STATUS_ERROR(100001,"修改邮件处理状态失败"),
    QUERY_MAIL_INFO_ERROR(100002,"查询邮件信息异常"),

    ADD_MAIL_RULE_ERROR(100003,"新增邮件过滤规则异常"),
    QUERY_MAIL_RULE_ERROR(100004,"查询邮件过滤规则异常"),
    UPDATE_MAIL_RULE_ERROR(100005,"修改邮件过滤规则异常"),
    PUT_MAIL_RULE_STATUS_ERROR(100006,"启停用邮件过滤规则状态异常"),
    AUTO_DEAL_MAIL_RULE_ERROR(100007,"自动忽略处理异常"),
    QUERY_MAIL_DETAIL_BY_ID_ERROR(100008,"查询邮件详细信息异常"),
    QUERY_MAIL_DETAIL_BY_MAIL_DEMENSION_ERROR(100009,"以邮件维度查询邮件信息异常"),
    UPDATE_MAIL_STATUE_ERROR(100010,"修改邮件处理状态失败"),
    DM_REFLUSH_MAIL_MONITOR_ERROR(100011,"估值反刷失败"),
    //QUERY_MAIL_RULE_ERROR(100004,"根据邮件id查询邮件附件的详细异常"),
    QUERY_MAIL_SORT_RULE_DETAIL_BY_ID_ERROR(100012,"根据组id查询所有配置的邮件分类规则明细信息异常"),
    QUERY_FILE_LOG_DETAIL_BY_ID_ERROR(100013,"查询业务日志明细信息异常"),
    QUERY_SHEET_STRUCTURE_DETAIL_BY_LOADID_ERROR(100014,"根据规则id查询查询sheet文件的落地表结构异常"),
    QUERY_OTC_FUND_NET_LOAD_DETAIL_BY_ID_ERROR(100015,"查询净值解析的台账明细信息异常"),
    QUERY_OTC_FUND_NET_DATA_DETAIL_BY_ID_ERROR(100016,"查询净值解析的明细信息异常"),
    QUERY_OPERATE_SQL_LOG_HISTORY_BY_ID__ERROR(100017,"根据sqlId查询操作日志历史记录异常"),
    INSERT_DICTIONARY_BY_ID_ERROR(100018,"新增字典失败{}"),
    ;


    public static final String APPCODE = "INFO_DATA";
    public static final String SYSTEM_ERROR="SystemException";
    public static final String BUSINESS_ERROR="BusinessException";

    public final int code;

    public final String message;

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    ErrorCode(IErrorCodeType errorCodeType) {
        this.code = errorCodeType.getCode();
        this.message = errorCodeType.getMessage();
    }

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String toString() {
        return "CommonErrorCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
