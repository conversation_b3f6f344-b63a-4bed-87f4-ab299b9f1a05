package com.gtja.faRtvData.dao.sjzx;

import com.gtja.faRtvData.model.GtjaSsgzAssetModel;
import com.gtja.faRtvData.model.GtjaSsgzMqRecLogModel;
import com.gtja.faRtvData.model.GtjaSsgzPointModel;
import com.gtja.faRtvData.model.GtjaSsgzPositionModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * RealTimeValueDao 日间资金变动 ta
 */
@Mapper
@Repository
public interface RtvFracResultDao {

    void insertMqLog(GtjaSsgzMqRecLogModel info);

    void insertAsset(@Param("infos") List<GtjaSsgzAssetModel> infos,@Param("msgId") String  msgId,@Param("sourceTime") String  sourceTime);

    void updateAssetCpdm();

    void insertPoint(@Param("infos")List<GtjaSsgzPointModel> infos,@Param("msgId") String  msgId,@Param("sourceTime") String  sourceTime);
    void updatePointCpdm();
    void insertPosition(@Param("infos")List<GtjaSsgzPositionModel> infos,@Param("msgId") String  msgId,@Param("sourceTime") String  sourceTime);
    void updatePositionCpdm();
}
