package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *   概要说明：涉及托管产品资金账户信息（国泰海通证券、国泰君安期货）
 */
@Data
public class RtvGtjaAccountDbfModel {
    @DbfAnnotation(fieldName = "L_FUNDID", fieldLength = 6)
    private String l_fundid;
    @DbfAnnotation(fieldName = "ZHSX", fieldLength = 100)
    private String zhsx;
    @DbfAnnotation(fieldName = "ZHLX", fieldLength = 200)
    private String zhlx;
    @DbfAnnotation(fieldName = "JGBM", fieldLength = 10)
    private String jgbm;
    @DbfAnnotation(fieldName = "ZJZH", fieldLength = 100)
    private String zjzh;
    @DbfAnnotation(fieldName = "ZT", fieldLength = 2)
    private String zt;
}
