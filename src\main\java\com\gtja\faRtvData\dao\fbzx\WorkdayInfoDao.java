package com.gtja.faRtvData.dao.fbzx;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gtja.faRtvData.model.WorkdayInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Author:wuguanqing
 * @Description:
 * @Date:2021/7/27 14:11
 * @Modifird BY:
 */
@Mapper
@Repository
public interface WorkdayInfoDao extends BaseMapper<WorkdayInfo> {
    /***
     * 获取date时间往direction(方向)num天工作日
     * direction -1 想前  +1 向后
     * num 间隔多少个交易日
     * @param date
     * @param num
     * @return
     */
    String getTradeday(@Param("date") String date, @Param("num") int num, @Param("direction") int direction);

    /*
     * 获取date时间当周第numge工作日
     */
    String getTradeNumOfWeek(@Param("date") String date, @Param("num") int num);
    /*
     * 获取date时间当月第num个工作日
     */
    String getTradeNumOfMonth(@Param("date") String date, @Param("num") int num);
    /*
     * 获取date时间当季度第num个工作日
     */
    String getTradeNumOfQuar(@Param("date") String date, @Param("num") int num);
    /*
     * 获取date时间当年度第num个工作日
     */
    String getTradeNumOfYear(@Param("date") String date, @Param("num") int num);

    /**
     * 获取date时间往后num天工作日
     * 获取date时间往direction(方向)num天工作日
     * 工作日取值类型：type 1 联交所 2 香港联交所
     * direction -1 想前  +1 向后
     * num 间隔多少个交易日
     * @param date
     * @param type
     * @param num
     * @param direction
     * @return
     */
    String getTradeday2(@Param("date") String date, @Param("type") String type, @Param("num") Integer num, @Param("direction") String direction);
}
