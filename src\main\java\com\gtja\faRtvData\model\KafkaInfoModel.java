package com.gtja.faRtvData.model;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "ssgz.kafka")
@Component
public class KafkaInfoModel {
    private static final long serialVersionUID = 1L;

    public String userName;

    /**
     * kafka 服务器密码
     **/
    public String passWord;
    public String servers;

}
