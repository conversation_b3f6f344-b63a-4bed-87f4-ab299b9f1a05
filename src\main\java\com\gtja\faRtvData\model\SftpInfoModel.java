package com.gtja.faRtvData.model;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "ssgz.sftp")
@Component
public class SftpInfoModel {
    private static final long serialVersionUID = 1L;
    /**
     * sftp服务器ip
     */
    public String ip;

    /**
     * cifs 文件地址
     *
     */
    public String path;

    /**
     * sftp服务器端口
     */
    public String port;

    /**
     * sftp服务器用户名
     */
    public String userName;

    /**
     * sftp服务器密码
     **/
    public String passWord;




    /**
     * 数据发送使用的协议
     **/
    public String protocol;

    /**
     * sftp服务器域名
     **/
    public String host;
    /**
     * sftp服务器私钥-不使用用户名、密码登录时使用
     **/
    public String privateKey;

    /**
     * sftp服务器私钥密码
     **/
    public String passPhrase;
}
