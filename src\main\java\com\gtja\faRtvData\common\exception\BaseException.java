package com.gtja.faRtvData.common.exception;


import lombok.Data;

import java.io.Serializable;

/**
 * @Description: TODO 异常封装基类
 */
@Data
public class BaseException extends Exception  implements Serializable {
    private static final long serialVersionUID = 6935552774586522701L;
    /**
         * 消息代码
         */
        protected String code;
        /**
         * 详细消息
         */
        protected String message;

        /**
         * 构造一个 BaseException
         */
        public BaseException() {
            super();
        }

        /**
         * 构造一个带指定详细消息的BaseException
         * @param message 消息代码
         */
        public BaseException(String message) {
            super(message);
            this.message = message;
        }

        /**
         * 构造一个带指定详细消息的BaseException
         * @param code    消息代码
         * @param message 详细消息
         */
        public BaseException(String code, String message) {
            super(message);
            this.code = code;
            this.message = message;
        }

        /**
         * 构造一个带代码、指定详细消息、原因的BaseException
         * @param code    消息代码
         * @param message 详细消息
         * @param cause   原因
         */
        public BaseException(String code, String message, Throwable cause) {
            super(message, cause);
            this.code = code;
            this.message = message;
        }

        /**
         * 构造一个指定详细消息、原因的BaseException
         * @param message 详细消息
         * @param cause   原因
         */
        public BaseException(String message, Throwable cause) {
            super(message, cause);
            this.message = message;
        }
}
