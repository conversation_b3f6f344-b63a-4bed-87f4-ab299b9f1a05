package com.gtja.faRtvData.common.utils.sftp;

import com.gtja.faRtvData.common.ienum.SftpConstants;
import com.gtja.faRtvData.model.SftpInfoModel;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Arrays;

@Slf4j
@Component
public class SftpUtils {
    /***
     * 获取连接
     * @param sftpSendConfig
     * @return
     * @throws Exception
     */
    public ChannelSftp getChannelSftp(SftpInfoModel sftpSendConfig) throws Exception {
        ChannelSftp sftp;
        if (StringUtils.isNotBlank(sftpSendConfig.getPrivateKey()) && StringUtils.isEmpty(sftpSendConfig.getPassWord())) {
            sftp = this.connectByKey(sftpSendConfig);
        } else {
            sftp = this.createSftp(sftpSendConfig);
        }
        return sftp;
    }

    /**
     * 加密秘钥方式登陆
     *
     * @return
     */
    private ChannelSftp connectByKey(SftpInfoModel sftpSendConfig) throws Exception {
        JSch jsch = new JSch();
        // 设置密钥和密码 ,支持密钥的方式登陆
        if (StringUtils.isNotBlank(sftpSendConfig.getPrivateKey())) {
            File file = File.createTempFile("temp" + System.currentTimeMillis(), ".tmp");
            FileUtils.writeStringToFile(file, sftpSendConfig.getPrivateKey());
            if (StringUtils.isNotBlank(sftpSendConfig.getPassPhrase())) {
                // 设置带口令的密钥
                jsch.addIdentity(file.getAbsolutePath(), sftpSendConfig.getPassPhrase());
            } else {
                // 设置不带口令的密钥
                jsch.addIdentity(file.getAbsolutePath());
            }
        }
        log.info("Try to connect sftp {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());

        Session session = createSession(jsch, sftpSendConfig.getIp(), sftpSendConfig.getUserName(), Integer.parseInt(sftpSendConfig.getPort()));
        // 设置登陆超时时间
        session.connect(SftpConstants.SESSION_CONNECT_TIMEOUT);
        log.info("Session connected {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());

        // 创建sftp通信通道
        Channel channel = session.openChannel(sftpSendConfig.getProtocol());
        channel.connect(SftpConstants.CHANNEL_CONNECTED_TIMEOUT);
        log.info("Channel created to {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        return (ChannelSftp) channel;
    }

    /**
     * 创建SFTP连接
     *
     * @return
     * @throws Exception
     */
    private ChannelSftp createSftp(SftpInfoModel sftpSendConfig) throws Exception {
        JSch jsch = new JSch();
        log.info("Try to connect sftp {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        Session session = createSession(jsch, sftpSendConfig.getIp(), sftpSendConfig.getUserName(), Integer.parseInt(sftpSendConfig.getPort()));
        session.setPassword(sftpSendConfig.getPassWord());
        session.connect(SftpConstants.SESSION_CONNECT_TIMEOUT);
        log.info("Session connected to {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        Channel channel = session.openChannel(sftpSendConfig.getProtocol());
        channel.connect(SftpConstants.CHANNEL_CONNECTED_TIMEOUT);
        log.info("Channel created to {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        return (ChannelSftp) channel;
    }

    /**
     * 创建session
     *
     * @param jsch
     * @param host
     * @param username
     * @param port
     * @return
     * @throws Exception
     */
    private Session createSession(JSch jsch, String host, String username, Integer port) throws Exception {
        Session session = null;
        if (port <= 0) {
            session = jsch.getSession(username, host);
        } else {
            session = jsch.getSession(username, host, port);
        }
        if (session == null) {
            throw new Exception(host + " session is null");
        }
        session.setConfig(SftpConstants.SESSION_CONFIG_STRICT_HOST_KEY_CHECKING, SftpConstants.SESSION_CONFIG_STRICT_HOST_KEY_CHECKING_NO);
        return session;
    }

    public boolean changeDirectory(String targetPath, ChannelSftp sftp) {
        if (targetPath != null && !targetPath.isEmpty() && sftp != null) {
            String[] dirs = Arrays.stream(targetPath.split("/")).filter(StringUtils::isNotBlank).toArray(String[]::new);
            for (String dir : dirs) {
                try {
                    sftp.cd(dir);
                    log.info("Change directory to {}.", dir);
                } catch (Exception e) {
                    try {
                        sftp.mkdir(dir);
                        log.info("Create directory {}.", dir);
                    } catch (SftpException e1) {
                        log.error("Create directory failure, directory = {}.", dir, e1);
                        e1.printStackTrace();
                    }
                    try {
                        sftp.cd(dir);
                        log.info("Change directory to {}.", dir);
                    } catch (SftpException e1) {
                        log.error("Change directory failure, directory = {}.", dir, e1);
                        e1.printStackTrace();
                    }
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 关闭连接
     *
     * @param sftp
     */
    public void disconnect(ChannelSftp sftp) {
        try {
            if (sftp != null) {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                } else if (sftp.isClosed()) {
                    log.info("sftp is closed already.");
                }
                if (null != sftp.getSession()) {
                    sftp.getSession().disconnect();
                }
            }
        } catch (JSchException e) {
            e.printStackTrace();
        }
    }

}
