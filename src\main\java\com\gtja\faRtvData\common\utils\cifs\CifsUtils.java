package com.gtja.faRtvData.common.utils.cifs;


import com.github.pagehelper.util.StringUtil;
import com.gtja.faRtvData.common.ienum.SftpConstants;
import com.gtja.faRtvData.common.utils.fileDownLoad.FileTools;
import com.gtja.faRtvData.model.NfsInfoModel;
import com.gtja.faRtvData.model.SftpInfoModel;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import jcifs.smb.SmbFileOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * FTP服务器文件上传下载工具类
 */
@Slf4j
@Component
public class CifsUtils {


    /**
     * @param localAbsoluteFile 本地文件的绝对路径
     * @param ftpPath           FTP服务器的绝对路径目录（只能是单层目录： /20240701/）
     * @throws IOException
     * @demoName: UploadCifsFile
     * @Anthor: Magic
     * @Create: 2025/2/13- 11:26
     * @TODO: //TODO   该方法依赖一下依赖，测试环境测试ok,生产环境验证失败：需要切换其他依赖
     *        <dependency>
     *        <groupId>jcifs</groupId>
     *        <artifactId>jcifs</artifactId>
     *        <version>1.3.17</version>
     *        </dependency>  end
     */
    public static void UploadCifsFile(NfsInfoModel sftpInfoModel, String localAbsoluteFile, String ftpPath) throws Exception {
        //创建一个远程文件夹
       // String smbUrl = "smb://" + sftpInfoModel.getIp() + "/" + sftpInfoModel.getShare() + ftpPath;
        String smbUrl = "smb://" + sftpInfoModel.getIp()+ "/" + sftpInfoModel.getShare() +sftpInfoModel.getPath()+ ftpPath;

        NtlmPasswordAuthentication auth =new NtlmPasswordAuthentication(null, sftpInfoModel.userName, sftpInfoModel.passWord);
        log.info("connect cifs start:ip:{};path:{}", sftpInfoModel.getIp(), sftpInfoModel.getPath());
        SmbFile smbFolder = new SmbFile(smbUrl, auth);
        try {
            if (!smbFolder.exists()) {
                smbFolder.mkdir();
            }
        } catch (SmbException e) {
            throw new RuntimeException(e);
        }
        //向远程文件夹上传文件
        File file = new File(localAbsoluteFile);
        SmbFile smbFile = new SmbFile(smbUrl + "/" + file.getName(), auth);

        SmbFileOutputStream smbOut = null;
        BufferedInputStream bf = null;
        try {
            smbOut = new SmbFileOutputStream(smbFile);
            bf = new BufferedInputStream(new FileInputStream(file));
            byte[] bt = new byte[8192];
            int n = bf.read(bt);
            while (n != -1) {
                smbOut.write(bt, 0, n);
                smbOut.flush();
                n = bf.read(bt);
            }
        } catch (UnknownHostException ue) {
            log.error("文件上传失败，失败原因:{}", ue);
            throw new Exception("文件上传失败" + ue.getMessage());
        } catch (Exception e) {
            log.error("文件上传失败，失败原因:{}", e);
            throw new Exception("文件上传失败");
        } finally {
            if (smbOut != null) {
                smbOut.close();
            }
            if (bf != null) {
                bf.close();
            }
        }
    }

}
