package com.gtja.faRtvData.service.sftp.impl;

import com.gtja.faRtvData.common.utils.SplitListForEachUtil;
import com.gtja.faRtvData.common.utils.cifs.CifsUtils;
import com.gtja.faRtvData.common.utils.fileDownLoad.FileTools;
import com.gtja.faRtvData.common.utils.sftp.FtpUtils;
import com.gtja.faRtvData.dao.fbzx.SsqsFileOutFogDao;
import com.gtja.faRtvData.model.NfsInfoModel;
import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.model.SsqsFileOutFogModel;
import com.gtja.faRtvData.service.sftp.FtpSsgzFileDealService;
import com.spire.ms.System.Collections.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
@Slf4j
public class FtpSsgzFileDealServiceImpl implements FtpSsgzFileDealService {
    @Autowired
    FtpUtils ftpUtils;
//    @Autowired
//    OrgCifsUtils orgCifsUtils;
    @Autowired
    CifsUtils orgCifsUtils;
    @Autowired
    FileTools fileTools;
    @Autowired
    SsqsFileOutFogDao ssqsFileOutFogDao;
    @Autowired
    private SplitListForEachUtil splitListForEachUtil;

    /**
     * 上传制定spft 文件
     *
     * @param sourcePath
     * @param targetPath
     * @param fileName
     * @param sftpSendConfig
     * @return
     */
    public String FtpSsgzFilePut(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpSendConfig) throws Exception {
        try {
            log.info("实时估值:ftp文件上传处理开始：上传文件：{}", sourcePath);
            ftpUtils.UploadFTPFiles(sftpSendConfig, sourcePath, targetPath);
            log.info("实时估值:ftp文件上传处理开始：");
        } catch (Exception e) {
            log.info("实时估值:ftp文件上传处理异常" + e.getMessage());
        }
        log.info("Uploader file success. sourcePath = {}, targetPath = {}， fileName = {}.", sourcePath, targetPath, fileName);
        return "文件上传成功";
    }

    /**
     * @demoName:  SsgzFilePutByLogForSftp
     * @Anthor: Magic
     * @Create: 2025/2/13- 11:01
     * @TODO: //TODO 通过文件日志 上传制定spft 文件 end
     * @Param:
     * @Return:
     */
    @Override
    public String SsgzFilePutByLogForSftp(String ywrq, String targetPath, SftpInfoModel sftpSendConfig) throws Exception {
        try {
            List<SsqsFileOutFogModel> infoSends = new ArrayList();
            List<SsqsFileOutFogModel> infoResults = new ArrayList();
            infoSends = ssqsFileOutFogDao.selectSsgzLog(ywrq);
            log.info("实时估值:ftp文件上传处理开始：上传文件条:{}", infoSends.size());
            infoSends.stream().forEach(info -> {
                try {
                    ftpUtils.UploadFTPFile(sftpSendConfig, info.getFilePath(), targetPath + info.getPjPath());
                    info.setStatus("1");//推送成功
                    infoResults.add(info);
                } catch (IOException e) {
                    info.setStatus("-1");//推送失败
                    infoResults.add(info);
                    log.info("实时估值:ftp文件上传处理中：上传文件 路径:{}文件:{},失败", info.getFilePath(), info.getFileName());
                }
            });
            //批量修改状态
            if (null != infoResults && infoResults.size() > 0) {
                List<List<SsqsFileOutFogModel>> lists = splitListForEachUtil.subList(infoResults, 200);
                lists.forEach(list1 -> {
                    ssqsFileOutFogDao.updateFileLogByIds(list1);
                });
            }
            log.info("实时估值:ftp文件上传处理结束");
        } catch (Exception e) {
            log.info("实时估值:ftp文件上传处理异常" + e.getMessage());
            return "实时估值:ftp文件上传处理异常" + e.getMessage();
        }
        return "文件上传成功";
    }


    /**
     * @demoName:  SsgzFilePutByLogForSftp
     * @Anthor: Magic
     * @Create: 2025/2/13- 11:01
     * @TODO: //TODO 通过文件日志 上传制定cifs 文件 end
     * @Param:
     * @Return:
     */
    @Override
    public String SsgzFilePutByLogForCifs(String ywrq, String targetPath, NfsInfoModel nfsSendConfig) throws Exception {
        try {
            List<SsqsFileOutFogModel> infoSends = new ArrayList();
            List<SsqsFileOutFogModel> infoResults = new ArrayList();
            infoSends = ssqsFileOutFogDao.selectSsgzLog(ywrq);
            log.info("实时估值:Cifs文件上传处理开始：上传文件条:{}", infoSends.size());
            infoSends.stream().forEach(info -> {
                try {
                    orgCifsUtils.UploadCifsFile(nfsSendConfig, info.getFilePath(), targetPath + info.getPjPath());
                    info.setStatus("1");//推送成功
                    infoResults.add(info);
                } catch (Exception e) {
                    info.setStatus("-1");//推送失败
                    infoResults.add(info);
                    log.info("实时估值:Cifs文件上传处理中：上传文件 路径:{}文件:{},失败", info.getFilePath(), info.getFileName());
                }
            });
            //批量修改状态
            if (null != infoResults && infoResults.size() > 0) {
                List<List<SsqsFileOutFogModel>> lists = splitListForEachUtil.subList(infoResults, 200);
                lists.forEach(list1 -> {
                    ssqsFileOutFogDao.updateFileLogByIds(list1);
                });
            }
            log.info("实时估值:Cifs文件上传处理结束");
        } catch (Exception e) {
            log.info("实时估值:Cifs文件上传处理异常" + e.getMessage());
            return "实时估值:Cifs文件上传处理异常" + e.getMessage();
        }
        return "文件上传成功";
    }

    /**
     * 下载指定文件
     *
     * @param sourcePath     待下载的源文件
     * @param targetPath     下载的路径
     * @param fileName       下载的文件名称，若无指定文件名称，则下载该路径下的所有文件
     * @param sftpSendConfig
     * @return
     * @throws Exception
     */
    @Override
    public boolean downloadFile(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpSendConfig) throws Exception {
        try {
            log.info("实时估值:ftp文件上传处理开始：上传文件：{}", sourcePath);
            ftpUtils.downFileFtp(sftpSendConfig, sourcePath, targetPath, fileName);
            log.info("实时估值:ftp文件上传处理开始：");
        } catch (Exception e) {
            log.info("实时估值:ftp文件上传处理异常" + e.getMessage());
            return false;
        }
        log.info("Uploader file success. sourcePath = {}, targetPath = {}， fileName = {}.", sourcePath, targetPath, fileName);
        return true;
    }
}
