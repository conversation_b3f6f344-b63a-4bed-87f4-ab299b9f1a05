package com.gtja.faRtvData.common.ienum;

/**
 * @Description: TODO  常量类
 * <AUTHOR> mayangyang
 * @Date : 2020/11/2011:28
 * @Version :1.0
 */


public class Constants {

    public static final int NTHREADS = 8;//开启线程的数量

    /*************************字典类*****************************/
    /*
     *
     */
    public static  class DBF_FILE_NAME{
        public static  String  COMMON_TQS="TQS_YYYYMMDD.DBF";
        public static  String  COMMON_TXTCS="TXTCS_YYYYMMDD.DBF";
        public static  String  CPDM_THKZH="THKZH_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TACCOUNT="TACCOUNT_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TYTTX="TYTTX_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TGDXW="TGDXW_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TLL="TLL_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TFUNDINFO="TFUNDINFO_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TGPSD="TGPSD_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TXTCS="TXTCS_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_BALANCE="BALANCE_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_TJJXSFL="TJJXSFL_CPDM_YYYYMMDD.DBF";
        public static  String  CPDM_GTJA_ACCOUNT="GTJA_ACCOUNT_CPDM_YYYYMMDD.DBF";

        public static  String  CPDM_TJK_ZZQS_TGPSDQ_LDXZK="TJK_ZZQS_TGPSDQ_LDXZK_YYYYMMDD.DBF";//中证折扣率
        public static  String  CPDM_TGPSD_DSFGZ="TGPSD_DSFGZ_CPDM_YYYYMMDD.DBF";//限售股锁定信息

        public static  String  CPDM_TQYXX="TQYXX_YYYYMMDD.DBF";//权益信息
        public static  String  CPDM_TZQXX="TZQXX_YYYYMMDD.DBF";//证券信息

        public static  String  CPDM_GZB="GZB_CPDM_YYYYMMDD.DBF";

    }

    public static  class DBF_FILE_TYPE{
        public static  String  COMMON="0";
        public static  String  CPDM="1";
    }
    public static  class RJZJBD_YWLX{
        public static  String  COMMOM="RJZJBD";
        public static  String  TA="TA";
        public static  String  ZQTZ="ZQTZ";
    }




}
