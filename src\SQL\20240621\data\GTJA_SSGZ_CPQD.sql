prompt Importing table gtja_ssgz_cpqd...
set feedback off
set define off

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SABX14', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SB4633', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SB7800', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SCD764', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SCD768', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SCE364', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SCF062', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SEU664', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SEW407', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SGH076', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SGR319', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SGU577', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SGY828', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SJ0446', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SJC822', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SJC834', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SJL793', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SLE496', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SNY178', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SQA854', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SQW159', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SQW163', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SQW164', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SQW165', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('STW895', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SVP714', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SVY443', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SVZ606', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXA405', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXC345', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXC625', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXE013', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXZ297', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SXZ374', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SY4973', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZJ172', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZK096', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZK291', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZK321', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZM523', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZP865', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZQ226', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZQ227', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZR839', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZU553', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_cpqd (CPDM, D_XGSJ, STATUS)
values ('SZZ676', to_date('18-06-2024 17:44:25', 'dd-mm-yyyy hh24:mi:ss'), '0');

prompt Done.
