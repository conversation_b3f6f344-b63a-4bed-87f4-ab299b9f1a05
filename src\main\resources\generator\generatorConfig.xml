<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <!--数据库驱动-->
   <!-- <classPathEntry location="D:\Java\repository\com\oracle\ojdbc14\10.2.0.5.0\ojdbc14-10.2.0.5.0.jar"/>-->
    <classPathEntry location="D:\ojdbc14-10.2.0.5.0.jar"/>

    <context id="DB2Tables" targetRuntime="MyBatis3">
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!--数据库链接地址账号密码-->
                <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                                connectionURL=" ******************************************" userId="ODS_DC" password="Bhua18$ods_d"/>

<!--        <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"-->
<!--                        connectionURL=" *****************************************" userId="fbzx" password="fbzx"/>-->

        <!--<jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"-->
                        <!--connectionURL=" ****************************************" userId="fbzx" password="fbzx@2019"/>-->
    <!--    <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                        connectionURL="*****************************************" userId="ODS_DC" password="DcdcOd_2020"/>-->
<!--
        <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                        connectionURL="********************************************" userId="gzsj" password="gzsj_2021"/>-->
               <!-- <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                        connectionURL="********************************************" userId="sjzx_etl" password="sjzx_2021"/>-->
      <!--<jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
               connectionURL="********************************************" userId="fbzx" password="fbzx_2021"/>-->
   <!--     <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                        connectionURL="****************************************" userId="fbzx" password="Bhua18$fbzx0"/>-->
    <!--    <jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                       connectionURL="*****************************************" userId="combs_query" password="combsquery"/>-->
        <!--<jdbcConnection driverClass="oracle.jdbc.driver.OracleDriver"
                        connectionURL="*******************************************" userId="gzsj" password="DcdbFasc_2020"/>
-->

        <!--字段映射  int 转integer 以及BigDecimals -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="generator.create" targetProject="src/main/resources/">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="false"/>
        </javaModelGenerator>
        <!--生成Mapper类存放位置-->
        <sqlMapGenerator targetPackage="generator.create" targetProject="src/main/resources/">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--生成映射文件存放位置-->
        <javaClientGenerator type="XMLMAPPER" targetPackage="generator.create" targetProject="src/main/resources/">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>


        <table tableName="GTJA_SSGZ_MQ_REC_LOG" domainObjectName="GtjaSsgzMqRecLogModel" mapperName="GtjaSsgzMqRecLogMapper" enableCountByExample="false"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
        </table>

        <table tableName="GTJA_SSGZ_PROD_ASSET" domainObjectName="GtjaSsgzAssetModel" mapperName="GtjaSsgzAssetMapper" enableCountByExample="false"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
        </table>

        <table tableName="GTJA_SSGZ_PROD_POINT" domainObjectName="GtjaSsgzPointModel" mapperName="GtjaSsgzPointMapper" enableCountByExample="false"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
        </table>

        <table tableName="GTJA_SSGZ_PROD_POSITION" domainObjectName="GtjaSsgzPositionModel" mapperName="GtjaSsgzPositionMapper" enableCountByExample="false"
               enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">
        </table>

        <!--<table tableName="GTJA_SJZX_BASE_RATE" domainObjectName="BaseRateModel" mapperName="BaseRateMapper" enableCountByExample="false"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="GTJA_SJZX_FWXY_DOUBLE_RECORD" domainObjectName="FwxyDoubleRecordModel" mapperName="FwxyDoubleRecordMapper" enableCountByExample="false"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="GTJA_SJZX_FWXY_DOUBLE_OPER" domainObjectName="FwxyDoubleOperModel" mapperName="FwxyDoubleOperMapper" enableCountByExample="false"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

        <!--<table tableName="GTJA_SJZX_FWXY_RATE_CONFIRM" domainObjectName="FwxyRateConfirmModel" mapperName="FwxyRateConfirmMapper" enableCountByExample="false"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->
        <!--<table tableName="GTJA_SJZX_RATE_SUBSIDIARY" domainObjectName="FwxyRateSubsidiaryModel" mapperName="FwxyRateSubsidiaryMapper" enableCountByExample="false"-->
               <!--enableUpdateByExample="true" enableDeleteByExample="true" enableSelectByExample="true" selectByExampleQueryId="true">-->
        <!--</table>-->

    </context>
</generatorConfiguration>
