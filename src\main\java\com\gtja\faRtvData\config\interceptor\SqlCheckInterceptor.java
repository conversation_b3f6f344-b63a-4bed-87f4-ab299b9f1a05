package com.gtja.faRtvData.config.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.springframework.util.StringUtils;

import java.util.Properties;

@Intercepts(
        {
                @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
                @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        }
)
@Slf4j
public class SqlCheckInterceptor implements Interceptor {
    private final int MAX_PLACEHOLDER_COUNT = 10000;
    private final int FORCE_KILL_COUNT = 30000;

//    @Resource
//    public MessageService messageService;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        BoundSql boundSql = ms.getBoundSql(parameter);
        String sql = boundSql.getSql();
        int num = StringUtils.countOccurrencesOf(sql, "?");


        if (num > FORCE_KILL_COUNT) {
            String message = "faRtvData :sql语句占位符超过应用阈值，SQL不执行:" + num;
            //log.error(sql.substring(0, 300));
          //  messageService.sendMessage(10038, message);
            return null;
        }

        if (num > MAX_PLACEHOLDER_COUNT) {
            //log.error("faRtvData_sql语句占位符数量超限" + num);
            //log.error(sql.substring(0, 300));
            String message = "faRtvData_sql语句占位符数量超限:" + num;
        }

        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
    }
}