package com.gtja.faRtvData.common.page;

import org.apache.ibatis.session.RowBounds;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 查询数据分页 page 类
 * @param <T>
 */
public class Page<T> extends RowBounds implements Serializable {
    private static final long serialVersionUID = -1909366153312943053L;

     private static final Integer TOTAL_COUNT_UNKNOW = -1;

    protected Integer pageNo = 1;
    protected Integer pageSize = -1;
    protected boolean autoCount = true;

    protected List<String> orderByCol;
    protected List<String> orderByMode;

    protected List<T> results = new ArrayList<T>();
    protected Integer totalCount = -1;

    public Page() {
    }

    public Page(Integer pageNo, Integer pageSize) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
    }

    public Page(Integer pageNo, Integer pageSize, List<String> orderByCol, List<String> orderByMode) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.orderByCol = orderByCol;
        this.orderByMode = orderByMode;
    }

    public Page buildWithOffsetLimit(Integer limit, Integer offset) {
        this.pageNo = offset % limit + 1;
        this.pageSize = limit;
        return this;
    }

    public Page(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public Page<T> setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
        if (pageNo < 1) {
            this.pageNo = 1;
        }
        return this;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public Page<T> setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public Integer getFirst() {
        return ((pageNo - 1) * pageSize) + 1;
    }

    public boolean isAutoCount() {
        return autoCount && totalCount == TOTAL_COUNT_UNKNOW;
    }

    public Page<T> setAutoCount(boolean autoCount) {
        this.autoCount = autoCount;
        return this;
    }

    public List<T> getResults() {
        return results;
    }

    public Page<T> setResults(List<T> results) {
        this.results = results;
        return this;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public Page<T> setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
        return this;
    }

    public List<String> getOrderByCol() {
        return orderByCol;
    }

    public void setOrderByCol(List<String> orderByCol) {
        this.orderByCol = orderByCol;
    }

    public List<String> getOrderByMode() {
        return orderByMode;
    }

    public void setOrderByMode(List<String> orderByMode) {
        this.orderByMode = orderByMode;
    }

    public Integer getTotalPages() {
        if (totalCount < 0) {
            return -1;
        }

        Integer count = totalCount / pageSize;
        if (totalCount % pageSize > 0) {
            count++;
        }
        return count;
    }

    public boolean isHasNext() {
        return (pageNo + 1 <= getTotalPages());
    }

    public Integer getNextPage() {
        if (isHasNext()) {
            return pageNo + 1;
        } else {
            return pageNo;
        }
    }

    public boolean isHasPre() {
        return (pageNo - 1 >= 1);
    }

    public Integer getPrePage() {
        if (isHasPre()) {
            return pageNo - 1;
        } else {
            return pageNo;
        }
    }

    public Integer offset() {
        return (pageNo - 1) * pageSize;
    }

    public Integer limit() {
        return pageSize;
    }

    @Override
    public String toString() {
        return "Page{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", autoCount=" + autoCount +
                ", orderByCol=" + orderByCol +
                ", orderByMode=" + orderByMode +
                ", results=" + results +
                ", totalCount=" + totalCount +
                '}';
    }
}