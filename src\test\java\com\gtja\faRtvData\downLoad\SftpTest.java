package com.gtja.faRtvData.downLoad;

import com.gtja.faRtvData.model.SftpInfoModel;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import static cn.hutool.extra.ssh.JschUtil.createSession;

@Slf4j
public class SftpTest {
    public static void main(String[] args) {
      //  ChannelSftp sftp = getChannelSftp(sftpSendConfig);
    }
    @Test
    public void createSftp() throws Exception {
        SftpInfoModel sftpSendConfig=new SftpInfoModel();
        sftpSendConfig.setIp("***************");
        sftpSendConfig.setPort("22");
        sftpSendConfig.setUserName("GUOTAIJUFTPH1");
        sftpSendConfig.setPassWord("R840A6Hd");

        JSch jsch = new JSch();
        log.info("Try to connect sftp {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        Session session = createSession(jsch, sftpSendConfig.getIp(), sftpSendConfig.getUserName(), Integer.parseInt(sftpSendConfig.getPort()));
        session.setPassword(sftpSendConfig.getPassWord());
        session.connect(120000);
        log.info("Session connected to {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
        Channel channel = session.openChannel(sftpSendConfig.getProtocol());
        channel.connect(120000);
        log.info("Channel created to {}@{}.", sftpSendConfig.getUserName(), sftpSendConfig.getIp());
      //  return (ChannelSftp) channel;
    }
    private Session createSession(JSch jsch, String host, String username, Integer port) throws Exception {
        Session session = null;
        if (port <= 0) {
            session = jsch.getSession(username, host);
        } else {
            session = jsch.getSession(username, host, port);
        }
        if (session == null) {
            throw new Exception(host + " session is null");
        }
        session.setConfig("StrictHostKeyChecking", "no");
        return session;
    }
}
