package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：管理费、托管费及预提待摊费用计算参数
 */
@Data
public class RtvTyttxDbfModel {
    @DbfAnnotation(fieldName = "L_BH", fieldLength = 10)
    private String l_bh;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 6)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_ZY", fieldLength = 100)
    private String vc_zy;
    @DbfAnnotation(fieldName = "L_JTFS", fieldLength = 2)
    private String l_jtfs;
    @DbfAnnotation(fieldName = "L_TS", fieldLength = 4)
    private String l_ts;
    @DbfAnnotation(fieldName = "EN_JTFL", fieldLength = 32)
    private String en_jtfl;
    @DbfAnnotation(fieldName = "D_BEGIN", fieldLength = 16)
    private String d_begin;
    @DbfAnnotation(fieldName = "D_END", fieldLength = 16)
    private String d_end;
    @DbfAnnotation(fieldName = "VC_DFKM", fieldLength = 32)
    private String vc_dfkm;
    @DbfAnnotation(fieldName = "EN_JE", fieldLength = 32)
    private String en_je;
    @DbfAnnotation(fieldName = "VC_JFKM", fieldLength = 32)
    private String vc_jfkm;
}
