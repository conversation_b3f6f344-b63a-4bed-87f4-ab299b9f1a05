prompt Importing table gtja_ssgz_file_qd...
set feedback off
set define off

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TXTCS_CPDM_YYYYMMDD.DBF', '系统参数', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TXTCS_YYYYMMDD.DBF', '系统参数', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TGPSD_CPDM_YYYYMMDD.DBF', '新股锁定', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TFUNDINFO_CPDM_YYYYMMDD.DBF', '产品基本信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('GTJA_ACCOUNT_CPDM_YYYYMMDD.DBF', '资金账户', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TJJXSFL_CPDM_YYYYMMDD.DBF', '产品分级信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('BALANCE_CPDM_YYYYMMDD.DBF', '余额信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TACCOUNT_CPDM_YYYYMMDD.DBF', '科目信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('THKZH_CPDM_YYYYMMDD.DBF', '托管账户信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TYTTX_CPDM_YYYYMMDD.DBF', '预提待摊', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TLL_CPDM_YYYYMMDD.DBF', '银行存款计息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TQS_YYYYMMDD.DBF', '经纪商信息', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TGDXW_CPDM_YYYYMMDD.DBF', '股东席位信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into fbzx.gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TGPSD_DSFGZ_CPDM_YYYYMMDD.DBF', '限售股锁定信息', '1', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into fbzx.gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TJK_ZZQS_TGPSDQ_LDXZK_YYYYMMDD.DBF', '中证折扣率', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into fbzx.gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TZQXX_YYYYMMDD.DBF', '证券信息', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');

insert into fbzx.gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('TQYXX_YYYYMMDD.DBF', '权益信息', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');



insert into gtja_ssgz_file_qd (FILE_NAME, FILE_NAME_DESC, TYPE, CREATE_DATE, STATUS)
values ('GZB_CPDM_YYYYMMDD.DBF', '估值', '0', to_date('21-06-2024 13:45:45', 'dd-mm-yyyy hh24:mi:ss'), '0');
commit;

prompt Done.
