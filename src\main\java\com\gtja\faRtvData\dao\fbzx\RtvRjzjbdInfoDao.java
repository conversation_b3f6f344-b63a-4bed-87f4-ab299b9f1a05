package com.gtja.faRtvData.dao.fbzx;

import com.gtja.faRtvData.model.GtjaSsgzTaywModel;
import com.gtja.faRtvData.model.GtjaSsgzZjgpywModel;
import com.gtja.faRtvData.model.SsgzMqSendLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * RealTimeValueDao 日间资金变动 ta
 */
@Repository
public interface RtvRjzjbdInfoDao {
    List<GtjaSsgzTaywModel> queryRtvRjzjbdTaInfo(String busi_date);

    void updateRtvRjzjbdTaInfo(@Param("busiFlowNo") String busiFlowNo,@Param("busiDate") String busiDate);

    List<GtjaSsgzZjgpywModel> queryRtvRjzjbdZjgpInfo(String busi_date);

    void updateRtvRjzjbdZjgpInfo(@Param("busiFlowNo") String busiFlowNo,@Param("busiDate") String busiDate);

    int  insertSenderLog(SsgzMqSendLog log);
}
