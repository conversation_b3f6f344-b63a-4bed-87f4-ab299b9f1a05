package com.gtja.faRtvData.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class GtjaSsgzZjgpywModel  implements Serializable {

    private String busi_date;

    private String busi_flow_no;

    private String account_set_no;

    private String prod_code;

    private String digest_id;

    private String ccy_code;

    private String secuid;

    private String market_code;

    private String scr_code;

    private BigDecimal fundeffect;

    private BigDecimal stkeffect;
    private String   broker_code;
    private String bank_acct;

    private String provision_acct;

    private String check_status;

    private Date send_data;

    private String send_status;



    private Date check_data;

    private String oper;

}