package com.gtja.faRtvData.model;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class GtjaSsgzMqRecLogModel {
    private String SRC;

    private String SRC_IP;

    private String MSG_TYPE;

    private String MSG_SUBTYPE;

    String MSG_ID ;
    String MSG_GROUP_SNO ;
    String MSG_GROUP_ID ;

    private BigDecimal MSG_GROUP_CNT;

    private String TARGET;

    private String SEND_DATE;

    private String SEND_TIME;

    private String LOAD_TIME;

    private  String fv_result_hold_total;
    private String fv_result_hold_offset;

    private Long offset;
    private String content;
}