package com.gtja.faRtvData.downLoad;

import com.alibaba.fastjson.JSON;
import com.gtja.faRtvData.common.utils.ip.IPAddressUtils;
import com.gtja.faRtvData.dao.fbzx.RtvRjzjbdInfoDao;
import com.gtja.faRtvData.model.GtjaSsgzTaywModel;
import com.gtja.faRtvData.model.NfsInfoModel;
import com.gtja.faRtvData.model.RtvRjzjbdTaMqModel;
import jcifs.smb.NtlmPasswordAuthentication;
import jcifs.smb.SmbException;
import jcifs.smb.SmbFile;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.concurrent.ListenableFuture;
import tools.DateTimeTools;

import java.io.*;
import java.net.MalformedURLException;
import java.net.UnknownHostException;
import java.util.List;
import java.util.UUID;

@RunWith(SpringRunner.class)
@SpringBootTest
@EnableKafka
@Slf4j
public class MqTest {

    @Autowired
    private KafkaTemplate kafkaTemplate;

    @Autowired
    private RtvRjzjbdInfoDao rtvRjzjbdInfoDao;
    private final static String TOPIC_RJZJBD = "G_P_USER_ZCTG_RJZJBD";

    @Autowired
    NfsInfoModel nfsInfo;


    @Test
    public void testoo2() {
        String localAbsoluteFile = "C:\\Users\\<USER>\\BALANCE_SCE364_20250213.DBF";
        FileOutputStream smbOut = null;
        BufferedInputStream bf = null;

        String targerFilePath = "D:\\Users\\User\\Desktop\\实时估值\\cifs-testfile";
        File file = new File(targerFilePath);
        if (!file.isDirectory()) {
            file.mkdirs();
        }
        //向远程文件夹上传文件

        try {
            File outPutFile=new File(localAbsoluteFile);
            smbOut = new FileOutputStream(targerFilePath+"/"+outPutFile.getName());
            bf = new BufferedInputStream(new FileInputStream(localAbsoluteFile));
            byte[] bt = new byte[8192];
            int n = bf.read(bt);
            while (n != -1) {
                smbOut.write(bt, 0, n);
                smbOut.flush();
                n = bf.read(bt);
            }
        } catch (UnknownHostException ue) {
            log.error("文件上传失败，失败原因:{}", ue);
        } catch (Exception e) {
            log.error("文件上传失败，失败原因:{}", e);
        } finally {
            if (smbOut != null) {
                try {
                    smbOut.close();
                } catch (IOException e) {
                    log.error("smbOut.close 失败原因:{}", e);
                }
            }
            if (bf != null) {
                try {
                    bf.close();
                } catch (IOException e) {
                    log.error("bf.close 失败原因:{}", e);
                }
            }
        }
    }

    @Test
    public void test001() {
        String smbUrl = "smb://" + nfsInfo.getIp() + "/" + nfsInfo.getShare();
        NtlmPasswordAuthentication auth = new NtlmPasswordAuthentication(null, nfsInfo.userName, nfsInfo.passWord);


        SmbFile smbFile = null;
        try {
//            smbFile = new SmbFile(smbUrl, auth);
//            smbFile.connect();
//            if (!smbFile.exists()) {
//                smbFile.mkdir();
//            }

            String smbUrl1 = "smb://" + nfsInfo.getIp() + "/" + nfsInfo.getShare() + "/" + "test001";

            smbFile = new SmbFile(smbUrl1, auth);
            if (!smbFile.exists()) {
                smbFile.mkdir();
            }

//        } catch (MalformedURLException e) {
//            throw new RuntimeException(e);
//        } catch (SmbException e) {
//            throw new RuntimeException(e);
//        } catch (IOException e) {
//            throw new RuntimeException(e);
        } catch (Exception e) {
            log.info("YICHANG :" + e.getMessage());
        } finally {
            log.info("jieshu");
        }

    }

    @Test
    public void sendMessage() {
        String ywlx = "RJZJBD";
        String ywlxs_sub = "RJZJBD_TA";

        List<GtjaSsgzTaywModel> tayws = rtvRjzjbdInfoDao.queryRtvRjzjbdTaInfo("20240619");
        tayws.stream().forEach(info -> {
            RtvRjzjbdTaMqModel base = new RtvRjzjbdTaMqModel();
            base.setCONTENT(info);
            mqHandInit(base, ywlx, ywlxs_sub);
            System.out.println("send-risk,MSG_ID:{} " + base.getMSG_ID());
            String message = JSON.toJSONString(base);
            ListenableFuture future = kafkaTemplate.send(TOPIC_RJZJBD, message);
            future.addCallback(o -> System.out.println("send-risk消息发送成功：" + message)
                    , throwable -> System.out.println("risk消息发送失败：" + message));
        });
    }

    private void mqHandInit(RtvRjzjbdTaMqModel info, String ywlx, String ywlx_sub) {
        info.setSRC_IP(IPAddressUtils.getIPAddress());
        info.setMSG_ID(UUID.randomUUID().toString());
        info.setMSG_TYPE(ywlx);
        info.setMSG_SUBTYPE(ywlx_sub);
        info.setSEND_DATE(DateTimeTools.getDateNumNow());
        info.setSEND_TIME((DateTimeTools.getTimestampNow()).substring(8, 14));
    }
}
