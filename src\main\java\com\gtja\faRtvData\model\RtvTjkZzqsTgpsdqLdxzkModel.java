package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvTjkZzqsTgpsdqLdxzkModel {
    @DbfAnnotation(fieldName = "GZR", fieldLength = 8)
    private String gzr;
    @DbfAnnotation(fieldName = "GPDM", fieldLength = 6)
    private String gpdm;
    @DbfAnnotation(fieldName = "XSQJSR", fieldLength = 8)
    private String xsqjsr;
    @DbfAnnotation(fieldName = "GFBDGGR", fieldLength = 8)
    private String gfbdggr;
    @DbfAnnotation(fieldName = "SSHY", fieldLength = 100)
    private String sshy;
    @DbfAnnotation(fieldName = "DYHYZS", fieldLength = 6)
    private String dyhyzs;
    @DbfAnnotation(fieldName = "SYXSQ", fieldLength = 6)
    private String syxsq;
    @DbfAnnotation(fieldName = "LDXZK", fieldLength = 15)
    private String ldxzk;
    @DbfAnnotation(fieldName = "BL", fieldLength = 100)
    private String bl;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 9)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "D_YWRQ", fieldLength = 20)
    private String d_ywrq;

}
