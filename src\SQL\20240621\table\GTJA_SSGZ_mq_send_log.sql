-- Create table
create table GTJA_SSGZ_MQ_SEND_LOG
(
    src         VARCHAR2(64) not null,
    src_ip      VARCHAR2(32),
    msg_type    VARCHAR2(32),
    msg_subtype VARCHAR2(20),
    msg_id      VARCHAR2(64) default '0',
    target      VARCHAR2(20) default '0',
    send_date   VARCHAR2(20),
    send_time   VARCHAR2(20),
    o_time      VARCHAR2(20) default to_char(sysdate,'yyyymmdd.hh24miss'),
    c_time      VARCHAR2(20) default to_char(sysdate,'yyyymmdd.hh24miss'),
    err_msg     VARCHAR2(2000),
    content     CLOB
);
-- Add comments to the table
comment on table GTJA_SSGZ_MQ_SEND_LOG
  is 'mq发送记录日志（实时估值）';
-- Create/Recreate indexes
create index IDX_SSGZ_MQ_SEND_LOG_1 on GTJA_SSGZ_MQ_SEND_LOG (MSG_ID);
-- Grant/Revoke object privileges
grant select on GTJA_SSGZ_MQ_SEND_LOG to SJPT_ROLE;
