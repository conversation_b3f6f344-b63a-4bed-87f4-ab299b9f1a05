package com.gtja.faRtvData.service.mq;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.gtja.faRtvData.common.ienum.TopicConstans;
import com.gtja.faRtvData.config.db.DataSourceConfigsjzx;
import com.gtja.faRtvData.dao.sjzx.RtvFracResultDao;
import com.gtja.faRtvData.model.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tools.DateTimeTools;

import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 实时估值消息发送
 */
@Slf4j
@Service
public class MessageConsumer {
    @Autowired
    private RtvFracResultDao rtvFracResultDao;

    @Autowired
    KafkaInfoModel kafkaInfoModel;
    @Value("${ssgz.kafka.servers}")
    private String BOOTSTRAP_SERVERS;

    @Autowired
    private DataSourceConfigsjzx dataSourceConfigsjzx;

    private KafkaConsumer<String, String> createConsumer() {
        Properties properties = new Properties();
        log.info("kafka bootstrap.servers：" + BOOTSTRAP_SERVERS);
        properties.put("bootstrap.servers", BOOTSTRAP_SERVERS);//  BOOTSTRAP_SERVERS "10.187.65.42:9093"
        //基础配置
        properties.put("group.id", "fa-rtv-data");
        properties.put("enable.auto.commit", "false");
        properties.put("auto.offset.reset", "earliest");
        //连接和网络配置
        properties.put("reconnect.backoff.ms", "1000");
        properties.put("reconnect.backoff.max.ms", "10000");
        properties.put("request.timeout.ms", "50000");

        //消费者行为配置
        properties.put("session.timeout.ms", "50000");//消费者心跳超时时间
        properties.put("max.poll.interval.ms", "600000");//两次poll最大间隔
        properties.put("heartbeat.interval.ms", "10000");//消费者发送心跳的频率
        properties.put("max.poll.records", "80"); //每次默认拉去50条
        properties.put("max.partition.fetch.bytes", "104857600"); //单分区默认拉去50M
        properties.put("fetch.max.bytes", "104857600"); //全局默认拉去50M

        //序列化配置
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        //安全配置
        properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        properties.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"zctgKafkaUser\" password=\"zctgKafkaUser@2025\";");
        return new KafkaConsumer<String, String>(properties);
    }
    /**
     * @demoName: kafkaConsumer
     * @Anthor: Magic
     * @Create: 2025/1/22- 10:52
     * @TODO: //TODO BOOTSTRAP_SERVERS 调整，上线前需要测试  end
     * @Param:
     * @Return:
     */
    @SneakyThrows
    public void kafkaConsumer() {
        ExecutorService executor = Executors.newFixedThreadPool(16);
        KafkaConsumer<String, String> consumer = createConsumer();
        consumer.subscribe(Arrays.asList(TopicConstans.TOPIC_RJZJJS));
        try {
            while (true) {
                ConsumerRecords<String, String> records = consumer.poll(Duration.ofMillis(1000));
                log.info("本次拉取消息数为：" + records.count());
                CountDownLatch latch = new CountDownLatch(records.count());

                for (ConsumerRecord<String, String> record : records) {
                    executor.submit(() -> {
                        try {
                            CunsumerRecord(record);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                latch.await();
                consumer.commitAsync();
                log.info("本次消费结束");
            }
        } finally {
            consumer.close();
        }
    }

    public void updateSsgzCpdm() {
        rtvFracResultDao.updateAssetCpdm();
        rtvFracResultDao.updatePointCpdm();
        rtvFracResultDao.updatePositionCpdm();
    }

    /**
     * 消息消费内容
     */
    public void CunsumerRecord(ConsumerRecord<String, String> record) {
        log.info(TopicConstans.TOPIC_RJZJJS + "消息消费中,目前时间：" + DateTimeTools.getDateTimeNow());
        String value = new String(record.value().getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8);
        // log.info("接收的消息：1offset= {},value ={}", record.offset(), value);
        try {
            log.info("当前消息转JSON对象：");
            JSONObject jsonObject = JSONObject.parseObject(value);
            log.info("当前消息转JSON对象成功");
            GtjaSsgzMqResultModel resultDetailModel = null;
            try {
                resultDetailModel = (GtjaSsgzMqResultModel) JSONObject.toJavaObject(jsonObject, GtjaSsgzMqResultModel.class);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("当前消息JSON对象转换GtjaSsgzMqResultModel失败：");
            }
            log.info("当前消息JSON对象转换GtjaSsgzMqResultModel成功：");
            GtjaSsgzMqRecLogModel logInfo = new GtjaSsgzMqRecLogModel();
            BeanUtils.copyProperties(resultDetailModel, logInfo);
            logInfo.setFv_result_hold_offset(resultDetailModel.getCONTENT().getFv_result_hold_offset());
            logInfo.setFv_result_hold_total(resultDetailModel.getCONTENT().getFv_result_hold_total());
            logInfo.setOffset(record.offset());
//            logInfo.setContent(value);
            String sourceTime = logInfo.getSEND_DATE() + "." + (logInfo.getSEND_TIME().length() < 6 ? "0" + logInfo.getSEND_TIME() : logInfo.getSEND_TIME());

            List<GtjaSsgzAssetModel> assetModels = resultDetailModel.getCONTENT().getFv_result_asset();
            List<GtjaSsgzPointModel> pointModels = resultDetailModel.getCONTENT().getFv_result_indic();
            List<GtjaSsgzPositionModel> positionModels = resultDetailModel.getCONTENT().getFv_result_hold();
            log.info("开始insertMqLog");
            try {
                rtvFracResultDao.insertMqLog(logInfo);
            } catch (Exception e) {
                log.info("消息消费日志记录：" + e.getMessage());
            }
            log.info("开始insertAsset");
            if (assetModels != null && assetModels.size() > 0) {
                rtvFracResultDao.insertAsset(assetModels, logInfo.getMSG_ID(), sourceTime);
            }
            log.info("开始insertPoint");
            if (pointModels != null && pointModels.size() > 0) {
                rtvFracResultDao.insertPoint(pointModels, logInfo.getMSG_ID(), sourceTime);
            }
            log.info("开始insertPosition");
            if (positionModels != null && positionModels.size() > 0) {
//                rtvFracResultDao.insertPosition(positionModels, logInfo.getMSG_ID(), sourceTime);
                //改为p处理添加数据
                insertBatchPosition(positionModels, logInfo.getMSG_ID(), sourceTime);
            }
            log.info("结束insertPosition");
        } catch (JSONException je) {
            je.printStackTrace();
            log.info("****JSON数据转换失败***" + je.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            log.info("****数据消费失败***" + record.value());
        }
    }

    private void insertBatchPosition(List<GtjaSsgzPositionModel> list, String msgId, String sourceTime) {
        String insertSql = "          insert into sjzx.GTJA_SSGZ_PROD_POSITION (MSG_ID, BUSI_DATE, ACCOUNT_SET_NO,\n" +
                "          POSITION_CODE, STK_ACCT, TRUSTEE_SEAT,\n" +
                "          MARKET_CODE, SCR_CODE, SCR_ID,\n" +
                "          CONTRACT_CODE, REMAIN_MARK, POSITION_DIRECTION,\n" +
                "          POSITION_TYPE, FNC_TOOL_CLASS, LISTED_CIRCULATE_SITUA,\n" +
                "          APPLY_WAY, CCY_CODE, EXCH_RATE,\n" +
                "          FAIR_PRICE_TYPE, VALU_FAIR_PRICE, S10101,\n" +
                "          S10311, S11001, S11003,\n" +
                "          S11020, S11021, S11031,\n" +
                "          S11032, S11033, S12030,\n" +
                "          S12040, S22310, S60110,\n" +
                "          S60610, S61010, S61111,\n" +
                "          S61112, S61113, S61114,\n" +
                "          S64070, S64110, S67010,\n" +
                "          S70010, S70011, S70012,\n" +
                "          S70013, S70014, S70020,\n" +
                "          S70030, S70040, S70050,\n" +
                "          S70060, S70110, S90201,\n" +
                "          S90202, S90203, S90204,\n" +
                "          S90901, S90902, S90903,\n" +
                "          S90904, S90911, S90912,\n" +
                "          S90913, S90914, S90921,\n" +
                "          S90922, S90931, S90932,\n" +
                "          S90933, S90934, S90935,\n" +
                "          S90936, S91001, S91002,\n" +
                "          S91003, S91004, S91005,\n" +
                "          DUE_DATE, CREATE_DATE, SOURCE_TIME)" +
                " values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?," +
                "        ?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        Connection connection = null;
        PreparedStatement pst = null;
        try {
            connection = dataSourceConfigsjzx.getDataSourcesjzx().getConnection();
            connection.setAutoCommit(false);
            pst = connection.prepareStatement(insertSql);
            for (GtjaSsgzPositionModel entity : list) {
                int index = 1;
                pst.setString(index++, msgId);
                pst.setInt(index++, entity.getBusi_date());
                pst.setString(index++, entity.getAccount_set_no());
                pst.setString(index++, entity.getPosition_code());
                pst.setString(index++, entity.getStk_acct());
                pst.setString(index++, entity.getTrustee_seat());
                pst.setString(index++, entity.getMarket_code());
                pst.setString(index++, entity.getScr_code());
                pst.setString(index++, entity.getScr_id());
                pst.setString(index++, entity.getContract_code());
                pst.setString(index++, entity.getRemain_mark());
                pst.setString(index++, entity.getPosition_direction());
                pst.setString(index++, entity.getPosition_type());
                pst.setString(index++, entity.getFnc_tool_class());
                pst.setString(index++, entity.getListed_circulate_situa());
                pst.setString(index++, entity.getApply_way());
                pst.setString(index++, entity.getCcy_code());
                pst.setBigDecimal(index++, entity.getExch_rate());
                pst.setString(index++, entity.getFair_price_type());
                pst.setBigDecimal(index++, entity.getValu_fair_price());
                pst.setBigDecimal(index++, entity.getS10101());
                pst.setBigDecimal(index++, entity.getS10311());
                pst.setBigDecimal(index++, entity.getS11001());
                pst.setBigDecimal(index++, entity.getS11003());
                pst.setBigDecimal(index++, entity.getS11020());
                pst.setBigDecimal(index++, entity.getS11021());
                pst.setBigDecimal(index++, entity.getS11031());
                pst.setBigDecimal(index++, entity.getS11032());
                pst.setBigDecimal(index++, entity.getS11033());
                pst.setBigDecimal(index++, entity.getS12030());
                pst.setBigDecimal(index++, entity.getS12040());
                pst.setBigDecimal(index++, entity.getS22310());
                pst.setBigDecimal(index++, entity.getS60110());
                pst.setBigDecimal(index++, entity.getS60610());
                pst.setBigDecimal(index++, entity.getS61010());
                pst.setBigDecimal(index++, entity.getS61111());
                pst.setBigDecimal(index++, entity.getS61112());
                pst.setBigDecimal(index++, entity.getS61113());
                pst.setBigDecimal(index++, entity.getS61114());
                pst.setBigDecimal(index++, entity.getS64070());
                pst.setBigDecimal(index++, entity.getS64110());
                pst.setBigDecimal(index++, entity.getS67010());
                pst.setBigDecimal(index++, entity.getS70010());
                pst.setBigDecimal(index++, entity.getS70011());
                pst.setBigDecimal(index++, entity.getS70012());
                pst.setBigDecimal(index++, entity.getS70013());
                pst.setBigDecimal(index++, entity.getS70014());
                pst.setBigDecimal(index++, entity.getS70020());
                pst.setBigDecimal(index++, entity.getS70030());
                pst.setBigDecimal(index++, entity.getS70040());
                pst.setBigDecimal(index++, entity.getS70050());
                pst.setBigDecimal(index++, entity.getS70060());
                pst.setBigDecimal(index++, entity.getS70110());
                pst.setBigDecimal(index++, entity.getS90201());
                pst.setBigDecimal(index++, entity.getS90202());
                pst.setBigDecimal(index++, entity.getS90203());
                pst.setBigDecimal(index++, entity.getS90204());
                pst.setBigDecimal(index++, entity.getS90901());
                pst.setBigDecimal(index++, entity.getS90902());
                pst.setBigDecimal(index++, entity.getS90903());
                pst.setBigDecimal(index++, entity.getS90904());
                pst.setBigDecimal(index++, entity.getS90911());
                pst.setBigDecimal(index++, entity.getS90912());
                pst.setBigDecimal(index++, entity.getS90913());
                pst.setBigDecimal(index++, entity.getS90914());
                pst.setBigDecimal(index++, entity.getS90921());
                pst.setBigDecimal(index++, entity.getS90922());
                pst.setBigDecimal(index++, entity.getS90931());
                pst.setBigDecimal(index++, entity.getS90932());
                pst.setBigDecimal(index++, entity.getS90933());
                pst.setBigDecimal(index++, entity.getS90934());
                pst.setBigDecimal(index++, entity.getS90935());
                pst.setBigDecimal(index++, entity.getS90936());
                pst.setBigDecimal(index++, entity.getS91001());
                pst.setBigDecimal(index++, entity.getS91002());
                pst.setBigDecimal(index++, entity.getS91003());
                pst.setBigDecimal(index++, entity.getS91004());
                pst.setBigDecimal(index++, entity.getS91005());
                pst.setBigDecimal(index++, entity.getDue_date());
                pst.setBigDecimal(index++, entity.getCreate_date());
                pst.setString(index++, sourceTime);
                //添加到批处理
                pst.addBatch();
            }
            //执行批处理
            pst.executeBatch();
            connection.commit();
        } catch (Exception e) {
            log.error("批处理新增sjzx.GTJA_SSGZ_PROD_POSITION失败,{}", e);
            try {
                connection.rollback();
            } catch (SQLException throwables) {
            }
        } finally {
            close(pst, connection);
        }
    }

    public void close(AutoCloseable... closeable) {
        if (closeable == null || closeable.length == 0) {
            return;
        }
        for (AutoCloseable c : closeable) {
            try {
                c.close();
            } catch (Exception e) {
            }
        }
    }

    public void test002() {

        String json = "{\"TARGET\":\"ZCTG\",\"MSG_GROUP_SNO\":1,\"MSG_ID\":\"*****************01\",\"MSG_SUBTYPE\":\"FV_RESULT\",\"MSG_TYPE\":\"FV_RESULT\",\"MSG_GROUP_ID\":\"*****************\",\"SRC\":\"146\",\"SEND_TIME\":101131,\"SRC_IP\":\"***********\",\"CONTENT\":{\"fv_result_asset\":[{\"f606101\":0.0,\"f630299\":0.0,\"accting_entity\":\"************\",\"f410431\":0.0,\"f220501\":0.0,\"f820306\":0.0,\"algo_comb\":\"*\",\"f641101\":0.0,\"f102101\":0.0,\"account_set_no\":\"\",\"f220902\":0.0,\"f400103\":0.0,\"f220901\":82.4,\"f400101\":0.0,\"f220903\":0.0,\"f400102\":0.0,\"f670101\":0.0,\"f300331\":0.0,\"exch_rate\":0.0,\"f100201\":0.0,\"f300330\":0.0,\"f223201\":0.0,\"f160101\":0.0,\"f680201\":0.0,\"f110401\":0.0,\"f200101\":41006.28,\"f102111\":0.0,\"f102112\":0.0,\"f641199\":0.0,\"f102113\":0.0,\"f100211\":0.0,\"f122101\":0.0,\"f300321\":0.0,\"f640701\":213.**************,\"f300320\":0.0,\"f120201\":0.0,\"f100212\":0.0,\"f640301\":0.0,\"f100213\":0.0,\"f100214\":0.0,\"f222101\":0.0,\"f601198\":0.0,\"f111001\":0.0,\"f410411\":0.0,\"f220601\":0.0,\"f610101\":-228237.6,\"f220201\":0.0,\"oneid\":\"************\",\"f300318\":0.0,\"f300317\":0.0,\"ccy_code\":\"CNY\",\"f300319\":0.0,\"f300314\":0.0,\"f300313\":0.0,\"f300316\":0.0,\"f300315\":0.0,\"f300310\":0.0,\"f601104\":0.0,\"f300312\":0.0,\"busi_date\":********,\"f300311\":0.0,\"f601101\":0.0,\"f601102\":0.0,\"f224199\":0.0,\"f410421\":0.0,\"f410301\":0.0,\"f110301\":4000.0,\"f410302\":0.0,\"f300307\":0.0,\"f103101\":0.0,\"f151101\":0.0,\"f300306\":0.0,\"f400199\":0.0,\"f130301\":0.0,\"f300309\":0.0,\"accting_unit\":\"*\",\"f300308\":0.0,\"f300303\":0.0,\"f300302\":0.0,\"f300305\":0.0,\"f300304\":0.0,\"f160601\":0.0,\"f300301\":-243125.2400000001,\"f660599\":0.0,\"f601199\":0.0,\"f640401\":0.0,\"asset_unit\":\"*\",\"f224101\":0.0,\"f220701\":0.0,\"f220301\":0.0,\"f400301\":0.0,\"f103111\":0.0,\"f150101\":0.0,\"f120499\":0.0,\"f660501\":0.0,\"hold_unit\":\"*\",\"f410401\":0.0,\"f410402\":0.0,\"f110201\":40666.0,\"f610198\":0.0,\"f110601\":0.0,\"f401101\":0.0,\"f210101\":0.0,\"f103121\":0.0,\"f401102\":0.0,\"f170101\":0.0,\"f120401\":0.0,\"f640501\":0.0,\"f220801\":0.0,\"f611198\":0.0,\"f611199\":0.0,\"f220401\":0.0,\"f152101\":0.0,\"f103131\":0.0,\"f400201\":0.0,\"f611107\":0.0,\"f223101\":0.0,\"f611108\":0.0,\"f611101\":0.0,\"f611102\":0.0,\"f611106\":0.0,\"f110501\":11096.4,\"f611103\":0.0,\"f611104\":0.0,\"f110901\":0.0,\"f103141\":0.0,\"f120301\":0.0,\"f120701\":0.0,\"f640601\":0.0}],\"fv_result_hold_total\":13,\"fv_result_hold\":[{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":2.6,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":2.6,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"821001.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"821001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"150080.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"150080\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":201.**************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":201.**************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-182280.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":200000.0,\"s94003\":0.0,\"s70110\":17720.0,\"s90201\":2000.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":2000.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-182280.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":200000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":5.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":5.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9114.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":886.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9114.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":1.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":1.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.68,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.68,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"102258.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"102258\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0}],\"fv_result_hold_offset\":13},\"MSG_GROUP_CNT\":1,\"SEND_DATE\":********}";
        try {
            JSONObject jsonObject1 = JSONObject.parseObject(json);
            GtjaSsgzMqResultModel resultDetailModel = (GtjaSsgzMqResultModel) JSONObject.toJavaObject(jsonObject1, GtjaSsgzMqResultModel.class);
            GtjaSsgzMqRecLogModel logInfo = new GtjaSsgzMqRecLogModel();
            BeanUtils.copyProperties(resultDetailModel, logInfo);

            String offset = resultDetailModel.getCONTENT().getFv_result_hold_offset();
            String total = resultDetailModel.getCONTENT().getFv_result_hold_total();
            String sourceTime = logInfo.getSEND_DATE() + "." + logInfo.getSEND_TIME();
            logInfo.setFv_result_hold_offset(offset);
            logInfo.setFv_result_hold_total(total);

            List<GtjaSsgzAssetModel> assetModels = resultDetailModel.getCONTENT().getFv_result_asset();
            List<GtjaSsgzPointModel> pointModels = resultDetailModel.getCONTENT().getFv_result_indic();
            List<GtjaSsgzPositionModel> positionModels = resultDetailModel.getCONTENT().getFv_result_hold();

            rtvFracResultDao.insertMqLog(logInfo);
            if (assetModels != null && assetModels.size() > 0) {
                rtvFracResultDao.insertAsset(assetModels, logInfo.getMSG_ID(), sourceTime);
            }

            if (pointModels != null && pointModels.size() > 0) {
                rtvFracResultDao.insertPoint(pointModels, logInfo.getMSG_ID(), sourceTime);
            }

            if (positionModels != null && positionModels.size() > 0) {
                rtvFracResultDao.insertPosition(positionModels, logInfo.getMSG_ID(), sourceTime);
            }
        } catch (Exception e) {
            log.info("****数据转换失败***" + json.toString());
        }
    }


}
