package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 * 概要说明：概要说明：新股锁定价格、锁定数量等信息
 */
@Data
public class RtvTgpsdDbfModel {
    @DbfAnnotation(fieldName = "L_ID", fieldLength = 10)
    private String l_id;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 9)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;
    @DbfAnnotation(fieldName = "L_SCLB", fieldLength = 4)
    private String l_sclb;

    @DbfAnnotation(fieldName = "EN_SDJG", fieldLength = 28)
    private String en_sdjg;
//    @DbfAnnotation(fieldName = "VC_CSZ", fieldLength = 4)
//    private String vc_csz;
    @DbfAnnotation(fieldName = "EN_SDSL", fieldLength = 28)
    private String en_sdsl;
    @DbfAnnotation(fieldName = "D_SDBEGIN", fieldLength = 16)
    private String d_sdbegin;
    @DbfAnnotation(fieldName = "D_SDEND", fieldLength = 16)
    private String d_sdend;


}
