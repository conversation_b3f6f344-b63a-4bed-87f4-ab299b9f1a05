package com.gtja.faRtvData.service;

import com.gtja.faRtvData.model.RtvCommonPdfModel;

import java.util.List;
import java.util.concurrent.ExecutorService;

public interface IRealTimeValueJob {

    String rtvGGFileProcessor(String ywrq, RtvCommonPdfModel param, List<RtvCommonPdfModel> cpdmModels) throws Exception;

    //ExecutorService executor,
    void rtvCpdmFileProcessor(ExecutorService executor ,String ywrq, List<RtvCommonPdfModel> fileParams, RtvCommonPdfModel cpdmModel) throws Exception;

    // void realTimeValueProcessor1(RtvCommonPdfModel param, List<RtvCommonPdfModel> cpdmModels) throws Exception;

    void insertLog(RtvCommonPdfModel param, String type, String successOrFail);

    // @Transactional(value = "fbzxTransactionManager", propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    // void rtvCpdmFileProcessor(String ywrq, List<RtvCommonPdfModel> fileModels, RtvCommonPdfModel cpdmModel) throws Exception;
}
