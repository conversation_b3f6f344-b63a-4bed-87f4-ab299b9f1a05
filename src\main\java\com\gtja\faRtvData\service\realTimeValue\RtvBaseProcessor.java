package com.gtja.faRtvData.service.realTimeValue;
/**
 * RtvCpdmProcessor  实时估值  dbf生成  根据产品代码导出的公共方法
 */

import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.dao.fbzx.RealTimeValueDao;
import com.gtja.faRtvData.dao.hsfa.RealTimeValueHsfaDao;
import com.gtja.faRtvData.model.*;
import com.gtja.faRtvData.service.AbstractRtvBaseProcessor;
import com.spire.ms.System.Collections.ArrayList;
import fileTools.FileTools;
import fileTools.dbf.DbfTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Component("RtvBaseProcessor")
@Slf4j
public class RtvBaseProcessor extends AbstractRtvBaseProcessor {

    @Autowired
    private RealTimeValueDao realTimeValueDao;

    @Autowired
    private RealTimeValueHsfaDao realTimeValueHsfaDao;

    @Override
    protected List<T> queryRtvDataList(String fileName) throws Exception {
        List data = new ArrayList();
        if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TQS)) {
            data = realTimeValueDao.queryRtvTqsList();
        } else if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TXTCS)) {
            data = realTimeValueDao.queryRtvTxtcsCommonList();
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TJK_ZZQS_TGPSDQ_LDXZK)) {
            data = realTimeValueHsfaDao.queryRtvTjkZzqsTgpsdqLdxzkList();
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TQYXX)) {
            data = realTimeValueHsfaDao.queryRtvTqyxxList();
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TZQXX)) {
            data = realTimeValueHsfaDao.queryRtvTzqxxList();
        }
        return data;
    }

    protected List<T> queryRtvDataList(String fileName, String cpdm, String ywrq) throws Exception {
        List data = new ArrayList();
       /* if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TQS)) {
            data = realTimeValueDao.queryRtvTqsList();
        } else if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TXTCS)) {
            data = realTimeValueDao.queryRtvTxtcsCommonList();
        } else */
        if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_BALANCE)) {
            data = realTimeValueHsfaDao.queryRtvBalanceList(cpdm, ywrq);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TACCOUNT)) {
            data = realTimeValueDao.queryRtvTaccountList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGDXW)) {
            data = realTimeValueDao.queryRtvTgdxwList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGPSD)) {
            data = realTimeValueDao.queryRtvTgpsdList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TFUNDINFO)) {
            data = realTimeValueDao.queryRtvTfundinfoList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_THKZH)) {
            data = realTimeValueDao.queryRtvThkzhList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TLL)) {
            data = realTimeValueDao.queryRtvTllList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_GTJA_ACCOUNT)) {
            data = realTimeValueDao.queryRtvGtjaAccountList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TJJXSFL)) {
            data = realTimeValueDao.queryRtvTjjxsflList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TYTTX)) {
            data = realTimeValueDao.queryRtvTyttxList(cpdm);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TXTCS)) {
            data = realTimeValueDao.queryRtvTxtcsList(cpdm);
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGPSD_DSFGZ)) {
            data = realTimeValueHsfaDao.queryRtvTgpsdDsfgzList(cpdm);
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_GZB)) {
            data = realTimeValueHsfaDao.queryRtvGzbList(cpdm, ywrq);
        }
        return data;
    }


    //@Override
    @Override
    public void pdfCreateCommonMothod(String fileName, String absolutePath, List<T> data, Class tclass) throws IOException {
        FileTools.createFileIfNotExists(absolutePath);
        if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TQS)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTqsDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.COMMON_TXTCS)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTxtcsDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_BALANCE)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvBalanceDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TACCOUNT)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTaccountDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGDXW)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTgdxwDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGPSD)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTgpsdDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TFUNDINFO)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTfundInfoDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_THKZH)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvThkzhDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TLL)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTllDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_GTJA_ACCOUNT)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvGtjaAccountDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TJJXSFL)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTjjxsflDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TYTTX)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTyttxDbfModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TXTCS)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTxtcsDbfModel.class);
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TJK_ZZQS_TGPSDQ_LDXZK)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTjkZzqsTgpsdqLdxzkModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TGPSD_DSFGZ)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTgpsdDsfgzModel.class);
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TQYXX)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTqyxxModel.class);
        } else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_TZQXX)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvTzqxxModel.class);
        }else if (fileName.equals(Constants.DBF_FILE_NAME.CPDM_GZB)) {
            DbfTools.writeToDbfUtf8New(absolutePath, data, RtvGzbModel.class);
        }
    }

}
