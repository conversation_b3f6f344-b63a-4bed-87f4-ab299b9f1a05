package com.gtja.faRtvData.xxlHandler.rtvHandler;

import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.common.utils.SplitListForEachUtil;
import com.gtja.faRtvData.dao.fbzx.RealTimeValueDao;
import com.gtja.faRtvData.dao.hsfa.ICommonDao;
import com.gtja.faRtvData.model.RtvCommonPdfModel;
import com.gtja.faRtvData.service.IRealTimeValueJob;
import com.gtja.faRtvData.xxlHandler.BaseJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import fileTools.FileTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import tools.DateTimeTools;
import tools.RegularTools;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @FileName: RtvBaseJobHandler.java
 * @anthor: Magic
 * @create: 2025/1/2- 17:39
 * @TODO: 实时估值文件生产//TODO end
 * @Version：1.0
 */
@Slf4j
@Component
public class RtvBaseJobHandler extends BaseJobHandler {
    @Qualifier("RtvBaseProcessor")
    @Autowired
    private IRealTimeValueJob realTimeValueJob;
    @Autowired
    private RealTimeValueDao realTimeValueDao;

    @Value("${baseFilePath.nfsTargetPath}")
    private String RTV_BASE_PATH;

    @Autowired
    private ICommonDao iCommonDao;

    /**
     * @demoName: RtvBaseFileCreateJobHandler
     * @Anthor: Magic
     * @Create: 2025/1/2- 17:39
     * @TODO: //TODO 判断当前时间 8：30前为前一工作日数据；8：30以后为当天数据
     * 直接将文件生成到 nfsTargetPath 目录，并记录日志
     * 文件生成：
     * 1.根据文件清单获取需要生成的的文件（基础和公共文件）；
     * 2.根据产品清单，每个产品生成所有文件清单中的所有文件；
     * 3.根据调度时间日期，生成不同日期的数据；默认：8:30前为T-1，之后为T日数据；制成生成制定日期数据；
     * 4.所有文件生成完成后生成 .Ok文件；
     * 5.二次生产如果估值确认时间大于日志表记录则生成新批次数据；
     * @Param:
     * @Return:
     */
    @XxlJob("RtvBaseFileCreateJobHandler")
    public ReturnT<String> RtvBaseFileCreateJobHandler(String param) throws InterruptedException {
        ReturnT result = new ReturnT("实时估值-dbf基础文件-创建-到目标目录)");
        //1.线程池
        ExecutorService executor = Executors.newFixedThreadPool(10);
        /*   ************************  第一部分：业务数据处理  ***********************************/
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());
        if (!DateTimeTools.getNowHours("07:00")) {
            ywrq = iCommonDao.getTradeday(ywrq, "-1", "1");//7点前 取T-1
        } else {
            ywrq = iCommonDao.getTradeday(ywrq, "-1", "0");//7点后 取T
        }
        //2.更新产品清单的最细估值日期和时间
        realTimeValueDao.updateRtvCpqdGzrq();
        //1.校验 1.1查询 所有的代办文件
        List<RtvCommonPdfModel> fileList = realTimeValueDao.queryRtvFileConfig(Constants.DBF_FILE_TYPE.CPDM);
        //1.查询 所有的产品代码
        List<String> cpdms = realTimeValueDao.queryRealTimeValueCpConfig1();
        log.info("产品代办清单数量{}", cpdms.size());

        /*   ************************  第二部分：代码设计：线程池处理  ***********************************/
        String finalYwrq = ywrq;
        cpdms.stream().forEach(cpdm -> {
            //1.查询产品代码 对应的需要生成的文件
            RtvCommonPdfModel cpdmModel = realTimeValueDao.queryRealTimeValueCpqd(finalYwrq, cpdm);
            //2.代办文件根据 文件创建的速度分类 ，分类处理 先处理速度快的文件
            try {
                if (null == cpdmModel || StringUtils.isEmpty(cpdmModel.getCpdm())) {
                    return;
                }
                log.info("开始处理产品：{}", cpdm);
                realTimeValueJob.rtvCpdmFileProcessor(executor,finalYwrq, fileList, cpdmModel);
            } catch (Exception e) {
                log.info("产品：{}处理异常{}", cpdm, e.getMessage());
            }
        });
        result.setMsg("ywrq" + ywrq + "产品待办执行完成");
        return result;
    }


    /**
     * @demoName: RtvBaseFileCreateJobHandler
     * @Anthor: Magic
     * @Create: 2025/1/2- 17:39
     * @TODO: //TODO 判断当前时间 8：30前为前一工作日数据；8：30以后为当天数据
     * 直接将文件生成到 nfsTargetPath 目录，并记录日志
     * 文件生成：
     * 1.根据文件清单获取需要生成的的文件（公共文件）；
     * 2.根据产品清单，每个产品生成所有文件清单中的所有文件；
     * 3.根据调度时间日期，生成不同日期的数据；默认：8:30前为T-1，之后为T日数据；制成生成制定日期数据；
     * 4.所有文件生成完成后生成 .Ok文件；
     * 5.二次生产如果估值确认时间大于日志表记录则生成新批次数据；
     * @Param:
     * @Return:
     */
    @XxlJob("RtvGGFileCreateJobHandler")
    public ReturnT<String> RtvGGFileCreateJobHandler(String param) throws InterruptedException {
        ReturnT result = new ReturnT("实时估值-dbf基础文件-创建-到目标目录)");
        //1.线程池
        ExecutorService executor = Executors.newFixedThreadPool(5);
        /*   ************************  第一部分：业务数据处理  ***********************************/
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());
        if (!DateTimeTools.getNowHours("07:00")) {
            ywrq = iCommonDao.getTradeday(ywrq, "-1", "1");//7点前 取T-1
        } else {
            ywrq = iCommonDao.getTradeday(ywrq, "-1", "0");//7点后 取T
        }

        //校验  1.2.查询所有代办产品（日志中不存在的和估值重新确认的） 如果文件或产品代办为空 return
        List<RtvCommonPdfModel> cpdmModels = realTimeValueDao.quertNotCreateGGFile(ywrq);
        log.info("产品代办清单数量{}", cpdmModels.size());
        if (cpdmModels.size() == 0) {
            result.setMsg("ywrq" + ywrq + "产品待办为空，无需执行");
            return result;
        }

        CountDownLatch unHandleLatch = new CountDownLatch(cpdmModels.size());
        String finalYwrq = ywrq;
        for (RtvCommonPdfModel fileModel : cpdmModels) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        log.info("开始处理文件模板：{}", fileModel.getRtvDbfName());
                        realTimeValueJob.rtvGGFileProcessor(finalYwrq, fileModel, cpdmModels);
                    } catch (Exception e) {
                        log.info("文件模板：{}处理异常{}", fileModel.getRtvDbfName(), e.getMessage());
                    } finally {
                        unHandleLatch.countDown();
                    }
                }
            });
        }
        unHandleLatch.await();
        // 1.3 公共文件 不重复生成
        log.info("3.开始生成。ok文件");
        okGGFileCreateForCpdm(ywrq);
        result.setMsg("ywrq" + ywrq + "产品待办执行完成");
        return result;
    }

    public void okGGFileCreateForCpdm(String ywrq) {
        //3.OK文件的处理  查询所有的CPDM已生成数据
        //为了防止第二次以后的。ok文件多次生成，取生成的fileList 作为入参，取生成产品对应批次的数据 判断是否需要生成。ok文件
        //默认添加一个公共文件的 。ok文件，数据库中没有GG 文件
        RtvCommonPdfModel ggmodel = new RtvCommonPdfModel();
        ggmodel.setYwrq(ywrq);
        ggmodel.setCpdm("GG");
        ggmodel.setBatchNo("1");

        List<RtvCommonPdfModel> okModelList = realTimeValueDao.queryRtvFileOk(Arrays.asList(ggmodel));
        okModelList.stream().forEach(okModel -> {
            String id = UUID.randomUUID().toString();
            okModel.setId(id);
            okModel.setYwrq(ywrq);
            try {
                String createPath = RTV_BASE_PATH.replace("YYYYMMDD", ywrq);
                okModel.setRtvDbfPath(createPath);
                okModel.setBatchNo("1");
                realTimeValueJob.insertLog(okModel, okModel.getCpdm(), "0");
                FileTools.createFileIfNotExists((createPath + okModel.getRtvDbfName()));
            } catch (IOException e) {
                // throw new RuntimeException(e);
                log.info("实时估值.OK文件创建失败：" + e.toString());
                realTimeValueJob.insertLog(okModel, okModel.getCpdm(), "1");
            }
        });
    }
}
