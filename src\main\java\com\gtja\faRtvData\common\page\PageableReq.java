package com.gtja.faRtvData.common.page;


import com.gtja.faRtvData.common.utils.JsonUtil;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2020/10/3020:56
 * @Version :1.0
 */
public class PageableReq implements Serializable {

    private static final long serialVersionUID = -8703227463948050533L;
    protected Integer pageNo = 1;
    protected Integer pageSize = 10;
    protected List<String> orderByCol;
    protected List<String> orderByMode;

    public PageableReq() {
    }

    public String toString() {
        return JsonUtil.toJson(this);
    }

    public void setPageNo(final Integer pageNo) {
        this.pageNo = pageNo;
    }

    public void setPageSize(final Integer pageSize) {
        this.pageSize = pageSize;
    }

    public void setOrderByCol(final List<String> orderByCol) {
        this.orderByCol = orderByCol;
    }

    public void setOrderByMode(final List<String> orderByMode) {
        this.orderByMode = orderByMode;
    }

    public Integer getPageNo() {
        return this.pageNo;
    }

    public Integer getPageSize() {
        return this.pageSize;
    }

    public List<String> getOrderByCol() {
        return this.orderByCol;
    }

    public List<String> getOrderByMode() {
        return this.orderByMode;
    }
}

