#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffd96513ef7, pid=22268, tid=13976
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.8+9 (21.0.8+9) (build 21.0.8+9-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.8+9 (21.0.8+9-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x1e3ef7]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   https://github.com/adoptium/adoptium-support/issues
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\workspaceStorage\a4a2337d6edc7af7211706e891d68e53\redhat.java -Daether.dependencyCollector.impl=bf d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\globalStorage\redhat.java\1.44.0\config_win -data d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\workspaceStorage\a4a2337d6edc7af7211706e891d68e53\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-ee386846efe6b792f3c8b1edf9d3fd1d-sock

Host: ZHAOXIN KaiXian KX-U6780@2.7GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Aug 13 13:56:01 2025 elapsed time: 966.559685 seconds (0d 0h 16m 6s)

---------------  T H R E A D  ---------------

Current thread (0x00000215545f8660):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=13976, stack(0x0000007596300000,0x0000007596400000) (1024K)]


Current CompileTask:
C2:966559 15972   !   4       org.eclipse.jdt.internal.compiler.lookup.Scope::getBinding (1562 bytes)

Stack: [0x0000007596300000,0x0000007596400000],  sp=0x00000075963fcd20,  free space=1011k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x1e3ef7]
V  [jvm.dll+0x38528a]
V  [jvm.dll+0x38477a]
V  [jvm.dll+0x248ed0]
V  [jvm.dll+0x2484af]
V  [jvm.dll+0x1c89ee]
V  [jvm.dll+0x257d4d]
V  [jvm.dll+0x2562ea]
V  [jvm.dll+0x3f2d16]
V  [jvm.dll+0x857e6b]
V  [jvm.dll+0x6d0b0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


siginfo: EXCEPTION_ACCESS_VIOLATION (0xc0000005), reading address 0x0000000000000000


Registers:
RAX=0x000002155f01efc8, RBX=0x0000000000000000, RCX=0x0000000000000151, RDX=0x0000000000000151
RSP=0x00000075963fcd20, RBP=0x000000000000080b, RSI=0x0000000000000004, RDI=0x0000021564102cc8
R8 =0xffffffffffffffff, R9 =0x000000000000000a, R10=0x0000000000000000, R11=0x0000000000000246
R12=0x0000000000000009, R13=0x00000075963fce50, R14=0x0000000000000020, R15=0x00000000000000ef
RIP=0x00007ffd96513ef7, EFLAGS=0x0000000000010283

XMM[0]=0x0000000000000000 0x0000000000000000
XMM[1]=0x0000000000000000 0x0000000000000000
XMM[2]=0x0000000000000000 0x0000000000000000
XMM[3]=0x0000000000000000 0x0000000000000000
XMM[4]=0x0000000000000000 0x0000000000000000
XMM[5]=0x0000000000000000 0x0000000000000000
XMM[6]=0x0000000000000000 0x0000000000000000
XMM[7]=0x0000000000000000 0x0000000000000000
XMM[8]=0x0000000000000000 0x0000000000000000
XMM[9]=0x0000000000000000 0x0000000000000000
XMM[10]=0x0000000000000000 0x0000000000000000
XMM[11]=0x0000000000000000 0x0000000000000000
XMM[12]=0x0000000000000000 0x0000000000000000
XMM[13]=0x0000000000000000 0x0000000000000000
XMM[14]=0x0000000000000000 0x0000000000000000
XMM[15]=0x0000000000000000 0x0000000000000000
  MXCSR=0x00001fa0


Register to memory mapping:

RAX=0x000002155f01efc8 points into unknown readable memory: 0x0000021555ad5c68 | 68 5c ad 55 15 02 00 00
RBX=0x0 is null
RCX=0x0000000000000151 is an unknown value
RDX=0x0000000000000151 is an unknown value
RSP=0x00000075963fcd20 is pointing into the stack for thread: 0x00000215545f8660
RBP=0x000000000000080b is an unknown value
RSI=0x0000000000000004 is an unknown value
RDI=0x0000021564102cc8 points into unknown readable memory: 0x00007ffd96d20a58 | 58 0a d2 96 fd 7f 00 00
R8 =0xffffffffffffffff is an unknown value
R9 =0x000000000000000a is an unknown value
R10=0x0 is null
R11=0x0000000000000246 is an unknown value
R12=0x0000000000000009 is an unknown value
R13=0x00000075963fce50 is pointing into the stack for thread: 0x00000215545f8660
R14=0x0000000000000020 is an unknown value
R15=0x00000000000000ef is an unknown value

Top of Stack: (sp=0x00000075963fcd20)
0x00000075963fcd20:   0000021554e92570 0000000000000007
0x00000075963fcd30:   00000075963fcdb8 00007ffd966e743a
0x00000075963fcd40:   0000000000000000 000002155478cac0
0x00000075963fcd50:   000002155478cab0 000002155478ce98
0x00000075963fcd60:   00000075963fce70 00007ffd966b528a
0x00000075963fcd70:   00000000ffffff01 00000000000003d8
0x00000075963fcd80:   00000075963fd230 0000021555ad0b38
0x00000075963fcd90:   000000007fffff01 0000000000000000
0x00000075963fcda0:   0000000000000000 0000000000000000
0x00000075963fcdb0:   0000000000000000 0000000000000007
0x00000075963fcdc0:   000002155478cab0 000002155478cab0
0x00000075963fcdd0:   000002155478cac0 000002155478ce98
0x00000075963fcde0:   00000000000003d8 0000021556aa3308
0x00000075963fcdf0:   0000021556aa53e8 0000021556aa3318
0x00000075963fce00:   0000021556d116e0 0000000000000040
0x00000075963fce10:   0000021556aa3218 00007ffd00000040
0x00000075963fce20:   0000021556d116e0 00000215642f48c0
0x00000075963fce30:   0000000000069e40 0000000000000000
0x00000075963fce40:   0000000000000000 0000000000000000
0x00000075963fce50:   00007ffd96d35180 0000000000000009
0x00000075963fce60:   00000075963fe7b0 0000000000000000
0x00000075963fce70:   0000000000000000 0000000000000002
0x00000075963fce80:   0000021564953ed0 00007ffd00000002
0x00000075963fce90:   0000021556d116e0 00000075963fd230
0x00000075963fcea0:   00000000deadbeef 00000075963fd440
0x00000075963fceb0:   0000000000007fd8 0000000000000000
0x00000075963fcec0:   00007ffd96f5f150 00007ffd00000002
0x00000075963fced0:   0000021564953ed8 00007ffd00000002
0x00000075963fcee0:   0000021556d116e0 0000000000000002
0x00000075963fcef0:   0000021564953ee0 00007ffd00000002
0x00000075963fcf00:   0000021556d116e0 0000000000000000
0x00000075963fcf10:   0000000000000000 000002150000080c 

Instructions: (pc=0x00007ffd96513ef7)
0x00007ffd96513df7:   00 00 00 48 c1 ef 06 80 e1 3f 83 e7 03 48 d3 e2
0x00007ffd96513e07:   49 8b 0c f8 48 8b c1 48 0b c2 49 89 04 f8 48 8b
0x00007ffd96513e17:   7c 24 30 48 23 ca 75 02 ff 03 48 85 c9 0f 94 c0
0x00007ffd96513e27:   48 83 c4 20 5b c3 cc cc cc 44 8b d2 41 3b d0 7c
0x00007ffd96513e37:   49 41 8d 80 c0 02 00 00 3b d0 7d 3e 44 8b ca 45
0x00007ffd96513e47:   2b c8 41 8b c1 83 e0 3f 0f b6 d0 41 8b c1 48 c1
0x00007ffd96513e57:   e8 06 48 8b 44 c1 38 48 0f a3 d0 73 1d 0f b7 81
0x00007ffd96513e67:   9c 00 00 00 83 f8 01 74 0e 8d 48 ff 8b c1 41 23
0x00007ffd96513e77:   c2 3b c1 0f 94 c0 c3 b0 01 c3 32 c0 c3 cc cc cc
0x00007ffd96513e87:   cc cc cc cc cc cc cc cc cc 40 55 41 55 41 57 48
0x00007ffd96513e97:   83 ec 30 48 8b 41 48 33 ed 45 33 ff 4c 8b e9 39
0x00007ffd96513ea7:   68 40 0f 86 c9 00 00 00 48 89 5c 24 50 48 89 74
0x00007ffd96513eb7:   24 58 48 89 7c 24 60 4c 89 64 24 28 4c 89 74 24
0x00007ffd96513ec7:   20 0f 1f 84 00 00 00 00 00 48 8b 40 30 33 f6 4a
0x00007ffd96513ed7:   8b 3c f8 44 8b 67 28 45 85 e4 74 6b 45 33 f6 3b
0x00007ffd96513ee7:   77 18 73 0a 48 8b 47 20 49 8b 1c 06 eb 02 33 db
0x00007ffd96513ef7:   48 8b 03 48 8b cb ff 90 80 00 00 00 45 33 c0 44
0x00007ffd96513f07:   8b 48 5c 8b 48 58 41 3b c9 77 17 8b d1 ff c1 4c
0x00007ffd96513f17:   0b 04 d0 41 3b c9 76 f3 4d 85 c0 74 05 8b 53 28
0x00007ffd96513f27:   eb 02 33 d2 49 8b 85 08 01 00 00 48 63 4b 28 89
0x00007ffd96513f37:   14 88 8b 43 28 3b c5 0f 46 c5 ff c6 49 83 c6 08
0x00007ffd96513f47:   8b e8 41 3b f4 72 98 49 8b 45 48 41 ff c7 44 3b
0x00007ffd96513f57:   78 40 0f 82 71 ff ff ff 4c 8b 74 24 20 4c 8b 64
0x00007ffd96513f67:   24 28 48 8b 7c 24 60 48 8b 74 24 58 48 8b 5c 24
0x00007ffd96513f77:   50 8d 55 01 49 8d 8d e0 00 00 00 89 11 48 83 c4
0x00007ffd96513f87:   30 41 5f 41 5d 5d e9 fe 00 00 00 cc cc cc cc cc
0x00007ffd96513f97:   cc cc cc cc cc cc cc cc cc 48 89 5c 24 08 57 48
0x00007ffd96513fa7:   83 ec 20 8b 52 28 48 8b d9 49 63 f8 48 81 c1 e0
0x00007ffd96513fb7:   00 00 00 44 8b c7 e8 1e ec ff ff 48 81 c3 e8 00
0x00007ffd96513fc7:   00 00 3b 3b 7c 4b 3b 7b 04 7c 0a 8b d7 48 8b cb
0x00007ffd96513fd7:   e8 f4 f0 ee ff 48 63 13 3b d7 7d 30 8b c7 48 8d
0x00007ffd96513fe7:   0c 95 00 00 00 00 2b c2 8b d0 45 33 c0 0f 1f 40 


Stack slot to memory mapping:

stack at sp + 0 slots: 0x0000021554e92570 points into unknown readable memory: 0x00007ffd96d1c110 | 10 c1 d1 96 fd 7f 00 00
stack at sp + 1 slots: 0x0000000000000007 is an unknown value
stack at sp + 2 slots: 0x00000075963fcdb8 is pointing into the stack for thread: 0x00000215545f8660
stack at sp + 3 slots: 0x00007ffd966e743a jvm.dll
stack at sp + 4 slots: 0x0 is null
stack at sp + 5 slots: 0x000002155478cac0 points into unknown readable memory: 0x00000075951ff468 | 68 f4 1f 95 75 00 00 00
stack at sp + 6 slots: 0x000002155478cab0 points into unknown readable memory: 0x0000000000000000 | 00 00 00 00 00 00 00 00
stack at sp + 7 slots: 0x000002155478ce98 points into unknown readable memory: 0x8800030048ce3f8a | 8a 3f ce 48 00 03 00 88


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000215623f94e0, length=48, elements={
0x0000021574024a10, 0x000002157ba7e0e0, 0x000002157ba80750, 0x000002157ba81460,
0x000002157ba82170, 0x000002157ba83d30, 0x000002157ba84780, 0x000002157ba855b0,
0x000002157ba860f0, 0x000002157d417cb0, 0x000002157d6ae110, 0x000002157da984b0,
0x000002157e1b9220, 0x000002157df4d080, 0x000002157df516f0, 0x000002157e2c3910,
0x000002157e4acc40, 0x000002157d80b460, 0x000002157d809390, 0x000002157d809a20,
0x000002157d80a0b0, 0x000002157d808670, 0x000002157d80add0, 0x0000021554b50830,
0x0000021554b4cd20, 0x0000021554b4e760, 0x0000021554b4fb10, 0x0000021554b4f480,
0x0000021554b4e0d0, 0x0000021554b4da40, 0x0000021554b4c000, 0x0000021554b50ec0,
0x0000021554b501a0, 0x0000021554b4d3b0, 0x0000021554b51550, 0x000002157d807fe0,
0x000002157d80a740, 0x0000021554b53620, 0x0000021554b52f90, 0x0000021554b52270,
0x00000215545f8660, 0x000002155feac170, 0x000002155feac800, 0x000002155feadbb0,
0x000002155feae240, 0x000002155feafc80, 0x000002155feb09a0, 0x00000215545f5d80
}

Java Threads: ( => current thread )
  0x0000021574024a10 JavaThread "main"                              [_thread_blocked, id=7088, stack(0x0000007594600000,0x0000007594700000) (1024K)]
  0x000002157ba7e0e0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19788, stack(0x0000007594a00000,0x0000007594b00000) (1024K)]
  0x000002157ba80750 JavaThread "Finalizer"                  daemon [_thread_blocked, id=12932, stack(0x0000007594b00000,0x0000007594c00000) (1024K)]
  0x000002157ba81460 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=16372, stack(0x0000007594c00000,0x0000007594d00000) (1024K)]
  0x000002157ba82170 JavaThread "Attach Listener"            daemon [_thread_blocked, id=5424, stack(0x0000007594d00000,0x0000007594e00000) (1024K)]
  0x000002157ba83d30 JavaThread "Service Thread"             daemon [_thread_blocked, id=18152, stack(0x0000007594e00000,0x0000007594f00000) (1024K)]
  0x000002157ba84780 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=11252, stack(0x0000007594f00000,0x0000007595000000) (1024K)]
  0x000002157ba855b0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=6528, stack(0x0000007595000000,0x0000007595100000) (1024K)]
  0x000002157ba860f0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12636, stack(0x0000007595100000,0x0000007595200000) (1024K)]
  0x000002157d417cb0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=2816, stack(0x0000007595200000,0x0000007595300000) (1024K)]
  0x000002157d6ae110 JavaThread "Notification Thread"        daemon [_thread_blocked, id=22516, stack(0x0000007595300000,0x0000007595400000) (1024K)]
  0x000002157da984b0 JavaThread "Active Thread: Equinox Container: db9a7bdd-2bfc-4fb3-9117-41623cecbc02"        [_thread_blocked, id=16952, stack(0x0000007595c00000,0x0000007595d00000) (1024K)]
  0x000002157e1b9220 JavaThread "Refresh Thread: Equinox Container: db9a7bdd-2bfc-4fb3-9117-41623cecbc02" daemon [_thread_blocked, id=13636, stack(0x0000007595500000,0x0000007595600000) (1024K)]
  0x000002157df4d080 JavaThread "Framework Event Dispatcher: Equinox Container: db9a7bdd-2bfc-4fb3-9117-41623cecbc02" daemon [_thread_blocked, id=1344, stack(0x0000007595d00000,0x0000007595e00000) (1024K)]
  0x000002157df516f0 JavaThread "Start Level: Equinox Container: db9a7bdd-2bfc-4fb3-9117-41623cecbc02" daemon [_thread_blocked, id=21000, stack(0x0000007595e00000,0x0000007595f00000) (1024K)]
  0x000002157e2c3910 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=19864, stack(0x0000007596000000,0x0000007596100000) (1024K)]
  0x000002157e4acc40 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=11088, stack(0x0000007595f00000,0x0000007596000000) (1024K)]
  0x000002157d80b460 JavaThread "Worker-JM"                         [_thread_blocked, id=7676, stack(0x0000007596400000,0x0000007596500000) (1024K)]
  0x000002157d809390 JavaThread "Worker-0"                          [_thread_blocked, id=11940, stack(0x0000007594400000,0x0000007594500000) (1024K)]
  0x000002157d809a20 JavaThread "Java indexing"              daemon [_thread_blocked, id=17328, stack(0x0000007596500000,0x0000007596600000) (1024K)]
  0x000002157d80a0b0 JavaThread "Worker-2: Initialize Workspace"        [_thread_blocked, id=22508, stack(0x0000007596600000,0x0000007596700000) (1024K)]
  0x000002157d808670 JavaThread "Worker-3"                          [_thread_blocked, id=1780, stack(0x0000007596700000,0x0000007596800000) (1024K)]
  0x000002157d80add0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=20436, stack(0x0000007596900000,0x0000007596a00000) (1024K)]
  0x0000021554b50830 JavaThread "Thread-2"                   daemon [_thread_in_native, id=22428, stack(0x0000007596f00000,0x0000007597000000) (1024K)]
  0x0000021554b4cd20 JavaThread "Thread-3"                   daemon [_thread_in_native, id=5420, stack(0x0000007597000000,0x0000007597100000) (1024K)]
  0x0000021554b4e760 JavaThread "Thread-4"                   daemon [_thread_in_native, id=4208, stack(0x0000007597100000,0x0000007597200000) (1024K)]
  0x0000021554b4fb10 JavaThread "Thread-5"                   daemon [_thread_in_native, id=8400, stack(0x0000007597200000,0x0000007597300000) (1024K)]
  0x0000021554b4f480 JavaThread "Thread-6"                   daemon [_thread_in_native, id=10336, stack(0x0000007597300000,0x0000007597400000) (1024K)]
  0x0000021554b4e0d0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=15088, stack(0x0000007597400000,0x0000007597500000) (1024K)]
  0x0000021554b4da40 JavaThread "Thread-8"                   daemon [_thread_in_native, id=1684, stack(0x0000007597500000,0x0000007597600000) (1024K)]
  0x0000021554b4c000 JavaThread "Thread-9"                   daemon [_thread_in_native, id=20576, stack(0x0000007597600000,0x0000007597700000) (1024K)]
  0x0000021554b50ec0 JavaThread "Thread-10"                  daemon [_thread_in_native, id=8004, stack(0x0000007597700000,0x0000007597800000) (1024K)]
  0x0000021554b501a0 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=17964, stack(0x0000007597800000,0x0000007597900000) (1024K)]
  0x0000021554b4d3b0 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=14548, stack(0x0000007597a00000,0x0000007597b00000) (1024K)]
  0x0000021554b51550 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=6696, stack(0x0000007597b00000,0x0000007597c00000) (1024K)]
  0x000002157d807fe0 JavaThread "Worker-6"                          [_thread_blocked, id=16388, stack(0x0000007596a00000,0x0000007596b00000) (1024K)]
  0x000002157d80a740 JavaThread "Worker-7: Initialize workspace"        [_thread_blocked, id=19636, stack(0x0000007596b00000,0x0000007596c00000) (1024K)]
  0x0000021554b53620 JavaThread "Worker-8: Publish Diagnostics"        [_thread_in_Java, id=14444, stack(0x0000007594500000,0x0000007594600000) (1024K)]
  0x0000021554b52f90 JavaThread "Worker-9"                          [_thread_blocked, id=10712, stack(0x0000007596100000,0x0000007596200000) (1024K)]
  0x0000021554b52270 JavaThread "ForkJoinPool.commonPool-worker-1" daemon [_thread_in_vm, id=17332, stack(0x0000007596200000,0x0000007596300000) (1024K)]
=>0x00000215545f8660 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=13976, stack(0x0000007596300000,0x0000007596400000) (1024K)]
  0x000002155feac170 JavaThread "ForkJoinPool.commonPool-worker-2" daemon [_thread_blocked, id=10224, stack(0x0000007596c00000,0x0000007596d00000) (1024K)]
  0x000002155feac800 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=8528, stack(0x0000007596d00000,0x0000007596e00000) (1024K)]
  0x000002155feadbb0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=3588, stack(0x0000007596e00000,0x0000007596f00000) (1024K)]
  0x000002155feae240 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=10432, stack(0x0000007597c00000,0x0000007597d00000) (1024K)]
  0x000002155feafc80 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=19312, stack(0x0000007597d00000,0x0000007597e00000) (1024K)]
  0x000002155feb09a0 JavaThread "ForkJoinPool.commonPool-worker-7" daemon [_thread_blocked, id=14912, stack(0x0000007597e00000,0x0000007597f00000) (1024K)]
  0x00000215545f5d80 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=11844, stack(0x0000007596800000,0x0000007596900000) (1024K)]
Total: 48

Other Threads:
  0x0000021571e5ec30 VMThread "VM Thread"                           [id=14432, stack(0x0000007594900000,0x0000007594a00000) (1024K)]
  0x000002157408f930 WatcherThread "VM Periodic Task Thread"        [id=6844, stack(0x0000007594800000,0x0000007594900000) (1024K)]
  0x0000021574041c20 WorkerThread "GC Thread#0"                     [id=8256, stack(0x0000007594700000,0x0000007594800000) (1024K)]
  0x000002157dbdcfd0 WorkerThread "GC Thread#1"                     [id=6348, stack(0x0000007595600000,0x0000007595700000) (1024K)]
  0x000002157dbdd370 WorkerThread "GC Thread#2"                     [id=12204, stack(0x0000007595700000,0x0000007595800000) (1024K)]
  0x000002157dbd7f00 WorkerThread "GC Thread#3"                     [id=7128, stack(0x0000007595800000,0x0000007595900000) (1024K)]
  0x000002157dbd82a0 WorkerThread "GC Thread#4"                     [id=7208, stack(0x0000007595900000,0x0000007595a00000) (1024K)]
  0x000002157dbd8640 WorkerThread "GC Thread#5"                     [id=18480, stack(0x0000007595a00000,0x0000007595b00000) (1024K)]
  0x000002157df5e0f0 WorkerThread "GC Thread#6"                     [id=19732, stack(0x0000007595b00000,0x0000007595c00000) (1024K)]
  0x000002157e235cd0 WorkerThread "GC Thread#7"                     [id=5976, stack(0x0000007595400000,0x0000007595500000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  966654 15974       4       lombok.eclipse.EclipseNode::findAnnotation (18 bytes)
C2 CompilerThread1  966654 15972   !   4       org.eclipse.jdt.internal.compiler.lookup.Scope::getBinding (1562 bytes)
C2 CompilerThread2  966654 15890       4       org.eclipse.jdt.internal.compiler.lookup.AnnotatableTypeSystem::getParameterizedType (123 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002150f000000-0x000002150fba0000-0x000002150fba0000), size 12189696, SharedBaseAddress: 0x000002150f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000021510000000-0x0000021550000000, reserved size: 1073741824
Narrow klass base: 0x000002150f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 16374M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 13312K, used 10568K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11776K, 79% used [0x00000000d5580000,0x00000000d5eb2220,0x00000000d6100000)
  from space 1536K, 75% used [0x00000000d6300000,0x00000000d6420040,0x00000000d6480000)
  to   space 1536K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6300000)
 ParOldGen       total 123392K, used 123357K [0x0000000080000000, 0x0000000087880000, 0x00000000d5580000)
  object space 123392K, 99% used [0x0000000080000000,0x0000000087877638,0x0000000087880000)
 Metaspace       used 75089K, committed 76608K, reserved 1179648K
  class space    used 7806K, committed 8512K, reserved 1048576K

Card table byte_map: [0x00000215739d0000,0x0000021573de0000] _byte_map_base: 0x00000215735d0000

Marking Bits: (ParMarkBitMap*) 0x00007ffd96fea340
 Begin Bits: [0x00000215774d0000, 0x00000215794d0000)
 End Bits:   [0x00000215794d0000, 0x000002157b4d0000)

Polling page: 0x0000021571f70000

Metaspace:

Usage:
  Non-class:     65.71 MB used.
      Class:      7.62 MB used.
       Both:     73.33 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      66.50 MB ( 52%) committed,  2 nodes.
      Class space:        1.00 GB reserved,       8.31 MB ( <1%) committed,  1 nodes.
             Both:        1.12 GB reserved,      74.81 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  13.00 MB
       Class:  7.58 MB
        Both:  20.58 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1378.
num_arena_deaths: 30.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1196.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 46.
num_chunks_taken_from_freelist: 4755.
num_chunk_merges: 13.
num_chunk_splits: 2907.
num_chunks_enlarged: 1692.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=12269Kb max_used=12269Kb free=107730Kb
 bounds [0x0000021507ad0000, 0x00000215086d0000, 0x000002150f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=36937Kb max_used=36937Kb free=83063Kb
 bounds [0x0000021500000000, 0x0000021502420000, 0x0000021507530000]
CodeHeap 'non-nmethods': size=5760Kb used=1493Kb max_used=1650Kb free=4266Kb
 bounds [0x0000021507530000, 0x00000215077a0000, 0x0000021507ad0000]
CodeCache: size=245760Kb, used=50699Kb, max_used=50856Kb, free=195059Kb
 total_blobs=15987, nmethods=15175, adapters=716, full_count=0
Compilation: enabled, stopped_count=0, restarted_count=0

Compilation events (20 events):
Event: 966.532 Thread 0x000002157ba860f0 15991       1       org.eclipse.jdt.internal.core.JavaElementRequestor::isCanceled (5 bytes)
Event: 966.532 Thread 0x000002157ba860f0 nmethod 15991 0x00000215086c1090 code [0x00000215086c1220, 0x00000215086c12f0]
Event: 966.533 Thread 0x000002157ba860f0 15996   !   3       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::initialize (159 bytes)
Event: 966.535 Thread 0x000002157ba860f0 nmethod 15996 0x00000215023ebb90 code [0x00000215023ebe40, 0x00000215023ecc10]
Event: 966.535 Thread 0x000002157ba860f0 15997       3       org.eclipse.jdt.internal.compiler.impl.IrritantSet::hasSameIrritants (38 bytes)
Event: 966.535 Thread 0x000002157ba860f0 nmethod 15997 0x00000215023ed310 code [0x00000215023ed4e0, 0x00000215023ed7c8]
Event: 966.535 Thread 0x000002157ba860f0 15998       3       org.eclipse.jdt.internal.compiler.ast.QualifiedTypeReference::rejectAnnotationsOnPackageQualifiers (129 bytes)
Event: 966.536 Thread 0x000002157ba860f0 nmethod 15998 0x00000215023ed990 code [0x00000215023edbc0, 0x00000215023ee500]
Event: 966.536 Thread 0x000002157ba860f0 16001 %     3       org.eclipse.jdt.internal.compiler.lookup.MethodBinding::sourceMethod @ 64 (90 bytes)
Event: 966.536 Thread 0x000002157ba860f0 nmethod 16001% 0x00000215023ee910 code [0x00000215023eeb20, 0x00000215023ef1f0]
Event: 966.536 Thread 0x000002157ba860f0 15999   !   3       org.eclipse.jdt.internal.compiler.ast.QualifiedTypeReference::findNextTypeBinding (151 bytes)
Event: 966.538 Thread 0x000002157ba860f0 nmethod 15999 0x00000215023ef410 code [0x00000215023ef6a0, 0x00000215023f0588]
Event: 966.538 Thread 0x000002157ba860f0 16000   !   3       org.eclipse.jdt.internal.compiler.ast.QualifiedTypeReference::recordResolution (72 bytes)
Event: 966.539 Thread 0x000002157ba860f0 nmethod 16000 0x00000215023f0e90 code [0x00000215023f1080, 0x00000215023f1648]
Event: 966.539 Thread 0x000002157ba860f0 16002       3       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::isGenericType (29 bytes)
Event: 966.539 Thread 0x000002157ba860f0 nmethod 16002 0x00000215023f1990 code [0x00000215023f1b80, 0x00000215023f20c0]
Event: 966.539 Thread 0x000002157ba860f0 16003       3       org.eclipse.jdt.internal.compiler.lookup.BinaryTypeBinding::isHierarchyConnected (32 bytes)
Event: 966.540 Thread 0x000002157ba860f0 nmethod 16003 0x00000215023f2210 code [0x00000215023f2400, 0x00000215023f2948]
Event: 966.540 Thread 0x000002157ba860f0 16004       3       org.eclipse.jdt.internal.compiler.ast.SingleMemberAnnotation::computeElementValuePairs (17 bytes)
Event: 966.540 Thread 0x000002157ba860f0 nmethod 16004 0x00000215023f2a90 code [0x00000215023f2c60, 0x00000215023f3038]

GC Heap History (20 events):
Event: 965.692 GC heap before
{Heap before GC invocations=178 (full 3):
 PSYoungGen      total 13312K, used 13294K [0x00000000d5580000, 0x00000000d6780000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1536K, 98% used [0x00000000d6600000,0x00000000d677bb20,0x00000000d6780000)
  to   space 2560K, 0% used [0x00000000d6280000,0x00000000d6280000,0x00000000d6500000)
 ParOldGen       total 113152K, used 112775K [0x0000000080000000, 0x0000000086e80000, 0x00000000d5580000)
  object space 113152K, 99% used [0x0000000080000000,0x0000000086e21dc0,0x0000000086e80000)
 Metaspace       used 74713K, committed 76288K, reserved 1179648K
  class space    used 7780K, committed 8512K, reserved 1048576K
}
Event: 965.705 GC heap after
{Heap after GC invocations=178 (full 3):
 PSYoungGen      total 14336K, used 1121K [0x00000000d5580000, 0x00000000d6680000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 2560K, 43% used [0x00000000d6280000,0x00000000d63986f8,0x00000000d6500000)
  to   space 1536K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6680000)
 ParOldGen       total 114688K, used 114256K [0x0000000080000000, 0x0000000087000000, 0x00000000d5580000)
  object space 114688K, 99% used [0x0000000080000000,0x0000000086f941e0,0x0000000087000000)
 Metaspace       used 74713K, committed 76288K, reserved 1179648K
  class space    used 7780K, committed 8512K, reserved 1048576K
}
Event: 966.017 GC heap before
{Heap before GC invocations=179 (full 3):
 PSYoungGen      total 14336K, used 12897K [0x00000000d5580000, 0x00000000d6680000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 2560K, 43% used [0x00000000d6280000,0x00000000d63986f8,0x00000000d6500000)
  to   space 1536K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6680000)
 ParOldGen       total 114688K, used 114256K [0x0000000080000000, 0x0000000087000000, 0x00000000d5580000)
  object space 114688K, 99% used [0x0000000080000000,0x0000000086f941e0,0x0000000087000000)
 Metaspace       used 74806K, committed 76352K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.025 GC heap after
{Heap after GC invocations=179 (full 3):
 PSYoungGen      total 13312K, used 1152K [0x00000000d5580000, 0x00000000d6680000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1536K, 75% used [0x00000000d6500000,0x00000000d6620040,0x00000000d6680000)
  to   space 1536K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6500000)
 ParOldGen       total 115712K, used 115218K [0x0000000080000000, 0x0000000087100000, 0x00000000d5580000)
  object space 115712K, 99% used [0x0000000080000000,0x0000000087084908,0x0000000087100000)
 Metaspace       used 74806K, committed 76352K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.098 GC heap before
{Heap before GC invocations=180 (full 3):
 PSYoungGen      total 13312K, used 12928K [0x00000000d5580000, 0x00000000d6680000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1536K, 75% used [0x00000000d6500000,0x00000000d6620040,0x00000000d6680000)
  to   space 1536K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6500000)
 ParOldGen       total 115712K, used 115218K [0x0000000080000000, 0x0000000087100000, 0x00000000d5580000)
  object space 115712K, 99% used [0x0000000080000000,0x0000000087084908,0x0000000087100000)
 Metaspace       used 74820K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.105 GC heap after
{Heap after GC invocations=180 (full 3):
 PSYoungGen      total 13312K, used 1056K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1536K, 68% used [0x00000000d6380000,0x00000000d6488020,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6580000)
 ParOldGen       total 116224K, used 116163K [0x0000000080000000, 0x0000000087180000, 0x00000000d5580000)
  object space 116224K, 99% used [0x0000000080000000,0x0000000087170c38,0x0000000087180000)
 Metaspace       used 74820K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.153 GC heap before
{Heap before GC invocations=181 (full 3):
 PSYoungGen      total 13312K, used 12832K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1536K, 68% used [0x00000000d6380000,0x00000000d6488020,0x00000000d6500000)
  to   space 512K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6580000)
 ParOldGen       total 116224K, used 116163K [0x0000000080000000, 0x0000000087180000, 0x00000000d5580000)
  object space 116224K, 99% used [0x0000000080000000,0x0000000087170c38,0x0000000087180000)
 Metaspace       used 74826K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.160 GC heap after
{Heap after GC invocations=181 (full 3):
 PSYoungGen      total 12288K, used 512K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 512K, 100% used [0x00000000d6500000,0x00000000d6580000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 117760K, used 117411K [0x0000000080000000, 0x0000000087300000, 0x00000000d5580000)
  object space 117760K, 99% used [0x0000000080000000,0x00000000872a8c78,0x0000000087300000)
 Metaspace       used 74826K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.266 GC heap before
{Heap before GC invocations=182 (full 3):
 PSYoungGen      total 12288K, used 12288K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 512K, 100% used [0x00000000d6500000,0x00000000d6580000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 117760K, used 117411K [0x0000000080000000, 0x0000000087300000, 0x00000000d5580000)
  object space 117760K, 99% used [0x0000000080000000,0x00000000872a8c78,0x0000000087300000)
 Metaspace       used 74860K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.273 GC heap after
{Heap after GC invocations=182 (full 3):
 PSYoungGen      total 13824K, used 2016K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 2048K, 98% used [0x00000000d6180000,0x00000000d6378020,0x00000000d6380000)
  to   space 3072K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6700000)
 ParOldGen       total 118272K, used 117975K [0x0000000080000000, 0x0000000087380000, 0x00000000d5580000)
  object space 118272K, 99% used [0x0000000080000000,0x0000000087335c88,0x0000000087380000)
 Metaspace       used 74860K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.309 GC heap before
{Heap before GC invocations=183 (full 3):
 PSYoungGen      total 13824K, used 13792K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 2048K, 98% used [0x00000000d6180000,0x00000000d6378020,0x00000000d6380000)
  to   space 3072K, 0% used [0x00000000d6400000,0x00000000d6400000,0x00000000d6700000)
 ParOldGen       total 118272K, used 117975K [0x0000000080000000, 0x0000000087380000, 0x00000000d5580000)
  object space 118272K, 99% used [0x0000000080000000,0x0000000087335c88,0x0000000087380000)
 Metaspace       used 74871K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.317 GC heap after
{Heap after GC invocations=183 (full 3):
 PSYoungGen      total 12800K, used 976K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1024K, 95% used [0x00000000d6400000,0x00000000d64f4030,0x00000000d6500000)
  to   space 2048K, 0% used [0x00000000d6100000,0x00000000d6100000,0x00000000d6300000)
 ParOldGen       total 120320K, used 119995K [0x0000000080000000, 0x0000000087580000, 0x00000000d5580000)
  object space 120320K, 99% used [0x0000000080000000,0x000000008752ecb8,0x0000000087580000)
 Metaspace       used 74871K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.340 GC heap before
{Heap before GC invocations=184 (full 3):
 PSYoungGen      total 12800K, used 12752K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1024K, 95% used [0x00000000d6400000,0x00000000d64f4030,0x00000000d6500000)
  to   space 2048K, 0% used [0x00000000d6100000,0x00000000d6100000,0x00000000d6300000)
 ParOldGen       total 120320K, used 119995K [0x0000000080000000, 0x0000000087580000, 0x00000000d5580000)
  object space 120320K, 99% used [0x0000000080000000,0x000000008752ecb8,0x0000000087580000)
 Metaspace       used 74875K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.347 GC heap after
{Heap after GC invocations=184 (full 3):
 PSYoungGen      total 13824K, used 928K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 2048K, 45% used [0x00000000d6100000,0x00000000d61e8020,0x00000000d6300000)
  to   space 1536K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6480000)
 ParOldGen       total 121344K, used 120837K [0x0000000080000000, 0x0000000087680000, 0x00000000d5580000)
  object space 121344K, 99% used [0x0000000080000000,0x00000000876015b8,0x0000000087680000)
 Metaspace       used 74875K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.384 GC heap before
{Heap before GC invocations=185 (full 3):
 PSYoungGen      total 13824K, used 12704K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 2048K, 45% used [0x00000000d6100000,0x00000000d61e8020,0x00000000d6300000)
  to   space 1536K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6480000)
 ParOldGen       total 121344K, used 120837K [0x0000000080000000, 0x0000000087680000, 0x00000000d5580000)
  object space 121344K, 99% used [0x0000000080000000,0x00000000876015b8,0x0000000087680000)
 Metaspace       used 74880K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.389 GC heap after
{Heap after GC invocations=185 (full 3):
 PSYoungGen      total 12800K, used 512K [0x00000000d5580000, 0x00000000d6400000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1024K, 50% used [0x00000000d6300000,0x00000000d6380020,0x00000000d6400000)
  to   space 1024K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6300000)
 ParOldGen       total 121856K, used 121621K [0x0000000080000000, 0x0000000087700000, 0x00000000d5580000)
  object space 121856K, 99% used [0x0000000080000000,0x00000000876c55f8,0x0000000087700000)
 Metaspace       used 74880K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.421 GC heap before
{Heap before GC invocations=186 (full 3):
 PSYoungGen      total 12800K, used 12288K [0x00000000d5580000, 0x00000000d6400000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1024K, 50% used [0x00000000d6300000,0x00000000d6380020,0x00000000d6400000)
  to   space 1024K, 0% used [0x00000000d6200000,0x00000000d6200000,0x00000000d6300000)
 ParOldGen       total 121856K, used 121621K [0x0000000080000000, 0x0000000087700000, 0x00000000d5580000)
  object space 121856K, 99% used [0x0000000080000000,0x00000000876c55f8,0x0000000087700000)
 Metaspace       used 74881K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.426 GC heap after
{Heap after GC invocations=186 (full 3):
 PSYoungGen      total 12800K, used 992K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1024K, 96% used [0x00000000d6200000,0x00000000d62f8020,0x00000000d6300000)
  to   space 2048K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6500000)
 ParOldGen       total 122880K, used 122413K [0x0000000080000000, 0x0000000087800000, 0x00000000d5580000)
  object space 122880K, 99% used [0x0000000080000000,0x000000008778b618,0x0000000087800000)
 Metaspace       used 74881K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.465 GC heap before
{Heap before GC invocations=187 (full 3):
 PSYoungGen      total 12800K, used 12768K [0x00000000d5580000, 0x00000000d6500000, 0x0000000100000000)
  eden space 11776K, 100% used [0x00000000d5580000,0x00000000d6100000,0x00000000d6100000)
  from space 1024K, 96% used [0x00000000d6200000,0x00000000d62f8020,0x00000000d6300000)
  to   space 2048K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6500000)
 ParOldGen       total 122880K, used 122413K [0x0000000080000000, 0x0000000087800000, 0x00000000d5580000)
  object space 122880K, 99% used [0x0000000080000000,0x000000008778b618,0x0000000087800000)
 Metaspace       used 74885K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}
Event: 966.472 GC heap after
{Heap after GC invocations=187 (full 3):
 PSYoungGen      total 13312K, used 1152K [0x00000000d5580000, 0x00000000d6480000, 0x0000000100000000)
  eden space 11776K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6100000)
  from space 1536K, 75% used [0x00000000d6300000,0x00000000d6420040,0x00000000d6480000)
  to   space 1536K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6300000)
 ParOldGen       total 123392K, used 123357K [0x0000000080000000, 0x0000000087880000, 0x00000000d5580000)
  object space 123392K, 99% used [0x0000000080000000,0x0000000087877638,0x0000000087880000)
 Metaspace       used 74885K, committed 76416K, reserved 1179648K
  class space    used 7785K, committed 8512K, reserved 1048576K
}

Dll operation events (14 events):
Event: 0.014 Loaded shared library d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
Event: 0.245 Loaded shared library d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.575 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
Event: 0.593 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
Event: 0.596 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
Event: 0.602 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
Event: 0.650 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
Event: 0.831 Loaded shared library d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
Event: 38.888 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\globalStorage\redhat.java\1.44.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736\eclipse_11913.dll
Event: 60.075 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management.dll
Event: 60.079 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management_ext.dll
Event: 62.666 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-119063\jna14242903334136583694.dll
Event: 63.817 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
Event: 65.439 Loaded shared library D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\extnet.dll

Deoptimization events (20 events):
Event: 965.684 Thread 0x0000021554b52270 DEOPT PACKING pc=0x0000021508548934 sp=0x00000075962fb3d0
Event: 965.684 Thread 0x0000021554b52270 DEOPT UNPACKING pc=0x0000021507583a9c sp=0x00000075962fb338 mode 2
Event: 965.684 Thread 0x0000021554b52270 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000021508548934 relative=0x0000000000000f14
Event: 965.684 Thread 0x0000021554b52270 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000021508548934 method=lombok.eclipse.handlers.EclipseHandlerUtil.isBoolean(Lorg/eclipse/jdt/internal/compiler/ast/TypeReference;)Z @ 1 c2
Event: 965.684 Thread 0x0000021554b52270 DEOPT PACKING pc=0x0000021508548934 sp=0x00000075962fb4e0
Event: 965.684 Thread 0x0000021554b52270 DEOPT UNPACKING pc=0x0000021507583a9c sp=0x00000075962fb448 mode 2
Event: 966.198 Thread 0x0000021554b53620 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000021508687488 relative=0x0000000000006ee8
Event: 966.199 Thread 0x0000021554b53620 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000021508687488 method=org.eclipse.jdt.internal.compiler.lookup.LookupEnvironment.getTypeFromTypeSignature(Lorg/eclipse/jdt/internal/compiler/lookup/SignatureWrapper;[Lorg/eclipse/jdt/internal/com
Event: 966.200 Thread 0x0000021554b53620 DEOPT PACKING pc=0x0000021508687488 sp=0x00000075945fd6c0
Event: 966.201 Thread 0x0000021554b53620 DEOPT UNPACKING pc=0x0000021507583a9c sp=0x00000075945fd6e0 mode 2
Event: 966.459 Thread 0x0000021554b52270 Uncommon trap: trap_request=0xffffff6e fr.pc=0x00000215086bd880 relative=0x0000000000000d40
Event: 966.459 Thread 0x0000021554b52270 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x00000215086bd880 method=org.eclipse.jdt.internal.compiler.lookup.BlockScope.findVariable([C)Lorg/eclipse/jdt/internal/compiler/lookup/LocalVariableBinding; @ 65 c2
Event: 966.459 Thread 0x0000021554b52270 DEOPT PACKING pc=0x00000215086bd880 sp=0x00000075962fd860
Event: 966.459 Thread 0x0000021554b52270 DEOPT UNPACKING pc=0x0000021507583a9c sp=0x00000075962fd718 mode 2
Event: 966.511 Thread 0x0000021554b53620 DEOPT PACKING pc=0x0000021501a117b5 sp=0x00000075945fd2f0
Event: 966.511 Thread 0x0000021554b53620 DEOPT UNPACKING pc=0x0000021507584223 sp=0x00000075945fc718 mode 0
Event: 966.512 Thread 0x0000021554b52270 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000021508439d04 relative=0x0000000000000224
Event: 966.512 Thread 0x0000021554b52270 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000021508439d04 method=org.eclipse.jdt.core.compiler.CharOperation.match([CII[CIIZ)Z @ 48 c2
Event: 966.512 Thread 0x0000021554b52270 DEOPT PACKING pc=0x0000021508439d04 sp=0x00000075962fc380
Event: 966.512 Thread 0x0000021554b52270 DEOPT UNPACKING pc=0x0000021507583a9c sp=0x00000075962fc2e8 mode 2

Classes loaded (20 events):
Event: 953.625 Loading class java/nio/channels/FileChannel$MapMode
Event: 953.625 Loading class java/nio/channels/FileChannel$MapMode done
Event: 953.625 Loading class jdk/internal/misc/ExtendedMapMode
Event: 953.626 Loading class jdk/internal/misc/ExtendedMapMode done
Event: 953.631 Loading class sun/nio/ch/FileChannelImpl$DefaultUnmapper
Event: 953.632 Loading class sun/nio/ch/FileChannelImpl$Unmapper
Event: 953.632 Loading class jdk/internal/access/foreign/UnmapperProxy
Event: 953.633 Loading class jdk/internal/access/foreign/UnmapperProxy done
Event: 953.633 Loading class sun/nio/ch/FileChannelImpl$Unmapper done
Event: 953.633 Loading class sun/nio/ch/FileChannelImpl$DefaultUnmapper done
Event: 953.633 Loading class sun/nio/ch/Util$4
Event: 953.633 Loading class sun/nio/ch/Util$4 done
Event: 963.333 Loading class java/util/concurrent/CompletableFuture$UniApply
Event: 963.334 Loading class java/util/concurrent/CompletableFuture$UniApply done
Event: 963.334 Loading class java/util/concurrent/CompletableFuture$UniExceptionally
Event: 963.335 Loading class java/util/concurrent/CompletableFuture$UniExceptionally done
Event: 963.695 Loading class java/util/Hashtable$ValueCollection
Event: 963.695 Loading class java/util/Hashtable$ValueCollection done
Event: 964.421 Loading class java/lang/UnsupportedClassVersionError
Event: 964.421 Loading class java/lang/UnsupportedClassVersionError done

Classes unloaded (15 events):
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a4400 'java/lang/invoke/LambdaForm$MH+0x00000215101a4400'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a4000 'java/lang/invoke/LambdaForm$MH+0x00000215101a4000'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a3c00 'java/lang/invoke/LambdaForm$MH+0x00000215101a3c00'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a3800 'java/lang/invoke/LambdaForm$MH+0x00000215101a3800'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a3400 'java/lang/invoke/LambdaForm$BMH+0x00000215101a3400'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a3000 'java/lang/invoke/LambdaForm$DMH+0x00000215101a3000'
Event: 61.230 Thread 0x0000021571e5ec30 Unloading class 0x00000215101a2000 'java/lang/invoke/LambdaForm$DMH+0x00000215101a2000'
Event: 64.042 Thread 0x0000021571e5ec30 Unloading class 0x00000215102f8800 'java/lang/invoke/LambdaForm$MH+0x00000215102f8800'
Event: 64.042 Thread 0x0000021571e5ec30 Unloading class 0x00000215102f7c00 'java/lang/invoke/LambdaForm$MH+0x00000215102f7c00'
Event: 64.042 Thread 0x0000021571e5ec30 Unloading class 0x00000215102f7400 'java/lang/invoke/LambdaForm$DMH+0x00000215102f7400'
Event: 64.042 Thread 0x0000021571e5ec30 Unloading class 0x00000215102f6c00 'java/lang/invoke/LambdaForm$MH+0x00000215102f6c00'
Event: 213.181 Thread 0x0000021571e5ec30 Unloading class 0x00000215106bd400 'java/lang/invoke/LambdaForm$DMH+0x00000215106bd400'
Event: 213.181 Thread 0x0000021571e5ec30 Unloading class 0x00000215106bd000 'java/lang/invoke/LambdaForm$DMH+0x00000215106bd000'
Event: 213.181 Thread 0x0000021571e5ec30 Unloading class 0x00000215106bcc00 'java/lang/invoke/LambdaForm$DMH+0x00000215106bcc00'
Event: 213.181 Thread 0x0000021571e5ec30 Unloading class 0x00000215106bc400 'java/lang/invoke/LambdaForm$DMH+0x00000215106bc400'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 953.447 Thread 0x0000021554b53620 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66ba648}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d66ba648) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 953.634 Thread 0x0000021554b53620 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d585f740}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, int, long, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d585f740) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 958.003 Thread 0x0000021554b52270 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d635bd60}> (0x00000000d635bd60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 958.142 Thread 0x000002157d809a20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66f1018}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d66f1018) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 958.143 Thread 0x000002157d809a20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66f53b0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d66f53b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 958.143 Thread 0x000002157d809a20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d66f90e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d66f90e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 959.144 Thread 0x000002157d809a20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d87288}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, int)'> (0x00000000d5d87288) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 959.144 Thread 0x000002157d809a20 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d8b2e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, int)'> (0x00000000d5d8b2e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 961.331 Thread 0x000002157d809a20 Implicit null exception at 0x000002150826ef3f to 0x000002150826ef92
Event: 963.444 Thread 0x000002155feac170 Implicit null exception at 0x00000215085453c2 to 0x0000021508546198
Event: 963.513 Thread 0x0000021554b52270 Implicit null exception at 0x0000021508502f0d to 0x000002150850325c
Event: 963.632 Thread 0x000002155feac170 Exception <a 'java/lang/LinkageError'{0x00000000d5646e30}: loader lombok.launch.ShadowClassLoader @759e3669 attempted duplicate class definition for lombok.launch.PatchFixesHider$FieldInitializer. (lombok.launch.PatchFixesHider$FieldInitializer is in unnamed module of loader lombok.launch.ShadowClassLoader @759e3669, parent loader org.eclipse.osgi.internal.loader.EquinoxClassLoader @3631ccfa)> (0x00000000d5646e30) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 1682]
Event: 963.979 Thread 0x0000021554b51550 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d75190}> (0x00000000d5d75190) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 963.979 Thread 0x0000021554b51550 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d75a98}> (0x00000000d5d75a98) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 963.980 Thread 0x0000021554b51550 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d79508}> (0x00000000d5d79508) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 963.980 Thread 0x0000021554b51550 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5d79a60}> (0x00000000d5d79a60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 964.001 Thread 0x0000021554b51550 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5dd14d8}: Found class java.lang.Object, but interface was expected> (0x00000000d5dd14d8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 964.371 Thread 0x0000021554b53620 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e30ad8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, long)'> (0x00000000d5e30ad8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 964.477 Thread 0x0000021554b52270 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60b4ef8}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d60b4ef8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 965.317 Thread 0x0000021554b52270 Implicit null exception at 0x0000021508616389 to 0x0000021508616568

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 965.691 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 965.705 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.016 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.025 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.098 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.105 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.152 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.160 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.266 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.273 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.309 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.317 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.340 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.347 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.380 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.389 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.421 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.426 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 966.465 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 966.472 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500914190
Event: 213.215 Thread 0x0000021571e5ec30 flushing osr nmethod 0x0000021500919690
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500934590
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500959710
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500962410
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500967590
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500971890
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x0000021500999f90
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x000002150099dc10
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x000002150099ef90
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009a1310
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009a2690
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009a9990
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009ab390
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009aba90
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009ba790
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009bb490
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009bd090
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009c2890
Event: 213.215 Thread 0x0000021571e5ec30 flushing  nmethod 0x00000215009c2d10

Events (20 events):
Event: 951.636 Thread 0x0000021554a2e8b0 Thread exited: 0x0000021554a2e8b0
Event: 951.869 Thread 0x000002157ba860f0 Thread added: 0x0000021554a2b230
Event: 952.121 Thread 0x0000021554a2b230 Thread exited: 0x0000021554a2b230
Event: 952.209 Thread 0x000002157ba860f0 Thread added: 0x0000021554a2b230
Event: 952.458 Thread 0x000002157d808670 Thread added: 0x0000021554b52900
Event: 952.491 Thread 0x0000021554b52900 Thread exited: 0x0000021554b52900
Event: 952.607 Thread 0x000002157ba860f0 Thread added: 0x0000021555e5da30
Event: 953.117 Thread 0x0000021555e5da30 Thread exited: 0x0000021555e5da30
Event: 954.228 Thread 0x0000021554a2b230 Thread exited: 0x0000021554a2b230
Event: 957.945 Thread 0x000002157d809a20 Thread added: 0x0000021554b52270
Event: 958.346 Thread 0x000002157ba855b0 Thread added: 0x00000215545f8660
Event: 959.218 Thread 0x000002157ba860f0 Thread added: 0x00000215545f2700
Event: 963.337 Thread 0x0000021554b51550 Thread added: 0x000002155feac170
Event: 963.359 Thread 0x000002155feac170 Thread added: 0x000002155feac800
Event: 963.413 Thread 0x000002155feac800 Thread added: 0x000002155feadbb0
Event: 963.467 Thread 0x000002155feadbb0 Thread added: 0x000002155feae240
Event: 963.481 Thread 0x000002155feae240 Thread added: 0x000002155feafc80
Event: 963.496 Thread 0x000002155feafc80 Thread added: 0x000002155feb09a0
Event: 964.367 Thread 0x00000215545f2700 Thread exited: 0x00000215545f2700
Event: 964.700 Thread 0x00000215545f8660 Thread added: 0x00000215545f5d80


Dynamic libraries:
0x00007ff7cfb30000 - 0x00007ff7cfb3e000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.exe
0x00007ffdf9e10000 - 0x00007ffdfa008000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdf7e30000 - 0x00007ffdf7ef2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdf74a0000 - 0x00007ffdf7796000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdf77a0000 - 0x00007ffdf78a0000 	C:\Windows\System32\ucrtbase.dll
0x00007ffdec7c0000 - 0x00007ffdec7d8000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jli.dll
0x00007ffdf88d0000 - 0x00007ffdf8a6d000 	C:\Windows\System32\USER32.dll
0x00007ffdf7af0000 - 0x00007ffdf7b12000 	C:\Windows\System32\win32u.dll
0x00007ffdf9580000 - 0x00007ffdf95ab000 	C:\Windows\System32\GDI32.dll
0x00007ffdf7920000 - 0x00007ffdf7a39000 	C:\Windows\System32\gdi32full.dll
0x00007ffdf7d10000 - 0x00007ffdf7dad000 	C:\Windows\System32\msvcp_win.dll
0x00007ffde5000000 - 0x00007ffde501e000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffdebdd0000 - 0x00007ffdec06a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffdf8fd0000 - 0x00007ffdf906e000 	C:\Windows\System32\msvcrt.dll
0x00007ffdf9790000 - 0x00007ffdf97bf000 	C:\Windows\System32\IMM32.DLL
0x00007ffdf3650000 - 0x00007ffdf365c000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdcdc10000 - 0x00007ffdcdc9d000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\msvcp140.dll
0x00007ffd96330000 - 0x00007ffd970c7000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server\jvm.dll
0x00007ffdf9d10000 - 0x00007ffdf9dc1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdf7f60000 - 0x00007ffdf7fff000 	C:\Windows\System32\sechost.dll
0x00007ffdf9660000 - 0x00007ffdf9783000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdf7db0000 - 0x00007ffdf7dd7000 	C:\Windows\System32\bcrypt.dll
0x00007ffdf87f0000 - 0x00007ffdf885b000 	C:\Windows\System32\WS2_32.dll
0x00007ffdf69e0000 - 0x00007ffdf6a2b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdec840000 - 0x00007ffdec867000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdf0480000 - 0x00007ffdf048a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdf68a0000 - 0x00007ffdf68b2000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdf5ce0000 - 0x00007ffdf5cf2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffdeefe0000 - 0x00007ffdeefea000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\jimage.dll
0x00007ffdf5820000 - 0x00007ffdf5a21000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffdefa60000 - 0x00007ffdefa94000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdf7c80000 - 0x00007ffdf7d02000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffdeed90000 - 0x00007ffdeed9f000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll
0x00007ffde3640000 - 0x00007ffde365f000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\java.dll
0x00007ffdf8080000 - 0x00007ffdf87ee000 	C:\Windows\System32\SHELL32.dll
0x00007ffdf4f50000 - 0x00007ffdf56f4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffdf8b90000 - 0x00007ffdf8ee3000 	C:\Windows\System32\combase.dll
0x00007ffdf6ed0000 - 0x00007ffdf6efb000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ffdf8ef0000 - 0x00007ffdf8fbd000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdf92b0000 - 0x00007ffdf935d000 	C:\Windows\System32\SHCORE.dll
0x00007ffdf93c0000 - 0x00007ffdf941b000 	C:\Windows\System32\shlwapi.dll
0x00007ffdf73d0000 - 0x00007ffdf73f4000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffddff90000 - 0x00007ffddffa8000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\zip.dll
0x00007ffddff80000 - 0x00007ffddff90000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\net.dll
0x00007ffdf0ff0000 - 0x00007ffdf10fa000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffdf6c30000 - 0x00007ffdf6c9a000 	C:\Windows\system32\mswsock.dll
0x00007ffddff60000 - 0x00007ffddff76000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\nio.dll
0x00007ffddf290000 - 0x00007ffddf2a0000 	d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\verify.dll
0x00007ffd9b010000 - 0x00007ffd9b055000 	D:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\globalStorage\redhat.java\1.44.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736\eclipse_11913.dll
0x00007ffdf9450000 - 0x00007ffdf957b000 	C:\Windows\System32\ole32.dll
0x00007ffdde190000 - 0x00007ffdde19a000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management.dll
0x00007ffddb990000 - 0x00007ffddb99b000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\management_ext.dll
0x00007ffdf88c0000 - 0x00007ffdf88c8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdf6e20000 - 0x00007ffdf6e38000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffdf6480000 - 0x00007ffdf64b8000 	C:\Windows\system32\rsaenh.dll
0x00007ffdf7390000 - 0x00007ffdf73be000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffdf6e40000 - 0x00007ffdf6e4c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffdf68c0000 - 0x00007ffdf68fb000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdf9440000 - 0x00007ffdf9448000 	C:\Windows\System32\NSI.dll
0x00007ffda2f90000 - 0x00007ffda2fd9000 	C:\Users\<USER>\AppData\Local\Temp\jna-119063\jna14242903334136583694.dll
0x00007ffdf2ce0000 - 0x00007ffdf2cf7000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdf2cc0000 - 0x00007ffdf2cdd000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffdecdd0000 - 0x00007ffdecdde000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\sunmscapi.dll
0x00007ffdf7b20000 - 0x00007ffdf7c7d000 	C:\Windows\System32\CRYPT32.dll
0x00007ffdf6f40000 - 0x00007ffdf6f67000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffdf6f00000 - 0x00007ffdf6f3b000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffdecdc0000 - 0x00007ffdecdc9000 	D:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\extnet.dll
0x00007ffded010000 - 0x00007ffded05b000 	C:\Program Files (x86)\Sangfor\SSL\ClientComponent\SangforNspX64.dll
0x00007ffdf6900000 - 0x00007ffdf69ca000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffdf0c20000 - 0x00007ffdf0c2a000 	C:\Windows\System32\rasadhlp.dll
0x00007ffdf0260000 - 0x00007ffdf02e0000 	C:\Windows\System32\fwpuclnt.dll

JVMTI agents:
d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar path:d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\instrument.dll, loaded, initialized, instrumentlib options:none

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\jre\21.0.8-win32-x86_64\bin\server;D:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\globalStorage\redhat.java\1.44.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1400.v20250730-1736;C:\Users\<USER>\AppData\Local\Temp\jna-119063;C:\Program Files (x86)\Sangfor\SSL\ClientComponent

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\workspaceStorage\a4a2337d6edc7af7211706e891d68e53\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\globalStorage\redhat.java\1.44.0\config_win -data d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\workspaceStorage\a4a2337d6edc7af7211706e891d68e53\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-ee386846efe6b792f3c8b1edf9d3fd1d-sock
java_class_path (initial): d:\Programs\VSCode-win32-x64-1.99.0\data\extensions\redhat.java-1.44.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = d:\Programs\VSCode-win32-x64-1.99.0\data\user-data\User\workspaceStorage\a4a2337d6edc7af7211706e891d68e53\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Programs\jdk1.8.0_451
PATH=C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\dotnet\;C:\ProgramData\chocolatey\bin;C:\Program Files\Git\cmd;D:\Programs\jdk1.8.0_451\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;
USERNAME=xut
OS=Windows_NT
PROCESSOR_IDENTIFIER=VIA64 Family 7 Model 11 Stepping 1, CentaurHauls
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 34 days 15:11 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 7 model 59 stepping 1 microcode 0x0, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, aes, clmul, bmi1, bmi2, adx, clflush, rdtscp
Processor Information for the first 8 processors :
  Max Mhz: 2700, Current Mhz: 2700, Mhz Limit: 2700

Memory: 4k page, system-wide physical 16374M (1037M free)
TotalPageFile size 31133M (AvailPageFile size 12150M)
current process WorkingSet (physical memory assigned to process): 422M, peak: 510M
current process commit charge ("private bytes"): 503M, peak: 584M

vm_info: OpenJDK 64-Bit Server VM (21.0.8+9-LTS) for windows-amd64 JRE (21.0.8+9-LTS), built on 2025-07-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
