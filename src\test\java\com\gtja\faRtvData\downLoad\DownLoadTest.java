package com.gtja.faRtvData.downLoad;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * @ClassName
 * @Description TODO
 * <AUTHOR> @date
 * @Version 1.0
 */
@Slf4j
public class DownLoadTest {
    public static void main(String[] args) {
        String filePath = "C:\\Users\\<USER>\\Desktop\\测试URL下载\\";
        String name = "2022101401179_c.pdf";
        stockIssueDownload(name, filePath);
    }

    public static String stockIssueDownload(String name, String path) {
        //根据url 访问外网下载附件
        OutputStream outputStream = null;
        InputStream inputStream = null;
        String filePathTo = path + "/new/" + name;
        String filePathFrom = path + name;
        try {
            outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePathTo)));
            inputStream = new FileInputStream(filePathFrom);
            if (null == inputStream) {
                log.info("文件下载为空：未获取文件流");
            }
            byte[] buf = new byte[1024];
            int byteRead, byteWrten = 0;
            while ((byteRead = inputStream.read(buf)) != -1) {
                outputStream.write(buf, 0, byteRead);
                outputStream.flush();
                byteWrten += byteRead;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
            log.info("文件下载路径访问异常：" + e.getMessage());
        } catch (IOException e) {
            e.printStackTrace();
            log.info("文件下载异常：" + e.getMessage());
        } finally {
            try {
                inputStream.close();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "文件读取完成";
    }


    public String stockIssueDownload1(String urlAddress, String path) {
        log.info("下载文件的url{},下载文件到路径下{}", urlAddress, path);
        //根据url 访问外网下载附件
        OutputStream outputStream = null;
        URLConnection urlConnection = null;
        InputStream inputStream = null;
        int lastName = urlAddress.lastIndexOf("/");
        String name = urlAddress.substring(lastName + 1, urlAddress.length());

        String filePath = path + File.separator + name;
        try {
            Properties prop = System.getProperties();
            prop.put("http.proxyHost", "************");
            prop.put("http.proxyPort", "3128");

            URL url = new URL(urlAddress);
            outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(filePath)));
            urlConnection = url.openConnection();
            inputStream = urlConnection.getInputStream();
            if (null == inputStream) {
                log.info("文件下载为空：未获取文件流");
            }

            byte[] buf = new byte[1024];
            int byteRead, byteWrten = 0;
            while ((byteRead = inputStream.read(buf)) != -1) {
                outputStream.write(buf, 0, byteRead);
                outputStream.flush();
                byteWrten += byteRead;
            }
        } catch (MalformedURLException e) {
            e.printStackTrace();
            log.info("文件下载路径访问异常：" + e.getMessage());
        } catch (IOException e) {
            e.printStackTrace();
            log.info("文件下载异常：" + e.getMessage());
        } finally {
            try {
                inputStream.close();
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return filePath;
    }

}
