package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 * 概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvGzbModel {

    @DbfAnnotation(fieldName = "L_BH", fieldLength = 8)
    private String l_bh;
    @DbfAnnotation(fieldName = "VC_KMDM", fieldLength = 254)
    private String vc_kmdm;
    @DbfAnnotation(fieldName = "VC_KMMC", fieldLength = 128)
    private String vc_kmmc;
    @DbfAnnotation(fieldName = "L_SL", fieldLength = 40)
    private String l_sl;
    @DbfAnnotation(fieldName = "EN_DWCB", fieldLength = 40)
    private String en_dwcb;
    @DbfAnnotation(fieldName = "EN_CB", fieldLength = 40)
    private String en_cb;
    @DbfAnnotation(fieldName = "EN_CBZJZ", fieldLength = 40)
    private String en_cbzjz;
    @DbfAnnotation(fieldName = "EN_HQJZ", fieldLength = 40)
    private String en_hqjz;
    @DbfAnnotation(fieldName = "EN_SZ", fieldLength = 40)
    private String en_sz;
    @DbfAnnotation(fieldName = "EN_SZZJZ", fieldLength = 40)
    private String en_szzjz;
    @DbfAnnotation(fieldName = "EN_GZZZ", fieldLength = 40)
    private String en_gzzz;
    @DbfAnnotation(fieldName = "VC_TPXX", fieldLength = 100)
    private String vc_tpxx;
    @DbfAnnotation(fieldName = "VC_QYXX", fieldLength = 32)
    private String vc_qyxx;
    @DbfAnnotation(fieldName = "L_TMPID", fieldLength = 8)
    private String l_tmpid;
    @DbfAnnotation(fieldName = "VC_JYS", fieldLength = 128)
    private String vc_jys;
    @DbfAnnotation(fieldName = "VC_TZPZ", fieldLength = 128)
    private String vc_tzpz;
    @DbfAnnotation(fieldName = "VC_XJL", fieldLength = 128)
    private String vc_xjl;
    @DbfAnnotation(fieldName = "VC_TS", fieldLength = 128)
    private String vc_ts;
    @DbfAnnotation(fieldName = "EN_FDYKBL", fieldLength = 32)
    private String en_fdykbl;
    @DbfAnnotation(fieldName = "L_LEAF", fieldLength = 1)
    private String l_leaf;
    @DbfAnnotation(fieldName = "VC_KMPARENT", fieldLength = 32)
    private String vc_kmparent;
    @DbfAnnotation(fieldName = "L_LEVEL", fieldLength = 2)
    private String l_level;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 9)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "D_YWRQ", fieldLength = 12)
    private String d_ywrq;
    @DbfAnnotation(fieldName = "D_SCSJ", fieldLength = 12)
    private String d_scsj;
    @DbfAnnotation(fieldName = "L_QUANTITY", fieldLength = 1)
    private String l_quantity;
    @DbfAnnotation(fieldName = "VC_CODE_HS", fieldLength = 32)
    private String vc_code_hs;
    @DbfAnnotation(fieldName = "L_KIND", fieldLength = 3)
    private String l_kind;
    @DbfAnnotation(fieldName = "L_GZKMBZ", fieldLength = 1)
    private String l_gzkmbz;
    @DbfAnnotation(fieldName = "VC_JSBZ", fieldLength = 3)
    private String vc_jsbz;
    @DbfAnnotation(fieldName = "EN_EXCH", fieldLength = 50)
    private String en_exch;
    @DbfAnnotation(fieldName = "EN_WBCB", fieldLength = 32)
    private String en_wbcb;
    @DbfAnnotation(fieldName = "EN_WBHQ", fieldLength = 32)
    private String en_wbhq;
    @DbfAnnotation(fieldName = "EN_WBSZ", fieldLength = 32)
    private String en_wbsz;
    @DbfAnnotation(fieldName = "L_ZQNM", fieldLength = 8)
    private String l_zqnm;
    @DbfAnnotation(fieldName = "L_SFQR", fieldLength = 1)
    private String l_sfqr;
    @DbfAnnotation(fieldName = "L_TZLX", fieldLength = 4)
    private String l_tzlx;
    @DbfAnnotation(fieldName = "EN_ZYJ", fieldLength = 32)
    private String en_zyj;
    @DbfAnnotation(fieldName = "EN_WBZYJ", fieldLength = 32)
    private String en_wbzyj;
    @DbfAnnotation(fieldName = "VC_CHECKER", fieldLength = 32)
    private String vc_checker;
    @DbfAnnotation(fieldName = "VC_BZW", fieldLength = 20)
    private String vc_bzw;
    @DbfAnnotation(fieldName = "VC_LTLX", fieldLength = 1)
    private String vc_ltlx;
    @DbfAnnotation(fieldName = "EN_YBGZZZ", fieldLength = 32)
    private String en_ybgzzz;
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;
    @DbfAnnotation(fieldName = "L_SCLB", fieldLength = 4)
    private String l_sclb;
    @DbfAnnotation(fieldName = "L_ZQLB", fieldLength = 4)
    private String l_zqlb;
    @DbfAnnotation(fieldName = "EN_QYXX", fieldLength = 32)
    private String en_qyxx;

}
