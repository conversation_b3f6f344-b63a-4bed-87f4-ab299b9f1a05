package com.gtja.faRtvData.downLoad;

import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.common.utils.sftp.FtpUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;

@Slf4j
public class FtpTest {
    @Autowired
    static FtpUtils ftpUtils;
    private String RTV_BASE_PATH = "D:\\Users\\User\\Desktop\\实时估值\\YYYYMMDD\\";
    @Value("${baseFilePath.sftpTargetPath}")
    protected String SFTP_PATH;
    @Autowired
    SftpInfoModel dbinfo;

    public static void main(String[] args) {

        //  ChannelSftp sftp = getChannelSftp(sftpSendConfig);

    }

    @Test
    public void test001() {
        try {
            SftpInfoModel model = new SftpInfoModel();
            model.setHost("************");
            model.setIp("************");
            model.setPort("21");
            model.setPassWord("ftp.2024");
            model.setUserName("ftpuser");
//            ip : ************
//            port : 21
//            userName : ftpuser
//            passWord : ftp.2024
            //  ftpUtils.UploadFTPFile(dbinfo,RTV_BASE_PATH,SFTP_PATH);
        //    ftpUtils.UploadFTPFile("************", 21, "ftpuser", "ftp.2024", "D:\\Users\\User\\Desktop\\实时估值\\********\\SABX14\\TACCOUNT_SABX14_********.DBF",                    "/magicTest/");
        //    ftpUtils.UploadFTPFile(model, "D:\\Users\\User\\Desktop\\实时估值\\********\\SABX14\\TACCOUNT_SABX14_********.DBF",                    "/magicTest001/");


            //  ftpUtils.UploadFTPFiles(model,"D:\\Users\\User\\Desktop\\实时估值\\********\\","/ssgz/********/");

             ftpUtils.UploadFTPFiles(model, "D:\\Users\\User\\Desktop\\实时估值\\********\\", "/ssgz/magicTest001/");

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

}
