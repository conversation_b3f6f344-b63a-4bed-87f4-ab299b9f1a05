<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.RealTimeValueDao">

    <select id="queryRealTimeValueCpConfig1" parameterType="java.lang.String" resultType="java.lang.String">
        select cpdm
        from fbzx.gtja_ssgz_cpqd t
        where t.status = 0
    </select>


    <select id="queryRealTimeValueCpqd" parameterType="java.lang.String"
            resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
        with temp_batch as
                 (select max(t.batch_no) batch_no
                       , t.cpdm
                  from gtja_ssgz_file_out_log t
                  where t.ywrq = #{ywrq}
                  group by t.cpdm)
        select distinct a.cpdm
                      , #{ywrq} ywrq
                      , 1 as    batchNo
        from fbzx.gtja_ssgz_cpqd a
           , fbzx.gtja_ssgz_file_qd b
        where a.status = 0
          and b.type = 1
          and b.status=0
          and a.gzrq=#{ywrq}
          and a.cpdm=#{cpdm}
          and not exists(select 1
                         from gtja_ssgz_file_out_log c
                         where a.cpdm = c.cpdm
                           and b.file_name = c.file_name
                           and c.ywrq = #{ywrq})
        union
        select distinct a.cpdm as cpdm
                      ,max(ywrq) ywrq
                      ,max(c.batch_no) + 1 as batchNo
        from fbzx.gtja_ssgz_file_out_log c
           ,fbzx.gtja_ssgz_cpqd         a
           ,temp_batch                  d
        where 1 = 1
          and a.cpdm = c.cpdm
          and c.ywrq = #{ywrq}
          and a.cpdm=#{cpdm}
          and c.batch_no = d.batch_no
          and d.cpdm = c.cpdm
          and a.gzsj > c.create_date
        group by a.cpdm
    </select>

    <!-- 从每个产品的最新的日志俩判断估值是否重新确认  4.11 修改  估值日期入产品表-->
    <select id="queryRealTimeValueCpConfig" parameterType="java.lang.String"
            resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
        with temp_batch as
                 (select max(t.batch_no) batch_no
                       , t.cpdm
                  from gtja_ssgz_file_out_log t
                  where t.ywrq = #{ywrq}
                  group by t.cpdm)
        select distinct a.cpdm
                      , #{ywrq} ywrq
                      , 1 as    batchNo
        from fbzx.gtja_ssgz_cpqd a
           , fbzx.gtja_ssgz_file_qd b
        where a.status = 0
          and b.type = 1
          and a.gzrq=#{ywrq}
          and not exists(select 1
                         from gtja_ssgz_file_out_log c
                         where a.cpdm = c.cpdm
                           and b.file_name = c.file_name
                           and c.ywrq = #{ywrq})
        union
        select distinct a.cpdm as cpdm
                      ,max(ywrq) ywrq
                      ,max(c.batch_no) + 1 as batchNo
        from fbzx.gtja_ssgz_file_out_log c
           ,fbzx.gtja_ssgz_cpqd         a
           ,temp_batch                  d
        where 1 = 1
          and a.cpdm = c.cpdm
          and c.ywrq = #{ywrq}
          and c.batch_no = d.batch_no
          and d.cpdm = c.cpdm
          and a.gzsj > c.create_date
        group by a.cpdm
    </select>

    <select id="queryRtvFileConfig" resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
        select t.file_name as rtvDbfName, t.type
        from fbzx.gtja_ssgz_file_qd t
        where t.status = 0
         and  t.type=#{type}
    </select>
    <!--    .OK 文件的生成 需要保证 产品对应的文件数量满足 配置的文件数量   该sql第二次 无法查询出需要生成的。ok文件-->
    <select id="queryRtvFileCommonOk" parameterType="java.lang.String"
            resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
        with temp_qr as
                 (select t.type
                       , count(1) numbers
                  from fbzx.gtja_ssgz_file_qd t
                  where t.status = 0
                  group by t.type),
             temp_log as (select b.cpdm
                               , a.type
                               , max(b.batch_no) batch_no
                               , count(1)        numbers
                               , MIN(b.ywrq)     YWRQ
                          from fbzx.gtja_ssgz_file_qd a
                             , fbzx.gtja_ssgz_file_out_log b
                          where a.file_name = b.file_name
                            and a.status = 0
                            and b.status = 0
                            and b.ywrq = #{ywrq}
                          group by b.cpdm
                                 , a.type)
        select cpdm
             , case
                   when cpdm !='GG'  then cpdm || '_' || b.ywrq || '_' || b.batch_no || '.OK'
                   else
                       'GG' || '_' || b.ywrq || '.OK'
            end as        rtvDbfName
             , b.batch_no batchNo
        from temp_qr a
           , temp_log b
        where a.type = b.type
          and a.numbers = b.numbers
    </select>

    <!--    .OK 文件的生成 需要保证 产品对应的文件数量满足 配置的文件数量-->
    <select id="queryRtvFileOk" parameterType="com.gtja.faRtvData.model.RtvCommonPdfModel"
            resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
        with temp_qr as (
        select t.type
        ,count(1) numbers
        from fbzx.gtja_ssgz_file_qd t
        where t.status = 0
        group by t.type
        ), temp_log as (
        select b.cpdm
        ,a.type
        ,max(b.batch_no) batch_no
        ,count(1) numbers
        ,MIN(b.ywrq) YWRQ
        from fbzx.gtja_ssgz_file_qd a
        ,fbzx.gtja_ssgz_file_out_log b
        where a.file_name = b.file_name
        and a.status = 0
        and b.status = 0
        <if test="infos != null and infos.size()> 0">
            and (b.ywrq,b.cpdm,b.batch_no) in
            <foreach collection="infos" item="info" index="index" open="(" close=")" separator=",">
                (#{info.ywrq},#{info.cpdm},#{info.batchNo})
            </foreach>
        </if>
        group by b.cpdm
        ,a.type
        )
        select cpdm,case
        when cpdm !='GG' then
        cpdm || '_' || b.ywrq|| '_' || b.batch_no || '.OK'
        else
        'GG' || '_' || b.ywrq || '.OK'
        end as rtvDbfName
        ,b.batch_no batchNo
        from temp_qr a
        ,temp_log b
        where a.type = b.type
        and a.numbers = b.numbers
    </select>


    <resultMap id="tfundinfoMap" type="com.gtja.faRtvData.model.RtvTfundInfoDbfModel">
        <result column="L_FUNDID" property="l_fundid"></result>
        <result column="VC_CODE" property="vc_code"></result>
        <result column="VC_NAME" property="vc_name"></result>
        <result column="VC_FULLNAME" property="vc_fullname"></result>
        <result column="L_CLASS" property="l_class"></result>
        <result column="L_JJTZLX" property="l_jjtzlx"></result>
        <result column="TRUSTEE_OPER_ID" property="trustee_oper_id"></result>
        <result column="VC_BH " property="vc_bh"></result>
        <result column="VC_GLR" property="vc_glr"></result>
        <result column="VC_TGR" property="vc_tgr"></result>
        <result column="D_CREATE" property="d_create"></result>
        <result column="D_DESTORY" property="d_destory"></result>
        <result column="D_DQRQ" property="d_dqrq"></result>
        <result column="EN_MJJE" property="en_mjje"></result>
        <result column="EN_ZFE" property="en_zfe"></result>
    </resultMap>

    <select id="queryRtvTfundinfoList" parameterType="java.lang.String" resultMap="tfundinfoMap">
        select a.l_fundid
             , a.vc_code
             , a.vc_name
             , a.vc_fullname
             , a.l_class
             , a.l_jjtzlx
             , null                TRUSTEE_OPER_ID
             , null                vc_glr
             , null                VC_GLR
             , null                VC_TGR
             , b.D_CREATE
             , b.d_destory
             , a.D_DQRQ
             , round(a.EN_MJJE, 4) EN_MJJE
             , round(a.EN_ZFE, 4)  EN_ZFE
        from hsfa.tfundinfo@wbgz_query a
           , hsfa.tsysinfo@wbgz_query b
        where a.l_fundid = b.l_id
          and upper(a.vc_code) = #{cpdm}
    </select>


    <resultMap id="tjjxsflMap" type="com.gtja.faRtvData.model.RtvTjjxsflDbfModel">
        <result column="VC_FJJJDM" property="vc_fjjjdm"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_FJJJMC" property="vc_fjjjmc"></result>
        <result column="VC_FJJB" property="vc_fjjb"></result>
        <result column="D_KSRQ" property="d_ksrq"></result>
        <result column="D_JSRQ" property="d_jsrq"></result>
        <result column="VC_JSBZ" property="vc_jsbz"></result>
        <result column="D_TQJSRQ" property="d_tqjsrq"></result>
        <result column="EN_YQSY" property="en_yqsy"></result>
        <result column="L_YXBZ" property="l_yxbz"></result>
    </resultMap>

    <select id="queryRtvTjjxsflList" parameterType="java.lang.String" resultMap="tjjxsflMap">
        select a.vc_fjjjdm
             , a.l_ztbh
             , a.vc_fjjjmc
             , a.vc_fjjb
             , a.d_ksrq
             , a.d_jsrq
             , a.vc_jsbz
             , a.d_tqjsrq
             , round(a.en_yqsy, 6) en_yqsy
             , a.l_yxbz
        from hsfa.tjjxsfl@wbgz_query a
        where upper(a.VC_JJDM) = #{cpdm}
    </select>


    <resultMap id="ThkzhMap" type="com.gtja.faRtvData.model.RtvThkzhDbfModel">
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_ZH" property="vc_zh"></result>
        <result column="VC_MC" property="vc_mc"></result>
        <result column="VC_KMDM" property="vc_kmdm"></result>
    </resultMap>

    <select id="queryRtvThkzhList" parameterType="java.lang.String" resultMap="ThkzhMap">
        select l_ztbh
             , vc_zh
             , vc_mc
             , vc_kmdm
        from hsfa.thkzh@wbgz_query a,
             hsfa.tfundinfo@wbgz_query b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>

    <resultMap id="BalanceMap" type="com.gtja.faRtvData.model.RtvBalanceDbfModel">
        <result column="TRADEDATE" property="tradedate"></result>
        <result column="PD_ID" property="pd_id"></result>
        <result column="SUBJ_CODE" property="subj_code"></result>
        <result column="SUBJ_CRRC_CODE" property="subj_crrc_code"></result>
        <result column="SUBJ_NAME" property="subj_name"></result>
        <result column="VC_CODE_HS" property="vc_code_hs"></result>
        <result column="L_LEAF" property="l_leaf"></result>
        <result column="END_BAL_BC" property="end_bal_bc"></result>
        <result column="END_QTY_BAL" property="end_qty_bal"></result>
        <result column="END_BAL_OC" property="end_bal_oc"></result>
        <result column="SEC_ID" property="sec_id"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
    </resultMap>

    <!--    <select id="queryRtvBalanceList" parameterType="java.lang.String" resultMap="BalanceMap">-->
    <!--        select #{ywrq}                   as TRADEDATE-->
    <!--             , a.l_fundid                as PD_ID-->
    <!--             , a.vc_code                 as SUBJ_CODE-->
    <!--             , a.vc_jsbz                 as SUBJ_CRRC_CODE-->
    <!--             , substr(a.vc_name, 0, 254) as SUBJ_NAME-->
    <!--             , a.vc_code_hs-->
    <!--             , a.l_leaf-->
    <!--             , hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 0, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) as END_BAL_BC-->
    <!--        ,hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 1, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) as END_QTY_BAL-->
    <!--        ,hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 2, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) as END_BAL_OC-->
    <!--        ,a.l_sclb as SEC_ID-->
    <!--             , case-->
    <!--                   when length(a.vc_code) > 8 then-->
    <!--                       substr(a.vc_code, 9, length(a.vc_code))-->
    <!--                   else-->
    <!--                       ''-->
    <!--            end                          as vc_zqdm-->
    <!--        from hsfa.taccount@wbgz_query a-->
    <!--           , hsfa.tfundinfo@wbgz_query b-->
    <!--        where a.l_fundid = b.l_fundid-->
    <!--          and upper(b.vc_code) = #{cpdm}-->
    <!--          and (hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 1, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0 or-->
    <!--            hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 2, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0 or-->
    <!--            hsfa.sf_pub_kmye@wbgz_query(a.vc_code, 0, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0)-->
    <!--    </select>-->

    <resultMap id="GtjaAccountMap" type="com.gtja.faRtvData.model.RtvGtjaAccountDbfModel">
        <result column="L_FUNDID" property="l_fundid"></result>
        <result column="ZHSX" property="zhsx"></result>
        <result column="ZHLX" property="zhlx"></result>
        <result column="JGBM" property="jgbm"></result>
        <result column="ZJZH" property="zjzh"></result>
        <result column="ZT" property="zt"></result>
    </resultMap>

    <select id="queryRtvGtjaAccountList" resultMap="GtjaAccountMap">
        select b.l_fundid, a.zhsx, a.zhlx, a.jgbm, a.zjzh, a.zt
        from zctg.gtja_account a
           , hsfa.tfundinfo@wbgz_query b
        where a.cpdm = b.vc_code
          and a.jgbm in ('QS001', 'QS002')
          and zhsx = '资金账号'
          and upper(a.cpdm) = #{cpdm}
    </select>

    <resultMap id="TgpsdMap" type="com.gtja.faRtvData.model.RtvTgpsdDbfModel">
        <result column="L_ID" property="l_id"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
        <result column="L_SCLB" property="l_sclb"></result>
        <result column="EN_SDJG" property="en_sdjg"></result>
        <result column="EN_SDSL" property="en_sdsl"></result>
        <result column="D_SDBEGIN" property="d_sdbegin"></result>
        <result column="D_SDEND" property="d_sdend"></result>
    </resultMap>
    <!--    <select id="queryRtvTgpsdCommonList" resultMap="TgpsdMap">-->
    <!--        select a.l_id,A.L_ZTBH-->
    <!--             ,a.VC_ZQDM-->
    <!--             ,b.l_sclb,-->
    <!--             ,a.en_zqcb/a.l_sl EN_SDJG-->
    <!--             ,a.l_sl as EN_SDSL-->
    <!--             ,a.d_begin as  D_SDBEGIN-->
    <!--             ,a.d_jxr as  D_SDEND-->
    <!--        from hsfa.tgpsd_dsfgz@wbgz_query a,hsfa.tzqxx@wbgz_query b-->
    <!--        where a.l_zqnm = b.l_zqnm-->
    <!--    </select>-->
    <select id="queryRtvTgpsdList" parameterType="java.lang.String" resultMap="TgpsdMap">
        select a.l_id
             , A.L_ZTBH
             , a.VC_ZQDM
             , b.l_sclb
             , round(a.en_zqcb / a.l_sl, 4) EN_SDJG
             , round(a.l_sl, 4) as          EN_SDSL
             , a.d_begin        as          D_SDBEGIN
             , a.d_jxr          as          D_SDEND
        from hsfa.tgpsd_dsfgz@wbgz_query a
           , hsfa.tzqxx@wbgz_query b
           , hsfa.tfundinfo@wbgz_query c
        where a.l_zqnm = b.l_zqnm
          and a.l_ztbh = c.l_fundid
          and upper(c.vc_code) = #{cpdm}
    </select>


    <resultMap id="TxtcsMap" type="com.gtja.faRtvData.model.RtvTxtcsDbfModel">
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_CSDM" property="vc_csdm"></result>
        <result column="VC_CSZ" property="vc_csz"></result>
    </resultMap>
    <select id="queryRtvTxtcsList" parameterType="java.lang.String" resultMap="TxtcsMap">
        select l_ztbh
             , vc_csdm
             , substr(vc_csz, 0, 254) vc_csz
        from hsfa.txtcs@wbgz_query a,
             hsfa.tfundinfo@wbgz_query b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>
    <select id="queryRtvTxtcsCommonList" resultMap="TxtcsMap">
        select l_ztbh
             , vc_csdm
             , vc_csz
        from hsfa.txtcs@wbgz_query a
        where a.l_ztbh = 0
    </select>


    <resultMap id="TaccountMap" type="com.gtja.faRtvData.model.RtvTaccountDbfModel">
        <result column="L_FUNDID" property="l_fundid"></result>
        <result column="VC_CODE" property="vc_code"></result>
        <result column="VC_NAME" property="vc_name"></result>
        <result column="l_kind" property="l_kind"></result>
        <result column="VC_CODE_HS" property="vc_code_hs"></result>
        <result column="L_LEVEL" property="l_level"></result>
        <result column="VC_PARENT" property="vc_parent"></result>
        <result column="L_LEAF" property="l_leaf"></result>
        <result column="D_CREATE" property="d_create"></result>
        <result column="L_QUANTITY" property="l_quantity"></result>
        <result column="VC_FZHS" property="vc_fzhs"></result>
        <result column="L_SCLB" property="l_sclb"></result>
    </resultMap>
    <select id="queryRtvTaccountList" resultMap="TaccountMap">
        select a.l_fundid
             , a.vc_code
             , substr(a.vc_name, 0, 254) vc_name
             , a.l_kind
             , a.vc_code_hs
             , a.l_level
             , a.vc_parent
             , a.l_leaf
             , a.d_create
             , a.l_quantity
             , a.vc_fzhs
             , a.l_sclb
        from hsfa.taccount@wbgz_query a,
             hsfa.tfundinfo@wbgz_query b
        where a.l_fundid = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>

    <resultMap id="TyttxMap" type="com.gtja.faRtvData.model.RtvTyttxDbfModel">
        <result column="L_BH" property="l_bh"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_ZY" property="vc_zy"></result>
        <result column="L_JTFS" property="l_jtfs"></result>
        <result column="L_TS" property="l_ts"></result>
        <result column="EN_JTFL" property="en_jtfl"></result>
        <result column="D_BEGIN" property="d_begin"></result>
        <result column="D_END" property="d_end"></result>
        <result column="VC_DFKM" property="vc_dfkm"></result>
        <result column="EN_JE" property="en_je"></result>
        <result column="VC_JFKM" property="vc_jfkm"></result>
    </resultMap>
    <select id="queryRtvTyttxList" resultMap="TyttxMap">
        select a.l_bh
             , a.l_ztbh
             , a.vc_zy
             , a.l_jtfs
             , a.l_ts
             , round(a.en_jtfl, 15) en_jtfl
             , a.d_begin
             , a.d_end
             , a.vc_dfkm
             , round(a.en_je, 4)    en_je
             , a.vc_jfkm
        from hsfa.tyttx@wbgz_query a,
             hsfa.tfundinfo@wbgz_query b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>


    <resultMap id="TllMap" type="com.gtja.faRtvData.model.RtvTllDbfModel">
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_ZH" property="vc_zh"></result>
        <result column="D_BEGIN" property="d_begin"></result>
        <result column="L_JZRQ" property="l_jzrq"></result>
        <result column="L_JXTS" property="l_jxts"></result>
        <result column="VC_JXLB" property="vc_jxlb"></result>
        <result column="D_END" property="d_end"></result>
        <result column="EN_NLL" property="en_nll"></result>
    </resultMap>
    <select id="queryRtvTllList" resultMap="TllMap">
        select a.l_ztbh
             , a.vc_zh
             , a.d_begin
             , a.l_jzrq
             , a.l_jxts
             , a.vc_jxlb
             , a.d_end
             , round(a.en_nll, 10) en_nll
        from hsfa.tll@wbgz_query a,
             hsfa.tfundinfo@wbgz_query b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>

    <resultMap id="TgdxwMap" type="com.gtja.faRtvData.model.RtvTgdxwDbfModel">
        <result column="L_SCLB" property="l_sclb"></result>
        <result column="VC_XWDM" property="vc_xwdm"></result>
        <result column="VC_XNAME" property="vc_xname"></result>
        <result column="VC_QSBH" property="vc_qsbh"></result>
        <result column="VC_CODE" property="vc_code"></result>
        <result column="VC_NAME" property="vc_name"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="VC_ZJZH" property="vc_zjzh"></result>
        <result column="D_BEGIN" property="d_begin"></result>
        <result column="L_JSJG" property="l_jsjg"></result>
        <result column="L_TQBZ" property="l_tqbz"></result>
    </resultMap>
    <select id="queryRtvTgdxwList" resultMap="TgdxwMap">
        select a.l_sclb
             , a.vc_code
             , a.vc_name
             , a.vc_qsbh
             , a.vc_code
             , a.vc_name
             , a.l_ztbh
             , a.vc_zjzh
             , a.d_begin
             , a.l_jsjg
             , a.l_tqbz
        from hsfa.tgdxw@wbgz_query a
           , hsfa.tfundinfo@wbgz_query b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
    </select>

    <!--BROKER_-->
    <resultMap id="TqsMap" type="com.gtja.faRtvData.model.RtvTqsDbfModel">
        <result column="CODE" property="code"></result>
        <result column="NAME" property="name"></result>
        <result column="FULL_NAME" property="full_name"></result>
    </resultMap>

    <select id="queryRtvTqsList" resultMap="TqsMap">
        select a.vc_bh as code
             , a.vc_mc as name
             , a.vc_mc as full_name
        from hsfa.tqs@wbgz_query a
    </select>


    <update id="updateRtvCpqdGzrq">
        update GTJA_SSGZ_CPQD t
        set (t.gzrq, t.gzsj) =
                (select to_char(max(d_ywrq), 'yyyymmdd') gzrq
                      , max(d_qrsj)                      gzsj
                 from hsfa.tfundinfo@wbgz_query a
                    , hsfa.ttmpgzb_index@wbgz_query b
                 where a.l_fundid = b.l_ztbh
                   and b.l_sfqr = 1
                   and a.vc_code = t.cpdm
                   and t.status = 0
                 group by l_ztbh
                        , vc_code)
    </update>

 <select id="quertNotCreateGGFile"  resultType="com.gtja.faRtvData.model.RtvCommonPdfModel">
     select t.file_name as rtvDbfName,t.type
     from fbzx.gtja_ssgz_file_qd t
     where t.status = 0
       and t.type = 0
       AND NOT EXISTS(select 1
                      from gtja_ssgz_file_out_log b
                      where t.file_name = b.file_name
                        and b.ywrq = #{ywrq})
 </select>


</mapper>