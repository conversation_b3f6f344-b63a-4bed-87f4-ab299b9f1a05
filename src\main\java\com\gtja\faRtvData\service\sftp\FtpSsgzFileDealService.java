package com.gtja.faRtvData.service.sftp;

import com.gtja.faRtvData.model.NfsInfoModel;
import com.gtja.faRtvData.model.SftpInfoModel;

public interface FtpSsgzFileDealService {
    public String FtpSsgzFilePut(String sourcePath, String targetPath,String fileName, SftpInfoModel sftpSendConfig) throws Exception;

    public String SsgzFilePutByLogForSftp( String ywrq,String targetPath, SftpInfoModel sftpSendConfig) throws Exception;

    public String SsgzFilePutByLogForCifs( String ywrq,String targetPath, NfsInfoModel sftpSendConfig) throws Exception;
    boolean downloadFile(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpInfoModel) throws Exception;

}
