package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *   概要说明：科目余额信息
 */
@Data
public class RtvBalanceDbfModel {
    @DbfAnnotation(fieldName = "TRADEDATE", fieldLength = 8)
    private String tradedate;
    @DbfAnnotation(fieldName = "PD_ID", fieldLength = 32)
    private String pd_id;
    @DbfAnnotation(fieldName = "SUBJ_CODE", fieldLength = 50)
    private String subj_code;
    @DbfAnnotation(fieldName = "SUBJ_CRRC", fieldLength = 30)
    private String subj_crrc_code;
    @DbfAnnotation(fieldName = "SUBJ_NAME", fieldLength = 254)
    private String subj_name;
    @DbfAnnotation(fieldName = "VC_CODE_HS", fieldLength = 32)
    private String vc_code_hs;
    @DbfAnnotation(fieldName = "L_LEAF", fieldLength = 2)
    private String l_leaf;
    @DbfAnnotation(fieldName = "END_BAL_BC", fieldLength = 32)
    private String end_bal_bc;
    @DbfAnnotation(fieldName = "END_QTY", fieldLength = 32)
    private String end_qty_bal;
    @DbfAnnotation(fieldName = "END_BAL_OC", fieldLength = 32)
    private String end_bal_oc;
    @DbfAnnotation(fieldName = "SEC_ID", fieldLength = 4)
    private String sec_id;
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;
}
