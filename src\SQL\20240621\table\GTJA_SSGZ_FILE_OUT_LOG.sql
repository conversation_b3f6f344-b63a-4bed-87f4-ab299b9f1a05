-- Create table
create table GTJA_SSgz_FILE_OUT_LOG
(
    id          VARCHAR2(64),
    ywrq        VARCHAR2(8),
    cpdm        VARCHAR2(20),
    file_name   VARCHAR2(128),
    file_path   VARCHAR2(200),
    create_date DATE default sysdate,
    status      VARCHAR2(2),
    batch_no     VARCHAR2(2),
    update_date DATE
);
-- Add comments to the columns
comment on column GTJA_SSGZ_FILE_OUT_LOG.id
  is 'log_id';
comment on column GTJA_SSgz_FILE_OUT_LOG.ywrq
  is '业务日期';
comment on column GTJA_SSgz_FILE_OUT_LOG.cpdm
  is '产品代码';
comment on column GTJA_SSgz_FILE_OUT_LOG.file_name
  is '文件名成';
comment on column GTJA_SSgz_FILE_OUT_LOG.file_path
  is '文件路径';
comment on column GTJA_SSgz_FILE_OUT_LOG.create_date
  is '创建时间';
comment on column GTJA_SSgz_FILE_OUT_LOG.status
  is '状态 0-成功生成';
comment on column GTJA_SSgz_FILE_OUT_LOG.batch_no
  is '批次号';
comment on column GTJA_SSGZ_FILE_OUT_LOG.update_date
  is '推送时间';

-- Create/Recreate indexes
create index IDX_GTJA_SSgz_FILE_OUT_LOG_1 on GTJA_SSgz_FILE_OUT_LOG (id);
