package com.gtja.faRtvData.downLoad;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.gtja.faRtvData.dao.sjzx.RtvFracResultDao;
import com.gtja.faRtvData.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.config.SaslConfigs;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Properties;

/**
 * @ProjectName: com.gtja.faData.service
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2020/12/2213:37
 * @Version :1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class KafkaConsumerTest {

    @Autowired
    private  RtvFracResultDao rtvFracResultDao;

    @Autowired
    KafkaInfoModel kafkaInfoModel;

    @Test
    public void main() {
        KafkaConsumer<String, String> consumer;
        Properties properties = new Properties();
        properties.setProperty("bootstrap.servers", "************:9093");//************:9092 ************:9092  ***********
        properties.setProperty("group.id", "fa-rtv-data");
        properties.put("enable.auto.commit", "false");
        properties.put("auto.offset.reset", "earliest");
        properties.put("auto.commit.interval.ms", "1000");
        properties.put("session.timeout.ms", "30000");
        properties.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        properties.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        properties.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        properties.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"sszcKafkaUser\" password=\"sszcKafkaUser@2024\";");


        System.out.println(properties.get("sasl.jaas.config"));
        // properties.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"sszcKafkaUser\" password=\"sszcKafkaUser@2024\";");
        properties.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"" +
                kafkaInfoModel.getUserName() + "\\ password=\"" +
                kafkaInfoModel.getPassWord() + "\";");


        System.out.println(properties.get("sasl.jaas.config"));


        consumer = new KafkaConsumer<String, String>(properties);
        //制定订阅主题
        consumer.subscribe(Arrays.asList("G_P_USER_FRAC_FV_RESULT"));

        while (true) {
            ConsumerRecords<String, String> records = consumer.poll(100);
            for (ConsumerRecord<String, String> record : records) {
                //System.out.println("offset=%d,key=$s,value= %s",record.offset(),record.key(),record.value());
                System.out.println("接收的消息：1.offset：" + record.offset() + "；value:" + record.value());
                try {
                    JSONObject jsonObject1 = JSONObject.parseObject(record.value());
                    GtjaSsgzMqResultModel resultDetailModel = (GtjaSsgzMqResultModel) JSONObject.toJavaObject(jsonObject1, GtjaSsgzMqResultModel.class);

                    GtjaSsgzMqRecLogModel logInfo = new GtjaSsgzMqRecLogModel();
                    BeanUtils.copyProperties(resultDetailModel, logInfo);
                    logInfo.setFv_result_hold_offset(resultDetailModel.getCONTENT().getFv_result_hold_offset());
                    logInfo.setFv_result_hold_total(resultDetailModel.getCONTENT().getFv_result_hold_total());

                    List<GtjaSsgzAssetModel> assetModels = resultDetailModel.getCONTENT().getFv_result_asset();
                    List<GtjaSsgzPointModel> pointModels = resultDetailModel.getCONTENT().getFv_result_indic();
                    List<GtjaSsgzPositionModel> positionModels = resultDetailModel.getCONTENT().getFv_result_hold();

                    rtvFracResultDao.insertMqLog(logInfo);
//                    if (assetModels != null && assetModels.size() > 0) {
//                        rtvFracResultDao.insertAsset(assetModels, logInfo.getMSG_ID());
//                    }
//                    if (pointModels != null && pointModels.size() > 0) {
//                        rtvFracResultDao.insertPoint(pointModels, logInfo.getMSG_ID());
//                    }
//                    if (positionModels != null && positionModels.size() > 0) {
//                        rtvFracResultDao.insertPosition(positionModels, logInfo.getMSG_ID());
//                    }
                    System.out.println(resultDetailModel);
                } catch (JSONException e) {
                    log.info("****数据转换失败***" + "消息转JSON失败");
                } catch (Exception e) {
                    log.info("****数据转换失败***" + record.value());
                }
            }
        }


//        while (true) {
//            ConsumerRecords<String, String> records = consumer.poll(500);
//            log.debug("FA-RTV OnMessage::records = [{}], consumer = [{}]", records, consumer);
//            try {
//                records.forEach(record -> {
//                    System.out.println("接收的消息：1.offset：" + record.offset() + "；value:" + record.value());
//                    Optional<?> kafkaMessage = Optional.ofNullable(record.value());
//                    String json = record.value();
//                    try {
//                        JSONObject jsonObject1 = JSON.parseObject(json);
//                    } catch (Exception e) {
//                        log.info("****数据转换失败***" + kafkaMessage.toString());
//                    }
//                    //String message = JSON.parseObject(kafkaMessage.get().toString(), String.class);
//                    try {
//                        JSONObject jsonObject2 = JSONObject.parseObject(json);
//                    } catch (Exception e) {
//                        log.info("****数据转换失败***" + kafkaMessage.toString());
//                    }
//
//                    log.info("***开始消费****" + kafkaMessage.toString());
//                    // JsonObject josn=kafkaMessage.get("CONTENT");
//                    JSONObject jsonObject = JSONObject.parseObject(String.valueOf(kafkaMessage.toString()));
//
//                    // JSONObject jsonObject= JSONObject.parseObject(kafkaMessage.toString());
//                    Map<String, Class> classMap = new HashMap<String, Class>();
//                    classMap.put("fv_result_indic", GtjaSsgzPointModel.class);
//                    classMap.put("fv_result_asset", GtjaSsgzAssetModel.class);
//                    classMap.put("fv_result_hold", GtjaSsgzPositionModel.class);
//                    GtjaSsgzMqResultDetailModel resultDetailModel = (GtjaSsgzMqResultDetailModel) JSONObject.toJavaObject(jsonObject, GtjaSsgzMqResultDetailModel.class);
//                    System.out.println(resultDetailModel);
//
//                    //  List list = gson.fromJson(kafkaMessage.toString(), new TypeToken<List>() {}.getType());
////                    Map<String, Object> map = new HashMap<>();
////                    map = gson.fromJson(kafkaMessage.toString(), map.getClass());
//                    GtjaSsgzMqRecLogModel logInfo = null;
//                    try {
//                        logInfo = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzMqRecLogModel.class);
//                    } catch (Exception e) {
//                        log.info("****数据转换失败***" + kafkaMessage.toString());
//                    }
//
//                    GtjaSsgzAssetModel assetModel = null;
//                    try {
//                        assetModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzAssetModel.class);
//                    } catch (Exception e) {
//                        log.info("****数据转换失败***" + kafkaMessage.toString());
//                    }
//
//                    GtjaSsgzPointModel pointModel = null;
//                    try {
//                        pointModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzPointModel.class);
//                    } catch (Exception e) {
//                        log.info("****数据转换失败***" + kafkaMessage.toString());
//                    }
//
//
//                    GtjaSsgzPositionModel positionModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzPositionModel.class);
//                    rtvFracResultDao.insertMqLog(logInfo);
//                    rtvFracResultDao.insertAsset(assetModel);
//                    rtvFracResultDao.insertPoint(pointModel);
//                    rtvFracResultDao.insertPosition(positionModel);
//                });
//                // 提交kafka offset
//                //  consumer.commitSync();
//            } catch (Exception e) {
//                log.error("Kafka监听异常", e);
//            }
//            log.debug("onMessage::records = [{}], consumer = [{}]", records, consumer);
//
//
//        }


    }

    @Test
    public void test002() {
        String json = "{\"TARGET\":\"ZCTG\",\"MSG_GROUP_SNO\":1,\"MSG_ID\":\"*****************01\",\"MSG_SUBTYPE\":\"FV_RESULT\",\"MSG_TYPE\":\"FV_RESULT\",\"MSG_GROUP_ID\":\"*****************\",\"SRC\":\"146\",\"SEND_TIME\":101131,\"SRC_IP\":\"***********\",\"CONTENT\":{\"fv_result_asset\":[{\"f606101\":0.0,\"f630299\":0.0,\"accting_entity\":\"************\",\"f410431\":0.0,\"f220501\":0.0,\"f820306\":0.0,\"algo_comb\":\"*\",\"f641101\":0.0,\"f102101\":0.0,\"account_set_no\":\"\",\"f220902\":0.0,\"f400103\":0.0,\"f220901\":82.4,\"f400101\":0.0,\"f220903\":0.0,\"f400102\":0.0,\"f670101\":0.0,\"f300331\":0.0,\"exch_rate\":0.0,\"f100201\":0.0,\"f300330\":0.0,\"f223201\":0.0,\"f160101\":0.0,\"f680201\":0.0,\"f110401\":0.0,\"f200101\":41006.28,\"f102111\":0.0,\"f102112\":0.0,\"f641199\":0.0,\"f102113\":0.0,\"f100211\":0.0,\"f122101\":0.0,\"f300321\":0.0,\"f640701\":213.**************,\"f300320\":0.0,\"f120201\":0.0,\"f100212\":0.0,\"f640301\":0.0,\"f100213\":0.0,\"f100214\":0.0,\"f222101\":0.0,\"f601198\":0.0,\"f111001\":0.0,\"f410411\":0.0,\"f220601\":0.0,\"f610101\":-228237.6,\"f220201\":0.0,\"oneid\":\"************\",\"f300318\":0.0,\"f300317\":0.0,\"ccy_code\":\"CNY\",\"f300319\":0.0,\"f300314\":0.0,\"f300313\":0.0,\"f300316\":0.0,\"f300315\":0.0,\"f300310\":0.0,\"f601104\":0.0,\"f300312\":0.0,\"busi_date\":********,\"f300311\":0.0,\"f601101\":0.0,\"f601102\":0.0,\"f224199\":0.0,\"f410421\":0.0,\"f410301\":0.0,\"f110301\":4000.0,\"f410302\":0.0,\"f300307\":0.0,\"f103101\":0.0,\"f151101\":0.0,\"f300306\":0.0,\"f400199\":0.0,\"f130301\":0.0,\"f300309\":0.0,\"accting_unit\":\"*\",\"f300308\":0.0,\"f300303\":0.0,\"f300302\":0.0,\"f300305\":0.0,\"f300304\":0.0,\"f160601\":0.0,\"f300301\":-243125.2400000001,\"f660599\":0.0,\"f601199\":0.0,\"f640401\":0.0,\"asset_unit\":\"*\",\"f224101\":0.0,\"f220701\":0.0,\"f220301\":0.0,\"f400301\":0.0,\"f103111\":0.0,\"f150101\":0.0,\"f120499\":0.0,\"f660501\":0.0,\"hold_unit\":\"*\",\"f410401\":0.0,\"f410402\":0.0,\"f110201\":40666.0,\"f610198\":0.0,\"f110601\":0.0,\"f401101\":0.0,\"f210101\":0.0,\"f103121\":0.0,\"f401102\":0.0,\"f170101\":0.0,\"f120401\":0.0,\"f640501\":0.0,\"f220801\":0.0,\"f611198\":0.0,\"f611199\":0.0,\"f220401\":0.0,\"f152101\":0.0,\"f103131\":0.0,\"f400201\":0.0,\"f611107\":0.0,\"f223101\":0.0,\"f611108\":0.0,\"f611101\":0.0,\"f611102\":0.0,\"f611106\":0.0,\"f110501\":11096.4,\"f611103\":0.0,\"f611104\":0.0,\"f110901\":0.0,\"f103141\":0.0,\"f120301\":0.0,\"f120701\":0.0,\"f640601\":0.0}],\"fv_result_hold_total\":13,\"fv_result_hold\":[{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":2.6,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":2.6,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"821001.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"821001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"150080.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"150080\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":201.**************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":201.**************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-182280.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":200000.0,\"s94003\":0.0,\"s70110\":17720.0,\"s90201\":2000.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":2000.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-182280.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":200000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":5.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":5.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9114.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":886.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9114.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":1.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":1.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.68,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.68,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"102258.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"102258\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0}],\"fv_result_hold_offset\":13},\"MSG_GROUP_CNT\":1,\"SEND_DATE\":********}";
        try {
            JSONObject jsonObject1 = JSONObject.parseObject(json);
            GtjaSsgzMqResultModel resultDetailModel = (GtjaSsgzMqResultModel) JSONObject.toJavaObject(jsonObject1, GtjaSsgzMqResultModel.class);
            GtjaSsgzMqRecLogModel logInfo = new GtjaSsgzMqRecLogModel();
            BeanUtils.copyProperties(resultDetailModel, logInfo);
            List<GtjaSsgzAssetModel> assetModels = resultDetailModel.getCONTENT().getFv_result_asset();
            List<GtjaSsgzPointModel> pointModels = resultDetailModel.getCONTENT().getFv_result_indic();
            List<GtjaSsgzPositionModel> positionModels = resultDetailModel.getCONTENT().getFv_result_hold();

            // logInfo.setFv_result_hold_offset(resultDetailModel.getCONTENT().getFv_result_hold_offset());
            // logInfo.setFv_result_hold_total(resultDetailModel.getCONTENT().getFv_result_hold_total());
            String date = logInfo.getSEND_DATE() + "." + logInfo.getSEND_TIME();

            //rtvFracResultDao.insertMqLog();
            try {
                rtvFracResultDao.insertAsset(assetModels, logInfo.getMSG_ID(), date);
            } catch (Exception e) {
                log.info("****数据转换失败***" + e.getMessage());
            }
            try {
                rtvFracResultDao.insertPoint(pointModels, logInfo.getMSG_ID(), date);
            } catch (Exception e) {
                log.info("****数据转换失败***" + e.getMessage());
            }
            try {
                rtvFracResultDao.insertPosition(positionModels, logInfo.getMSG_ID(), date);
            } catch (Exception e) {
                log.info("****数据转换失败***" + e.getMessage());
            }

            System.out.println(resultDetailModel);
        } catch (Exception e) {
            log.info("****数据转换失败***" + e.getMessage());
        } finally {
            log.info("根据ztbh更新指标数据的产品代码：");
            rtvFracResultDao.updateAssetCpdm();
            rtvFracResultDao.updatePointCpdm();
            rtvFracResultDao.updatePositionCpdm();
        }
    }

}
