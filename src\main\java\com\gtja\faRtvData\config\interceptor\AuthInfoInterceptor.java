package com.gtja.faRtvData.config.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> zhangkai
 * @date : 9:30 2021/6/11
 * @desc
 */
@Component
@Slf4j
public class AuthInfoInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String userCode = request.getHeader("User-Code");
        if(StringUtils.isNotEmpty(userCode)){
            ThreadLocalUtil.addCurrentUser(userCode);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        ThreadLocalUtil.rmCurrentUser();
    }
}
