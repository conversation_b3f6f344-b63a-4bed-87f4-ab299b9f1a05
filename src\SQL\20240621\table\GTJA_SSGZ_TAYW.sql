-- Create table
create table GTJA_SSgz_TAYW
(
    create_date                    DATE default sysdate,
    busi_date                      NUMBER not null,
    busi_flow_no                   NUMBER not null,
    account_set_no                 VARCHAR2(32),
    prod_code                      VARCHAR2(20),
    lv_prod_code                   VARCHAR2(20),
    digest_id                      VARCHAR2(32),
    ccy_code                       CHAR(3),
    trd_apply_date                 NUMBER,
    confirm_date                   NUMBER,
    settle_date                    NUMBER,
    match_share                    NUMBER(18,4),
    match_amt                      NUMBER(18,4),
    confirm_amt                    NUMBER(18,4),
    divd_amt                       NUMBER(18,4),
    fee_bcf                        NUMBER(18,4),
    fee_ghf                        NUMBER(18,4),
    fee_gjjzc                      NUMBER(18,4),
    back_apply_fee                 NUMBER(18,4),
    fee_jyf                        NUMBER(18,4),
    fee_qtf_use                    NUMBER(18,4),
    frz_amt                        NUMBER(18,4),
    inc_tax_amt                    NUMBER(18,4),
    minus_amt                      NUMBER(18,4),
    pay_performance                NUMBER(18,4),
    plus_amt                       NUMBER(18,4),
    then_invest_bonus_cash_amt     NUMBER(18,4),
    then_invest_price              NUMBER(18,12),
    then_invest_share              NUMBER(18,4),
    share_bal                      NUMBER(18,4),
    profit_loss_equaliz_un_realize NUMBER(18,4),
    fee_yhs                        NUMBER(18,4),
    tranfin_yield                  NUMBER(18,4),
    tranfin_share                  NUMBER(18,4),
    tranf_out_yield                NUMBER(18,4),
    tranf_out_share                NUMBER(18,4),
    un_pay_yield                   NUMBER(18,4),
    share_lv                       CHAR(1),
    share_type                     CHAR(2),
    check_status                   VARCHAR2(2),
    send_data                      DATE,
    send_status                    VARCHAR2(2),
    check_data                     DATE,
    oper                           VARCHAR2(64)
);
-- Add comments to the columns
comment
on column GTJA_SSgz_TAYW.create_date
  is '创建时间';
comment
on column GTJA_SSgz_TAYW.busi_date
  is '业务日期';
comment
on column GTJA_SSgz_TAYW.busi_flow_no
  is '流水号';
comment
on column GTJA_SSgz_TAYW.account_set_no
  is '账套号';
comment
on column GTJA_SSgz_TAYW.digest_id
  is '业务摘要代码';
comment
on column GTJA_SSgz_TAYW.ccy_code
  is '币种代码';
comment
on column GTJA_SSgz_TAYW.trd_apply_date
  is '交易申请日';
comment
on column GTJA_SSgz_TAYW.confirm_date
  is '确认日期';
comment
on column GTJA_SSgz_TAYW.settle_date
  is '交收日期';
comment
on column GTJA_SSgz_TAYW.match_share
  is '成交份额';
comment
on column GTJA_SSgz_TAYW.match_amt
  is '成交金额';
comment
on column GTJA_SSgz_TAYW.confirm_amt
  is '确认金额';
comment
on column GTJA_SSgz_TAYW.divd_amt
  is '分红金额';
comment
on column GTJA_SSgz_TAYW.fee_bcf
  is '补差费';
comment
on column GTJA_SSgz_TAYW.fee_ghf
  is '过户费';
comment
on column GTJA_SSgz_TAYW.fee_gjjzc
  is '归基金资产的费用';
comment
on column GTJA_SSgz_TAYW.back_apply_fee
  is '后端申购费';
comment
on column GTJA_SSgz_TAYW.fee_jyf
  is '交易费用';
comment
on column GTJA_SSgz_TAYW.fee_qtf_use
  is '其他费用';
comment
on column GTJA_SSgz_TAYW.frz_amt
  is '冻结金额';
comment
on column GTJA_SSgz_TAYW.inc_tax_amt
  is '所得税金额';
comment
on column GTJA_SSgz_TAYW.minus_amt
  is '调减金额';
comment
on column GTJA_SSgz_TAYW.pay_performance
  is '业绩报酬';
comment
on column GTJA_SSgz_TAYW.plus_amt
  is '调增金额';
comment
on column GTJA_SSgz_TAYW.then_invest_bonus_cash_amt
  is '再投资红利金额';
comment
on column GTJA_SSgz_TAYW.then_invest_price
  is '再投资价格';
comment
on column GTJA_SSgz_TAYW.then_invest_share
  is '再投资份额';
comment
on column GTJA_SSgz_TAYW.share_bal
  is '份额余额';
comment
on column GTJA_SSgz_TAYW.profit_loss_equaliz_un_realize
  is '损益平准金未实现';
comment
on column GTJA_SSgz_TAYW.fee_yhs
  is '印花税';
comment
on column GTJA_SSgz_TAYW.tranfin_yield
  is '转入收益';
comment
on column GTJA_SSgz_TAYW.tranfin_share
  is '转入份额';
comment
on column GTJA_SSgz_TAYW.tranf_out_yield
  is '转出收益';
comment
on column GTJA_SSgz_TAYW.tranf_out_share
  is '转出份额';
comment
on column GTJA_SSgz_TAYW.un_pay_yield
  is '未付收益';
comment
on column GTJA_SSgz_TAYW.share_lv
  is '份额级别';
comment
on column GTJA_SSgz_TAYW.share_type
  is '份额类别';
comment
on column GTJA_SSgz_TAYW.check_status
  is '复核状态';
comment
on column GTJA_SSgz_TAYW.send_data
  is '发送时间';
comment
on column GTJA_SSgz_TAYW.send_status
  is '发送状态';
comment
on column GTJA_SSgz_TAYW.check_data
  is '复核时间';
comment
on column GTJA_SSgz_TAYW.oper
  is '操作人';
