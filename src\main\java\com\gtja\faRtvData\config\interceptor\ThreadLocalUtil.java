package com.gtja.faRtvData.config.interceptor;

/**
 * <AUTHOR> zhangkai
 * @date : 9:36 2021/6/11
 * @desc   保存userCode ThreadLoacl 在拦截操作 添加 删除 用户数据
 */
public class ThreadLocalUtil {
    private static final ThreadLocal<String> userThreadLocal = new ThreadLocal<>();


    public static void addCurrentUser(String userCode){
        userThreadLocal.set(userCode);
    }

    public static String getCurrentUser(){
      return userThreadLocal.get();
    }

    public static void rmCurrentUser(){
        userThreadLocal.remove();
    }
}
