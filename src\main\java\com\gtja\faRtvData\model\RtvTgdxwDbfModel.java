package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

@Data
public class RtvTgdxwDbfModel {
    @DbfAnnotation(fieldName = "L_SCLB", fieldLength = 6)
    private String l_sclb;
    @DbfAnnotation(fieldName = "VC_XWDM", fieldLength = 30)
    private String vc_xwdm;
    @DbfAnnotation(fieldName = "VC_XNAME", fieldLength = 50)
    private String vc_xname;

    @DbfAnnotation(fieldName = "VC_QSBH", fieldLength = 4)
    private String vc_qsbh;
    @DbfAnnotation(fieldName = "L_TQBZ", fieldLength = 4)
    private String l_tqbz;
    @DbfAnnotation(fieldName = "VC_CODE", fieldLength = 20)
    private String vc_code;
    @DbfAnnotation(fieldName = "VC_NAME", fieldLength = 100)
    private String vc_name;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 6)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_ZJZH", fieldLength = 28)
    private String vc_zjzh;
    @DbfAnnotation(fieldName = "D_BEGIN", fieldLength = 16)
    private String d_begin;
    @DbfAnnotation(fieldName = "L_JSJG", fieldLength = 1)
    private String l_jsjg;
}
