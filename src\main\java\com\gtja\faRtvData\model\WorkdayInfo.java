package com.gtja.faRtvData.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:wuguanqing
 * @Description:
 * @Date:2021/7/27 13:49
 * @Modifird BY:
 */
@Data
@TableName("FBZX.REF_WORKDAY_INFO")
@Accessors(chain = true)
public class WorkdayInfo extends Model<WorkdayInfo> {
    /*
    日期
     */
    @TableField(value = "cal_date")
    private String calDate;
    /*
    工作日标志，1：交易日，0：非交易日
     */
    @TableField(value = "workday_sign")
    private int workdaySign;
    /*
    当年交易日顺序
     */
    @TableField(value = "workday_order")
    private int workdayOrder;
    /*
    最近交易日
     */
    @TableField(value = "recent_workday")
    private String recentWorkday;
    /*
    上个交易日
     */
    @TableField(value = "last_workday")
    private String lastWorkday;
    /*
    下个交易日
     */
    @TableField(value = "next_workday")
    private String nextWorkday;
    /*
    是否为本周最后一个交易日，1：是，0：否
     */
    @TableField(value = "is_lastworkday_week")
    private int isLastworkdayWeek;
    /*
    是否为本月最后一个交易日，1：是，0：否
     */
    @TableField(value = "is_lastworkday_month")
    private int isLastworkdayMonth;
    /*
    是否为本季度最后一个交易日，1：是，0：否
     */
    @TableField(value = "is_lastworkday_quar")
    private int isLastworkdayQuar;
    /*
    是否为本年度最后一个交易日，1：是，0：否
     */
    @TableField(value = "is_lastworkday_year")
    private int isLastworkdayYear;
    /*
    是否为本周第一个交易日，1：是，0：否
     */
    @TableField(value = "is_firworkday_week")
    private int isFirworkdayWeek;
    /*
    是否为本月第一个交易日，1：是，0：否
     */
    @TableField(value = "is_firworkday_month")
    private int isFirworkdayMonth;
    /*
    是否为本季度第一个交易日，1：是，0：否
     */
    @TableField(value = "is_firworkday_quar")
    private int isFirworkdayQuar;
    /*
    是否为本年度第一个交易日，1：是，0：否
     */
    @TableField(value = "is_firworkday_year")
    private int isFirworkdayYear;
    /*
    本周第一个交易日
     */
    @TableField(value = "firworkday_week")
    private String firworkdayWeek;
    /*
    本月第一个交易日
     */
    @TableField(value = "firworkday_month")
    private String firworkdayMonth;
    /*
    本季度第一个交易日
     */
    @TableField(value = "firworkday_quar")
    private String firworkdayQuar;
    /*
    本年度第一个交易日
     */
    @TableField(value = "firworkday_year")
    private String firworkdayYear;
    /*
    本周最后一个交易日
     */
    @TableField(value = "lastworkday_week")
    private String lastworkdayWeek;
    /*
    本月最后一个交易日
     */
    @TableField(value = "lastworkday_month")
    private String lastworkdayMonth;
    /*
    本季度最后一个交易日
     */
    @TableField(value = "lastworkday_quar")
    private String lastworkdayQuar;
    /*
    本年度最后一个交易日
     */
    @TableField(value = "lastworkday_year")
    private String lastworkdayYear;
    /*
    本周第一个自然日
     */
    @TableField(value = "firstday_week")
    private String firstdayWeek;
    /*
    本月第一个自然日
     */
    @TableField(value = "firstday_month")
    private String firstdayMonth;
    /*
    本季度第一个自然日
     */
    @TableField(value = "firstday_quar")
    private String firstdayQuar;
    /*
    本年度第一个自然日
     */
    @TableField(value = "firstday_year")
    private String firstdayYear;
    /*
    本周最后一个自然日
     */
    @TableField(value = "lastday_week")
    private String lastdayWeek;
    /*
    本月最后一个自然日
     */
    @TableField(value = "lastday_month")
    private String lastdayMonth;
    /*
    本季度最后一个自然日
     */
    @TableField(value = "lastday_quar")
    private String lastdayQuar;
    /*
    本年度最后一个自然日
     */
    @TableField(value = "lastday_year")
    private String lastdayYear;
    /*
    本周工作日顺序
     */
    @TableField(value = "week_order")
    private String weekOrder;
    /*
    本月工作日顺序
     */
    @TableField(value = "month_order")
    private String monthOrder;
    /*
    本季度工作日顺序
     */
    @TableField(value = "quar_order")
    private String quarOrder;
}
