package com.gtja.faRtvData.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
@Data
public class GtjaSsgzTaywModel  implements Serializable {

    private String type;
    private String prod_code;

    private String lv_prod_code;

    private Date create_date;

    private BigDecimal busi_date;

    private String busi_flow_no;

    private String account_set_no;

    private String digest_id;

    private String ccy_code;

    private BigDecimal trd_apply_date;

    private BigDecimal confirm_date;

    private BigDecimal settle_date;

    private BigDecimal match_share;

    private BigDecimal match_amt;

    private BigDecimal confirm_amt;

    private BigDecimal divd_amt;

    private BigDecimal fee_bcf;

    private BigDecimal fee_ghf;

    private BigDecimal fee_gjjzc;

    private BigDecimal back_apply_fee;

    private BigDecimal fee_jyf;

    private BigDecimal fee_qtf_use;

    private BigDecimal frz_amt;

    private BigDecimal inc_tax_amt;

    private BigDecimal minus_amt;

    private BigDecimal pay_performance;

    private BigDecimal plus_amt;

    private BigDecimal then_invest_bonus_cash_amt;

    private BigDecimal then_invest_price;

    private BigDecimal then_invest_share;

    private BigDecimal share_bal;

    private BigDecimal profit_loss_equaliz_un_realize;

    private BigDecimal fee_yhs;

    private BigDecimal tranfin_yield;

    private BigDecimal tranfin_share;

    private BigDecimal tranf_out_yield;

    private BigDecimal tranf_out_share;

    private BigDecimal un_pay_yield;

    private String share_lv;

    private String share_type;

    private String check_status;

    private Date send_data;

    private String send_status;

    private Date check_data;

    private String oper;

}