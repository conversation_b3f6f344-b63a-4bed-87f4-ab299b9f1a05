package com.gtja.faRtvData.encryption;

import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;

/**
 * @ClassName: encryptionTest
 * @anthor: Magic
 * @create: 2025-05-2025/5/19 1801
 * @TODO:
 * @version: 1.0
 */
public class encryptionTest {
    private static final String ALGORITHM = "PBWEithHMACSHA512AndAES_256";
    private static final String PASSWORD = "gtja-sjzx";

    public static String encrypt(String input) {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setAlgorithm(ALGORITHM);
        encryptor.setPassword(PASSWORD);
        return encryptor.encrypt(input);
    }

    public static String decrypt(String encrypted) {
        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setAlgorithm(ALGORITHM);
        encryptor.setPassword(PASSWORD);
        return encryptor.decrypt(encrypted);
    }

    public static void main(String[] args) {
        String encrypted="ygXJ7hhdml/RXPDApV0rotx44Gt8f/gr";
        System.out.println(decrypt(encrypted));
        System.out.println(encrypt(""));
    }


}
