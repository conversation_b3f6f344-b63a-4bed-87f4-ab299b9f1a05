<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.RtvSendLogDao">
  <resultMap id="BaseResultMap" type="com.gtja.faRtvData.model.SsgzMqSendLog">
    <result column="SRC" jdbcType="VARCHAR" property="SRC" />
    <result column="SRC_IP" jdbcType="VARCHAR" property="SRC_IP" />
    <result column="MSG_TYPE" jdbcType="VARCHAR" property="MSG_TYPE" />
    <result column="MSG_SUBTYPE" jdbcType="VARCHAR" property="MSG_SUBTYPE" />
    <result column="MSG_ID" jdbcType="VARCHAR" property="MSG_ID" />
    <result column="TARGET" jdbcType="VARCHAR" property="TARGET" />
    <result column="SEND_DATE" jdbcType="VARCHAR" property="SEND_DATE" />
    <result column="SEND_TIME" jdbcType="VARCHAR" property="SEND_TIME" />
    <result column="CONTENT" jdbcType="VARCHAR" property="MESSAGE" />
    <result column="ERR_MSG" jdbcType="VARCHAR" property="ERR_MSG" />
  </resultMap>
  <sql id="Base_Column_List">
    SRC, SRC_IP, MSG_TYPE, MSG_SUBTYPE, MSG_ID, TARGET, SEND_DATE, SEND_TIME, CONTENT,  ERR_MSG
  </sql>

  <insert id="insertSenderLog" parameterType="com.gtja.faRtvData.model.SsgzMqSendLog">
    insert into GTJA_SSGZ_MQ_SEND_LOG (SRC, SRC_IP, MSG_TYPE, 
      MSG_SUBTYPE, MSG_ID, TARGET, 
      SEND_DATE, SEND_TIME, CONTENT
      )
    values (#{SRC,jdbcType=VARCHAR}, #{SRC_IP,jdbcType=VARCHAR}, #{MSG_TYPE,jdbcType=VARCHAR},
      #{MSG_SUBTYPE ,jdbcType=VARCHAR}, #{MSG_ID,jdbcType=VARCHAR}, #{TARGET,jdbcType=VARCHAR},
      #{SEND_DATE,jdbcType=VARCHAR}, sysdate, #{MESSAGE,jdbcType=VARCHAR}
      )
  </insert>


</mapper>