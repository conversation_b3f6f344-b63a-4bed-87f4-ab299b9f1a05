package com.gtja.faRtvData.config.db;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.gtja.faRtvData.config.interceptor.SqlCheckInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.gtja.faRtvData.dao.hsfa", sqlSessionFactoryRef = "hsfaSqlSessionFactory")
public class DataSourceConfighsfa {
    @Bean(name = "hsfaDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.hsfa")
    public DataSource getDataSourcehsfa(){
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "hsfaSqlSessionFactory")
    @Primary
    public SqlSessionFactory hsfaSqlSessionFactory(@Qualifier("hsfaDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:/mapper/hsfa/*.xml")
        );
        return bean.getObject();
    }

    @Bean("hsfaSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate hsfaSqlSessionTemplate(
        @Qualifier("hsfaSqlSessionFactory") SqlSessionFactory sessionFactory
    ){
        return new SqlSessionTemplate(sessionFactory);
    }

    @Bean("hsfaTransactionManager")
    @Primary
    public PlatformTransactionManager hsfaTransactionManager(@Qualifier("hsfaDataSource") DataSource dataSource){
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager(dataSource);
        return dataSourceTransactionManager;
    }

}
