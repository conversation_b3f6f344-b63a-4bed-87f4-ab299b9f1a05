# 数据中心的测试环境ip
spring:
  datasource:
    #发布中心数据库
    fbzx:
      jdbc-url: ******************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: fbzx
      password: FBZX
    hsfa:
      jdbc-url: ******************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: hsfa
      password: hsfa
    sjzx:
      jdbc-url: ********************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: sjzx
      password: sjzx

xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8080/xxl-job-admin
    accessToken: Sjzx_xxl
    executor:
      appname: xxl-client-fa-rtv-data
      address:
      ip:
      port: 9200
      logpath: xxlLog\\
      logretentiondays: 30

## 测试使用 sftp ，生产对接nfs/cifs ==> SftpInfoModel
ssgz:
  sftp:
    ip: ************
    port: 21
    userName: ftpuser
    passWord: ftp.2024
  nfs:
    ip: *************
    share: fzcs
    path: /0000_个人文件夹/马杨杨
    userName: fs
    passWord: FS_share2020
  kafka:
    userName: sszcKafkaUser
    passWord: sszcKafkaUser@2024
    servers: ************:9093,************:9093,************:9093

baseFilePath:
  tempFilePath: //************/zctg_new/13_运行保障/02数据中心/10_临时文件/
  sftpTargetPath: //************//
  nfsTargetPath: //*************/fzcs/ssgaTest001/YYYYMMDD/


