-- Create table
create table GTJA_SSGZ_MQ_REC_LOG
(
    src           VARCHAR2(20),
    src_ip        VARCHAR2(20),
    msg_type      VARCHAR2(20),
    msg_subtype   VARCHAR2(20),
    msg_id        VARCHAR2(50),
    msg_group_id  NUMBER,
    msg_group_sno NUMBER,
    msg_group_cnt NUMBER,
    target        VARCHAR2(10),
    send_date     NUMBER,
    send_time     NUMBER,
    fv_result_hold_total  NUMBER,
    fv_result_hold_offset NUMBER,
    load_time     VARCHAR2(20) default to_char(sysdate,'yyyymmdd.hh24miss') not null,
    offset                NUMBER,
    content               CLOB
);
-- Add comments to the table
comment on table GTJA_SSGZ_MQ_REC_LOG
  is 'KAFKA消息接收表（实时估值）';
-- Add comments to the columns
comment on column GTJA_SSGZ_MQ_REC_LOG.src
  is '系统来源';
comment on column GTJA_SSGZ_MQ_REC_LOG.src_ip
  is '系统来源IP地址';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_type
  is '消息类型';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_subtype
  is '消息子类型';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_id
  is '消息ID';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_group_id
  is '消息组ID';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_group_sno
  is '消息组内序号';
comment on column GTJA_SSGZ_MQ_REC_LOG.msg_group_cnt
  is '消息组内消息个数';
comment on column GTJA_SSGZ_MQ_REC_LOG.target
  is '目标系统';
comment on column GTJA_SSGZ_MQ_REC_LOG.send_date
  is '发送日期';
comment on column GTJA_SSGZ_MQ_REC_LOG.send_time
  is '发送时间';
comment on column GTJA_SSGZ_MQ_REC_LOG.load_time
  is '入库时间';
