<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.hsfa.ICommonDao">
    <select id="getTradeday" resultType="java.lang.String">
		<![CDATA[
        select to_char(hsfa.sf_pub_addtradedays('1',to_date(#{date},'yyyyMMdd'), #{direction}, #{num}),'yyyyMMdd') lastTradeDay from dual
        ]]>
	</select>

</mapper>