<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.sjzx.RtvFracResultDao">

    <resultMap id="BasePointResultMap" type="com.gtja.faRtvData.model.GtjaSsgzPointModel">
        <result column="BUSI_DATE" jdbcType="DECIMAL" property="busi_date" />
        <result column="ACCOUNT_SET_NO" jdbcType="VARCHAR" property="account_set_no" />
        <result column="CCY_CODE" jdbcType="VARCHAR" property="ccy_code" />
        <result column="Z052001" jdbcType="DECIMAL" property="z052001" />
        <result column="Z052002" jdbcType="DECIMAL" property="z052002" />
        <result column="Z052003" jdbcType="DECIMAL" property="z052003" />
        <result column="Z052004" jdbcType="DECIMAL" property="z052004" />
        <result column="Z052005" jdbcType="DECIMAL" property="z052005" />
        <result column="Z052006" jdbcType="DECIMAL" property="z052006" />
        <result column="Z052007" jdbcType="DECIMAL" property="z052007" />
        <result column="Z052008" jdbcType="DECIMAL" property="z052008" />

        <result column="Z053001" jdbcType="DECIMAL" property="z053001" />
        <result column="Z053002" jdbcType="DECIMAL" property="z053002" />
        <result column="Z053003" jdbcType="DECIMAL" property="z053003" />
        <result column="Z053004" jdbcType="DECIMAL" property="z053004" />
        <result column="Z053005" jdbcType="DECIMAL" property="z053005" />
        <result column="Z053006" jdbcType="DECIMAL" property="z053006" />

        <result column="MSG_ID" jdbcType="VARCHAR" property="MSG_ID" />
        <result column="MSG_GROUP_ID" jdbcType="DECIMAL" property="MSG_GROUP_ID"/>
        <result column="MSG_GROUP_SNO" jdbcType="DECIMAL" property="MSG_GROUP_SNO"/>
    </resultMap>


    <select id="selectPointResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzPointModel" resultMap="BasePointResultMap">
        select
        *
        from GTJA_SSGZ_PROD_POINT
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <delete id="deletePointResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzPointModel">
        delete from GTJA_SSGZ_PROD_POINT
    </delete>

    <insert id="insertPoint" parameterType="com.gtja.faRtvData.model.GtjaSsgzPointModel">
        <foreach collection="infos" item="item" index="index" open="begin" close=";end;" separator=";">
            insert into GTJA_SSGZ_PROD_POINT (BUSI_DATE, ACCOUNT_SET_NO, CCY_CODE,
            Z052001, Z052002, Z052003,
            Z052004, Z052005, Z052006,
            Z052007, Z052008, MSG_ID,
            source_time,z053001,z053002,
            z053003,z053004,z053005,z053006
            )
            values (#{item.busi_date,jdbcType=DECIMAL}, #{item.account_set_no,jdbcType=VARCHAR}, #{item.ccy_code,jdbcType=VARCHAR},
            #{item.z052001,jdbcType=DECIMAL}, #{item.z052002,jdbcType=DECIMAL}, #{item.z052003,jdbcType=DECIMAL},
            #{item.z052004,jdbcType=DECIMAL}, #{item.z052005,jdbcType=DECIMAL}, #{item.z052006,jdbcType=DECIMAL},
            #{item.z052007,jdbcType=DECIMAL}, #{item.z052008,jdbcType=DECIMAL}, #{msgId,jdbcType=VARCHAR},
            #{sourceTime,jdbcType=VARCHAR}, #{item.z053001,jdbcType=DECIMAL}, #{item.z053002,jdbcType=DECIMAL},
            #{item.z053003,jdbcType=DECIMAL}, #{item.z053004,jdbcType=DECIMAL}, #{item.z053005,jdbcType=DECIMAL}, #{item.z053006,jdbcType=DECIMAL})
        </foreach>
    </insert>


    <update id="updatePointCpdm">
        update GTJA_SSGZ_PROD_POINT t
        set t.cpdm =
                (select b.cpdm from fbzx.gtja_ssgz_cpqd@zctg_query b where t.account_set_no = b.ztbh)
        where t.account_set_no is not null
          and t.cpdm is null
    </update>

</mapper>