package com.gtja.faRtvData.common.exception;

import java.io.Serializable;

/**
 * @Description: TODO 业务异常 封装类
 */
public class BusinessException extends BaseException implements Serializable {


    private static final long serialVersionUID = 2921378661996879666L;

    public BusinessException() {
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String code, String message) {
        super(code, message);
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(code, message, cause);
    }
}
