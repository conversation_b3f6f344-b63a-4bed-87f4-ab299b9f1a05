package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvTgpsdDsfgzModel {
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 9)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "L_ZQNM", fieldLength = 12)
    private String l_zqnm;//broker_
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;//broker_
    @DbfAnnotation(fieldName = "D_BEGIN", fieldLength = 16)
    private String d_begin;//broker_
    @DbfAnnotation(fieldName = "D_JSR", fieldLength = 16)
    private String d_jsr;//broker_

    @DbfAnnotation(fieldName = "D_JXR", fieldLength = 16)
    private String d_jxr;//broker_

    @DbfAnnotation(fieldName = "L_SL", fieldLength = 30)
    private String l_sl;//broker_

    @DbfAnnotation(fieldName = "L_XYLX", fieldLength = 1)
    private String l_xylx;//broker_

    @DbfAnnotation(fieldName = "L_ID", fieldLength = 40)
    private String l_id;//broker_

    @DbfAnnotation(fieldName = "L_CLBZ", fieldLength = 1)
    private String l_clbz;//broker_

    @DbfAnnotation(fieldName = "VC_BZ", fieldLength = 254)
    private String vc_bz;//broker_

    @DbfAnnotation(fieldName = "VC_LLR", fieldLength = 20)
    private String vc_llr;//broker_

    @DbfAnnotation(fieldName = "VC_FHR", fieldLength = 20)
    private String vc_fhr;//broker_

    @DbfAnnotation(fieldName = "L_SDXSLX", fieldLength = 2)
    private String l_sdxslx;//broker_

    @DbfAnnotation(fieldName = "EN_ZQCB", fieldLength = 25)
    private String en_zqcb;//broker_

    @DbfAnnotation(fieldName = "L_CCFL", fieldLength = 4)
    private String l_ccfl;//broker_

    @DbfAnnotation(fieldName = "D_QSRQ", fieldLength = 16)
    private String d_qsrq;//broker_

    @DbfAnnotation(fieldName = "L_XSYF", fieldLength = 16)
    private String l_xsyf;//broker_

    @DbfAnnotation(fieldName = "C_XWLX", fieldLength = 16)
    private String c_xwlx;//broker_

    @DbfAnnotation(fieldName = "D_QYRQ", fieldLength = 16)
    private String d_qyrq;//broker_
}
