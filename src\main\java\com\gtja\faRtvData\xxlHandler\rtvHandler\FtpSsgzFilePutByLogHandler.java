package com.gtja.faRtvData.xxlHandler.rtvHandler;

import com.gtja.faRtvData.model.NfsInfoModel;
import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.sftp.FtpSsgzFileDealService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tools.DateTimeTools;
import tools.RegularTools;

@Slf4j
@Component
public class FtpSsgzFilePutByLogHandler {

    protected static String SFTP_PATH = "/ssgz/";
    @Autowired
    SftpInfoModel dbinfo;
    @Autowired
    NfsInfoModel nfsInfo;
    @Autowired
    private FtpSsgzFileDealService ftpSsgzFileDealService;


    /**
     * @demoName:  FtpSsgzFilePutByLogHandler
     * @Anthor: Magic
     * @Create: 2025/2/13- 10:25
     * @TODO: 1.根据日志记录的未推送文件推送给目标地址；支持部分推送，下批次生成的文件下一次调度推送
     *        2.TEST:走 ftp推送数据   end
     * @Param:
     * @Return:
     */
    @XxlJob("SsgzFilePutByLogForTestHandler")
    public ReturnT<String> SsgzFilePutByLogForTestHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值ftp 文件处理 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());
        if (!DateTimeTools.getNowHours("08:30")) {
            ywrq = DateTimeTools.getDateTimeBeforeDay(-1);
        }
        try {
            String message = ftpSsgzFileDealService.SsgzFilePutByLogForSftp(ywrq, SFTP_PATH, dbinfo);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("ftp文件落库失败：" + e.toString());
            log.info("ftp文件落库失败:{}", e.toString());
        } finally {
            return result;
        }
    }

    /**
     * @demoName:  FtpSsgzFilePutByLogHandler
     * @Anthor: Magic
     * @Create: 2025/2/13- 10:39
     * @TODO: 1.根据日志记录的未推送文件推送给目标地址；支持部分推送，下批次生成的文件下一次调度推送
     *        2.PROD:走 NFS/cifs 传输   end
     * @Param:
     * @Return:
     */
    @XxlJob("SsgzFilePutByLogForProdHandler")
    public ReturnT<String> SsgzFilePutByLogForProdHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值cifs 文件处理 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());
        if (!DateTimeTools.getNowHours("08:30")) {
            ywrq = DateTimeTools.getDateTimeBeforeDay(-1);
        }
        try {
            String message = ftpSsgzFileDealService.SsgzFilePutByLogForCifs(ywrq, SFTP_PATH, nfsInfo);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("cifs文件落库失败：" + e.toString());
            log.info("cifs文件落库失败:{}", e.toString());
        } finally {
            return result;
        }
    }

    /**
     * 每天 19：00 发送当前日期的数据
     *
     * @param param
     * @return
     */
    @XxlJob("FtpSsgzFilePutByTimeHandler")
    public ReturnT<String> FtpSsgzFilePutByTimeHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值ftp 文件处理 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNow());

        try {
            String message = ftpSsgzFileDealService.SsgzFilePutByLogForSftp(ywrq, SFTP_PATH, dbinfo);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("ftp文件落库失败：" + e.toString());
            log.info("ftp文件落库失败:{}", e.toString());
        } finally {
            return result;
        }
    }
}
