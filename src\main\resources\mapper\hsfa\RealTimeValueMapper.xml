<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.hsfa.RealTimeValueHsfaDao">

    <resultMap id="BalanceMap" type="com.gtja.faRtvData.model.RtvBalanceDbfModel">
        <result column="TRADEDATE" property="tradedate"></result>
        <result column="PD_ID" property="pd_id"></result>
        <result column="SUBJ_CODE" property="subj_code"></result>
        <result column="SUBJ_CRRC_CODE" property="subj_crrc_code"></result>
        <result column="SUBJ_NAME" property="subj_name"></result>
        <result column="VC_CODE_HS" property="vc_code_hs"></result>
        <result column="L_LEAF" property="l_leaf"></result>
        <result column="END_BAL_BC" property="end_bal_bc"></result>
        <result column="END_QTY_BAL" property="end_qty_bal"></result>
        <result column="END_BAL_OC" property="end_bal_oc"></result>
        <result column="SEC_ID" property="sec_id"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
    </resultMap>

    <select id="queryRtvBalanceList" parameterType="java.lang.String" resultMap="BalanceMap">
        select #{ywrq}                                                                            as TRADEDATE
             , a.l_fundid                                                                         as PD_ID
             , a.vc_code                                                                          as SUBJ_CODE
             , a.vc_jsbz                                                                          as SUBJ_CRRC_CODE
             , substr(a.vc_name, 0, 254)                                                          as SUBJ_NAME
             , a.vc_code_hs
             , a.l_leaf
             , round(hsfa.sf_pub_kmye(a.vc_code, 0, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')), 4) as END_BAL_BC
             , round(hsfa.sf_pub_kmye(a.vc_code, 1, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')), 4) as END_QTY_BAL
             , round(hsfa.sf_pub_kmye(a.vc_code, 2, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')), 4) as END_BAL_OC
             , a.l_sclb                                                                           as SEC_ID
             , case
                   when length(a.vc_code) > 8 then
                       substr(a.vc_code, 9, length(a.vc_code))
                   else
                       ''
            end                                                                                   as vc_zqdm
        from hsfa.taccount a
           , hsfa.tfundinfo b
        where a.l_fundid = b.l_fundid
          and a.l_leaf = 1
          and upper(b.vc_code) = #{cpdm}
          and (hsfa.sf_pub_kmye(a.vc_code, 1, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0 or
               hsfa.sf_pub_kmye(a.vc_code, 2, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0 or
               hsfa.sf_pub_kmye(a.vc_code, 0, a.l_fundid, to_date(#{ywrq}, 'yyyymmdd')) &lt;> 0)
    </select>


    <!--BROKER_-->
    <!--BROKER_-->
    <resultMap id="TgpsdDsfgzMap" type="com.gtja.faRtvData.model.RtvTgpsdDsfgzModel">
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="L_ZQNM" property="l_zqnm"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
        <result column="D_BEGIN" property="d_begin"></result>
        <result column="D_JSR" property="d_jsr"></result>
        <result column="D_JXR" property="d_jxr"></result>
        <result column="L_SL" property="l_sl"></result>
        <result column="L_XYLX" property="l_xylx"></result>
        <result column="L_ID" property="l_id"></result>
        <result column="L_CLBZ" property="l_clbz"></result>
        <result column="VC_BZ" property="vc_bz"></result>
        <result column="VC_LLR" property="vc_llr"></result>
        <result column="VC_FHR" property="vc_fhr"></result>
        <result column="L_SDXSLX" property="l_sdxslx"></result>
        <result column="EN_ZQCB" property="en_zqcb"></result>
        <result column="L_CCFL" property="l_ccfl"></result>
        <result column="D_QSRQ" property="d_qsrq"></result>
        <result column="L_XSYF" property="l_xsyf"></result>
        <result column="C_XWLX" property="c_xwlx"></result>
        <result column="D_QYRQ" property="d_qyrq"></result>
    </resultMap>

    <select id="queryRtvTgpsdDsfgzList" resultMap="TgpsdDsfgzMap">
        select a.*
        from hsfa.view_gtja_tgpsd_dsfgz a
           , hsfa.tfundinfo b
        where a.l_ztbh = b.l_fundid
          and upper(b.vc_code) = #{cpdm}
          and sysdate between d_begin and nvl(d_jsr, sysdate + 1)
          and d_jxr > date '2024-11-1'
        order by d_begin desc
    </select>


    <!--BROKER_-->
    <resultMap id="RtvTjkZzqsTgpsdqLdxzkMap" type="com.gtja.faRtvData.model.RtvTjkZzqsTgpsdqLdxzkModel">
        <result column="GZR" property="gzr"></result>
        <result column="GPDM" property="gpdm"></result>
        <result column="XSQJSR" property="xsqjsr"></result>
        <result column="GFBDGGR" property="gfbdggr"></result>
        <result column="SSHY" property="sshy"></result>
        <result column="DYHYZS" property="dyhyzs"></result>
        <result column="SYXSQ" property="syxsq"></result>
        <result column="LDXZK" property="ldxzk"></result>
        <result column="BL" property="bl"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="D_YWRQ" property="d_ywrq"></result>
    </resultMap>

    <!-- where a.d_ywrq = to_date( #{ywrq},'yyyymmdd') and a.cpdm=#{cpdm}-->
    <select id="queryRtvTjkZzqsTgpsdqLdxzkList" resultMap="RtvTjkZzqsTgpsdqLdxzkMap">
        select *
        from hsfa.tjk_zzqs_tgpsdq_ldxzk a
    </select>

    <resultMap id="RtvTqyxxMap" type="com.gtja.faRtvData.model.RtvTqyxxModel">
        <result column="D_DJRQ" property="d_djrq"></result>
        <result column="L_YWLB" property="l_ywlb"></result>
        <result column="L_ZQNM" property="l_zqnm"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
        <result column="EN_SYBL" property="en_sybl"></result>
        <result column="L_CLBZ" property="l_clbz"></result>
        <result column="VC_BZ" property="vc_bz"></result>
        <result column="D_YWRQ" property="d_ywrq"></result>
        <result column="D_CXRQ" property="d_cxrq"></result>
        <result column="D_DZRQ" property="d_dzrq"></result>
        <result column="VC_DJBZ" property="vc_djbz"></result>
        <result column="VC_DZBZ" property="vc_dzbz"></result>
        <result column="EN_SHBL" property="en_shbl"></result>
        <result column="L_QYZQNM" property="l_qyzqnm"></result>
        <result column="VC_FZDM1" property="vc_fzdm1"></result>
        <result column="D_XQJSRQ" property="d_xqjsrq"></result>
        <result column="EN_PGJG" property="en_pgjg"></result>
        <result column="EN_BL" property="en_bl"></result>
        <result column="VC_QYBH" property="vc_qybh"></result>
        <result column="D_QYJSRQ" property="d_qyjsrq"></result>
        <result column="D_HSSBQSR" property="d_hssbqsr"></result>
        <result column="D_HSSBJZR" property="d_hssbjzr"></result>
        <result column="D_HSCXQSR" property="d_hscxqsr"></result>
        <result column="D_HSCXJZR" property="d_hscxjzr"></result>
        <result column="D_ZQHSXQR" property="d_zqhsxqr"></result>
        <result column="D_ZQHSDZR" property="d_zqhsdzr"></result>
        <result column="L_HSCS" property="l_hscs"></result>
        <result column="D_GJSCQRQ" property="d_gjscqrq"></result>
    </resultMap>
    <select id="queryRtvTqyxxList" resultMap="RtvTqyxxMap">
        select *
        from hsfa.tqyxx
        where l_ywlb = 6105
    </select>


    <resultMap id="RtvTzqxxMap" type="com.gtja.faRtvData.model.RtvTzqxxModel">
        <result column="L_ZQNM" property="l_zqnm"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
        <result column="L_ZQLB" property="l_zqlb"></result>
        <result column="L_ZQLBMX1" property="l_zqlbmx1"></result>
        <result column="L_ZQLBMX2" property="l_zqlbmx2"></result>
        <result column="L_SCLB" property="l_sclb"></result>
        <result column="VC_ZQJC" property="vc_zqjc"></result>
        <result column="VC_JSBZ" property="vc_jsbz"></result>
        <result column="EN_MGMZ" property="en_mgmz"></result>
        <result column="D_FXRQ" property="d_fxrq"></result>
        <result column="D_DQRQ" property="d_dqrq"></result>
        <result column="C_ZQZT" property="c_zqzt"></result>
        <result column="L_HGTS" property="l_hgts"></result>
        <result column="L_NJXTS" property="l_njxts"></result>
        <result column="EN_NLL" property="en_nll"></result>
        <result column="D_QXR" property="d_qxr"></result>
        <result column="L_FXFS" property="l_fxfs"></result>
        <result column="D_MODIFY" property="d_modify"></result>
        <result column="L_JXFS" property="l_jxfs"></result>
        <result column="EN_FXJG" property="en_fxjg"></result>
        <result column="VC_ZQQC" property="vc_zqqc"></result>
        <result column="L_HYYF" property="l_hyyf"></result>
        <result column="EN_BZJL" property="en_bzjl"></result>
        <result column="D_JGRQ" property="d_jgrq"></result>
        <result column="L_JGFS" property="l_jgfs"></result>
        <result column="L_HYZT" property="l_hyzt"></result>
        <result column="VC_MBBZ" property="vc_mbbz"></result>
        <result column="EN_HYCS" property="en_hycs"></result>
        <result column="C_QJJJ" property="c_qjjj"></result>
        <result column="L_LXJSFF" property="l_lxjsff"></result>
        <result column="D_ZGQKS" property="d_zgqks"></result>
        <result column="D_ZGQJS" property="d_zgqjs"></result>
        <result column="VC_HQBZ" property="vc_hqbz"></result>
        <result column="L_DQFXFS" property="l_dqfxfs"></result>
        <result column="L_YMFXBZ" property="l_ymfxbz"></result>
        <result column="L_GLZQNM" property="l_glzqnm"></result>
    </resultMap>

    <select id="queryRtvTzqxxList" resultMap="RtvTzqxxMap">
        select L_ZQNM
             ,VC_ZQDM
             ,L_ZQLB
             ,L_ZQLBMX1
             ,L_ZQLBMX2
             ,L_SCLB
             ,VC_ZQJC
             ,VC_YWJC
             ,VC_SSHY
             ,VC_JSBZ
             ,L_ZGB
             ,L_LTGS
             ,EN_MGMZ
             ,D_FXRQ
             ,D_DQRQ
             ,C_ZQZT
             ,L_HGTS
             ,L_NJXTS
             ,EN_NLL
             ,D_QXR
             ,L_FXFS
             ,D_MODIFY
             ,L_JXFS
             ,EN_FXJG
             ,VC_ZQQC
             ,L_HYYF
             ,EN_BZJL
             ,D_JGRQ
             ,L_JGFS
             ,L_HYZT
             ,VC_MBBZ
             ,EN_HYCS
             ,C_QJJJ
             ,L_LXJSFF
             ,D_ZGQKS
             ,D_ZGQJS
             ,VC_HQBZ
             ,L_DQFXFS
             ,L_YMFXBZ
             ,L_GLZQNM
        from hsfa.tzqxx  t
        where l_zqlb in (1, 2, 4, 7)
    </select>


    <resultMap id="RtvGzbMap" type="com.gtja.faRtvData.model.RtvGzbModel">
        <result column="L_BH" property="l_bh"></result>
        <result column="VC_KMDM" property="vc_kmdm"></result>
        <result column="VC_KMMC" property="vc_kmmc"></result>
        <result column="L_SL" property="l_sl"></result>
        <result column="EN_DWCB" property="en_dwcb"></result>
        <result column="EN_CB" property="en_cb"></result>
        <result column="EN_CBZJZ" property="en_cbzjz"></result>
        <result column="EN_HQJZ" property="en_hqjz"></result>
        <result column="EN_SZ" property="en_sz"></result>
        <result column="EN_SZZJZ" property="en_szzjz"></result>
        <result column="EN_GZZZ" property="en_gzzz"></result>
        <result column="VC_TPXX" property="vc_tpxx"></result>
        <result column="VC_QYXX" property="vc_qyxx"></result>
        <result column="L_TMPID" property="l_tmpid"></result>
        <result column="VC_JYS" property="vc_jys"></result>
        <result column="VC_TZPZ" property="vc_tzpz"></result>
        <result column="VC_XJL" property="vc_xjl"></result>
        <result column="VC_TS" property="vc_ts"></result>
        <result column="EN_FDYKBL" property="en_fdykbl"></result>
        <result column="L_LEAF" property="l_leaf"></result>
        <result column="VC_KMPARENT" property="vc_kmparent"></result>
        <result column="L_LEVEL" property="l_level"></result>
        <result column="L_ZTBH" property="l_ztbh"></result>
        <result column="D_YWRQ" property="d_ywrq"></result>
        <result column="D_SCSJ" property="d_scsj"></result>
        <result column="L_QUANTITY" property="l_quantity"></result>
        <result column="VC_CODE_HS" property="vc_code_hs"></result>
        <result column="L_KIND" property="l_kind"></result>
        <result column="L_GZKMBZ" property="l_gzkmbz"></result>
        <result column="VC_JSBZ" property="vc_jsbz"></result>
        <result column="EN_EXCH" property="en_exch"></result>
        <result column="EN_WBCB" property="en_wbcb"></result>
        <result column="EN_WBHQ" property="en_wbhq"></result>
        <result column="EN_WBSZ" property="en_wbsz"></result>
        <result column="L_ZQNM" property="l_zqnm"></result>
        <result column="L_SFQR" property="l_sfqr"></result>
        <result column="L_TZLX" property="l_tzlx"></result>
        <result column="EN_ZYJ" property="en_zyj"></result>
        <result column="EN_WBZYJ" property="en_wbzyj"></result>
        <result column="VC_CHECKER" property="vc_checker"></result>
        <result column="VC_BZW" property="vc_bzw"></result>
        <result column="VC_LTLX" property="vc_ltlx"></result>
        <result column="EN_YBGZZZ" property="en_ybgzzz"></result>
        <result column="VC_ZQDM" property="vc_zqdm"></result>
        <result column="L_SCLB" property="l_sclb"></result>
        <result column="L_ZQLB" property="l_zqlb"></result>
        <result column="EN_QYXX" property="en_qyxx"></result>
    </resultMap>

    <select id="queryRtvGzbList" resultMap="RtvGzbMap">
        select t.*
        from HSFA.TTMP_H_GZB t
           , hsfa.tfundinfo b
        where t.l_ztbh = b.l_fundid
          and b.vc_code = #{cpdm}
          and t.d_ywrq = to_date(#{ywrq}, 'yyyy-mm-dd')
    </select>


</mapper>