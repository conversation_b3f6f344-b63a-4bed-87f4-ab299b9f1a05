jasypt:
  encryptor:
    password: gtja-sjzx
spring:
  datasource:
    #发布中心数据库
    fbzx:
      jdbc-url: *****************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: E<PERSON>(IsDJ0jJpXw0BTrPMuFPwpw==)
      password: ENC(ygXJ7hhdml/RXPDApV0rotx44Gt8f/gr)
      initialSize: 10
      minIdle: 0
      maxActive: 400
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      filters: stat
      maxPoolPreparedStatementPerConnectionSize: 10
      useGlobalDataSourceStat: true
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    #外包估值
    hsfa:
      jdbc_url: *******************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: sjzx_query
      password: ENC(usUMCMhBU7pP+crA99bU4posvBf1xHIx6Bqfm9LiSHA=)
      initialSize: 1
      minIdle: 0
      maxActive: 5
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      filters: stat
      maxPoolPreparedStatementPerConnectionSize: 20
      useGlobalDataSourceStat: true
      connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    sjzx:
      jdbc_url: *****************************************
      driverClassName: oracle.jdbc.driver.OracleDriver
      username: sjzx
      password: ODS_sdata204
      maxActive: 20
xxl:
  job:
    admin:
      addresses: http://*************/xxl-job-admin
    accessToken: Sjzx_xxl
    executor:
      appname: xxl-client-fa-rtv-data
      address:
      ip:
      port: 9200
      logpath: xxlLog\\
      logretentiondays: 30

baseFilePath:
  rtvTargetPath: //************/zctg_new/13_运行保障/02数据中心/10_临时文件/实时估值/YYYYMMDD/
  tempFilePath: //************/zctg_new/13_运行保障/02数据中心/10_临时文件/
  sftpTargetPath: //************//
  nfsTargetPath: //************/frac_file/tgshare/YYYYMMDD/

## 测试使用 sftp ，生产对接nfs/cifs ==> SftpInfoModel
ssgz:
  nfs:
    ip: ************
    share: frac_file
    path: \frac_file\tgshare
    userName: clzxadmin
    passWord: Gtja@clzx111@#$
  sftp:
    ip: ************
    port: 21
    userName: ftpuser
    passWord: ftp.2024
  kafka:
    userName: zctgKafkaUser
    passWord: zctgKafkaUser@2025
    servers: ***********:9093,***********:9093,***********:9093,************:9093,************:9093,************:9093,************:9093


