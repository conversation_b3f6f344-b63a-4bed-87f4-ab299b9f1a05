package com.gtja.faRtvData.dao.hsfa;

import com.gtja.faRtvData.model.RtvCommonPdfModel;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ProjectName: RealTimeValueDao.java
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2022/9/2211:32
 * @Version :1.0
 */
@Repository
public interface RealTimeValueHsfaDao {
    List<T> queryRtvBalanceList(@Param("cpdm") String cpdm, @Param("ywrq") String ywrq);

    List<T> queryRtvGzbList(@Param("cpdm") String cpdm, @Param("ywrq") String ywrq);

    //---------------- ---------
    List<T> queryRtvTgpsdDsfgzList(@Param("cpdm") String cpdm);

    List<T> queryRtvTjkZzqsTgpsdqLdxzkList();

    List<T> queryRtvTqyxxList();

    List<T> queryRtvTzqxxList();
}
