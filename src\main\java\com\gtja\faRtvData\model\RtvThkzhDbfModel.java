package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

@Data
public class RtvThkzhDbfModel {
    @DbfAnnotation(fieldName = "l_ztbh", fieldLength = 6)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_ZH", fieldLength = 100)
    private String vc_zh;
    @DbfAnnotation(fieldName = "VC_MC", fieldLength = 200)
    private String vc_mc;
    @DbfAnnotation(fieldName = "VC_KMDM", fieldLength = 64)
    private String vc_kmdm;

}
