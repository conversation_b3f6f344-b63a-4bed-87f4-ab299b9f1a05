package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

@Data
public class RtvTfundInfoDbfModel {
    @DbfAnnotation(fieldName = "L_FUNDID", fieldLength = 6)
    private String l_fundid;
    @DbfAnnotation(fieldName = "VC_CODE", fieldLength = 15)
    private String vc_code;
    @DbfAnnotation(fieldName = "VC_NAME", fieldLength = 200)
    private String vc_name;
    @DbfAnnotation(fieldName = "FULLNAME", fieldLength = 200)
    private String vc_fullname;
    @DbfAnnotation(fieldName = "L_CLASS", fieldLength = 4)
    private String l_class;
    @DbfAnnotation(fieldName = "L_JJTZLX", fieldLength = 4)
    private String l_jjtzlx;
    @DbfAnnotation(fieldName = "OPER_ID", fieldLength = 100)
    private String trustee_oper_id;
    @DbfAnnotation(fieldName = "VC_BH", fieldLength = 8)
    private String vc_bh;
    @DbfAnnotation(fieldName = "VC_GLR", fieldLength = 60)
    private String vc_glr;
    @DbfAnnotation(fieldName = "VC_TGR", fieldLength = 50)
    private String vc_tgr;
    @DbfAnnotation(fieldName = "D_CREATE", fieldLength = 16)
    private String d_create;
    @DbfAnnotation(fieldName = "D_DESTORY", fieldLength = 16)
    private String d_destory;
    @DbfAnnotation(fieldName = "D_DQRQ", fieldLength = 16)
    private String d_dqrq;
    @DbfAnnotation(fieldName = "EN_MJJE", fieldLength = 28)
    private String en_mjje;
    @DbfAnnotation(fieldName = "EN_ZFE", fieldLength = 28)
    private String en_zfe;

}
