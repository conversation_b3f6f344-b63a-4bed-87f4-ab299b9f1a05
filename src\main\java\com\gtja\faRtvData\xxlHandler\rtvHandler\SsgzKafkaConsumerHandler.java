package com.gtja.faRtvData.xxlHandler.rtvHandler;

import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.model.KafkaInfoModel;
import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.mq.MessageConsumer;
import com.gtja.faRtvData.service.mq.MessageSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tools.DateTimeTools;
import tools.RegularTools;

@Slf4j
@Component
public class SsgzKafkaConsumerHandler {

    @Autowired
    private MessageConsumer messageConsumer;

    /**
     * 实时估值-sjzx-sender
     *
     * @param param
     * @return
     */
    @XxlJob("KafkaConsmerHandler")
    public ReturnT<String> KafkaConsmerHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值kafka 消息接收 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);

        try {
          //  messageConsumer.test002();
            messageConsumer.kafkaConsumer();
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("实时估值kafka 消息接收失败：" + e.toString());
            log.info("实时估值kafka 消息接收失败:{}", e.toString());
        } finally {
            return result;
        }
    }

}
