package com.gtja.faRtvData.common.utils;
/**
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2020/10/3020:57
 * @Version :1.0
 */

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.type.TypeFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public final class JsonUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(JsonUtil.class);

    public static final TypeReference<Map<String, String>> TYPE_REFERENCE = new TypeReference<Map<String, String>>() {};

    private static ObjectMapper objectMapper = JsonObjectMapperFactory.getInstance();

    public static String toJson(final Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception var2) {
            LOGGER.error("json序列化异常", var2);
            throw new IllegalArgumentException("转换为JSON字符串时异常", var2);
        }
    }

    public static <K, V> Map<K, V> toMap(String value) {
        return (Map) parse(value, (new HashMap()).getClass());
    }

    public static <V> List<V> toList(String value) {
        return (List) parse(value, (new ArrayList()).getClass());
    }

    public static <T> T parse(final String json, final Class<T> clazz) {
        if (StringUtils.isEmpty(json)) {
            return null;
        } else {
            try {
                return objectMapper.readValue(json, clazz);
            } catch (Exception var3) {
                LOGGER.error("json反序列化异常", var3);
                throw new IllegalArgumentException("由JSON字符串时转换为对象时异常", var3);
            }
        }
    }

    public static <T> T parse(final String json, final Type type) {
        if (StringUtils.isEmpty(json)) {
            return null;
        } else {
            try {
                return objectMapper.readValue(json, TypeFactory.defaultInstance().constructType(type));
            } catch (Exception var3) {
                LOGGER.error("json反序列化异常", var3);
                throw new IllegalArgumentException("由JSON字符串时转换为对象时异常", var3);
            }
        }
    }

    public static Map<String, String> parse(final String json) {
        try {
            return (Map) parse(json, TYPE_REFERENCE);
        } catch (Exception var2) {
            LOGGER.error("json反序列化异常", var2);
            throw new IllegalArgumentException("由JSON字符串时转换为对象时异常", var2);
        }
    }

    public static <T> T parse(final String json, final TypeReference<T> valueTypeRef) {
        if (StringUtils.isEmpty(json)) {
            return null;
        } else {
            try {
                return objectMapper.readValue(json, valueTypeRef);
            } catch (Exception var3) {
                LOGGER.error("json反序列化异常", var3);
                throw new IllegalArgumentException("由JSON字符串时转换为对象时异常", var3);
            }
        }
    }

    public static <T> T parse(final String json, final JavaType javaType) {
        if (StringUtils.isEmpty(json)) {
            return null;
        } else {
            try {
                return objectMapper.readValue(json, javaType);
            } catch (Exception var3) {
                LOGGER.error("json反序列化异常", var3);
                throw new IllegalArgumentException("由JSON字符串时转换为对象时异常", var3);
            }
        }
    }

    private JsonUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static class JsonObjectMapperFactory {
        private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        private static ObjectMapper om = new ObjectMapper();

        public JsonObjectMapperFactory() {
        }

        public static ObjectMapper getInstance() {
            return om;
        }

        static {
            om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            om.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
            om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            om.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        }
    }
}
