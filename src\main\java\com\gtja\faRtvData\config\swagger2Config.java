package com.gtja.faRtvData.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR> zhangkai
 * @date : 10:19 2020/11/20
 * @desc Swagger2 Java配置 配置一些基本内容和扫描包位置
 */
@Configuration
@EnableSwagger2
public class swagger2Config {

    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2).apiInfo(apiInfo()).select()
                //为扫描包路径
                .apis(RequestHandlerSelectors.basePackage("com.gtja.faRtvData.controller"))
                .paths(PathSelectors.any())
                .build();
    }

    private ApiInfo apiInfo(){
        return new ApiInfoBuilder().title("估值系统 估值数据处理").version("1.0").build();
    }

}
