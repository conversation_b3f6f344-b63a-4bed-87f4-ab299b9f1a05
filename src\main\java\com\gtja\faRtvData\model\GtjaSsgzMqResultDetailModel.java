package com.gtja.faRtvData.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class GtjaSsgzMqResultDetailModel implements Serializable {
    List<GtjaSsgzPointModel> fv_result_indic = new ArrayList<>();//指标
    List<GtjaSsgzAssetModel> fv_result_asset = new ArrayList<>();//资产
    List<GtjaSsgzPositionModel> fv_result_hold = new ArrayList<>();//持仓
    String fv_result_hold_total;
    String fv_result_hold_offset;
}