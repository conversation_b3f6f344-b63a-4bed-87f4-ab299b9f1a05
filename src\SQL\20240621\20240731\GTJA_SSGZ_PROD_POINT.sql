-- Create table
create table GTJA_SSGZ_PROD_POINT
(
    busi_date      NUMBER not null,
    account_set_no VARCHAR2(32) not null,
    ccy_code       VARCHAR2(3) not null,
    z052001        NUMBER(18,4),
    z052002        NUMBER(18,4),
    z052003        NUMBER(18,4),
    z052004        NUMBER(18,4),
    z052005        NUMBER(18,4),
    z052006        NUMBER(18,4),
    z052007        NUMBER(18,4),
    z052008        NUMBER(18,4),

    z053001        NUMBER(18,4),
    z053002        NUMBER(18,4),
    z053003        NUMBER(18,4),
    z053004        NUMBER(18,4),
    z053005        NUMBER(18,4),
    z053006        NUMBER(18,4),

    msg_id         VARCHAR2(50),
    cpdm          varchar2(20),
    load_time    VARCHAR2(20)  default to_char(sysdate,'yyyymmdd.hh24miss') not null,
    source_time VARCHAR2(20)
);
-- Add comments to the table
comment on table GTJA_SSGZ_PROD_POINT
  is 'FV_产品指标（实时估值）';
-- Add comments to the columns
-- Add comments to the columns
comment on column GTJA_SSGZ_PROD_POINT.busi_date
  is '业务日期';
comment on column GTJA_SSGZ_PROD_POINT.account_set_no
  is '账套号';
comment on column GTJA_SSGZ_PROD_POINT.ccy_code
  is '币种代码';
comment on column GTJA_SSGZ_PROD_POINT.z052001
  is '资产类合计';
comment on column GTJA_SSGZ_PROD_POINT.z052002
  is '负债类合计';
comment on column GTJA_SSGZ_PROD_POINT.z052003
  is '资产净值';
comment on column GTJA_SSGZ_PROD_POINT.z052004
  is '今日单位净值';
comment on column GTJA_SSGZ_PROD_POINT.z052005
  is '日净值增长率';
comment on column GTJA_SSGZ_PROD_POINT.z052006
  is '当日实现收益';
comment on column GTJA_SSGZ_PROD_POINT.z052007
  is '当日可分配收益';
comment on column GTJA_SSGZ_PROD_POINT.z052008
  is '单位可分配收益';
comment on column GTJA_SSGZ_PROD_POINT.msg_id
  is '消息ID';
comment on column GTJA_SSGZ_PROD_POINT.cpdm
  is '产品代码';
comment on column GTJA_SSGZ_PROD_POINT.load_time
  is '数据落地时间';
comment on column GTJA_SSGZ_PROD_POINT.source_time
  is '数据计算时间';



comment on column GTJA_SSGZ_PROD_POINT.z053001
  is '上证指数';
comment on column GTJA_SSGZ_PROD_POINT.z053002
  is '上证50';
comment on column GTJA_SSGZ_PROD_POINT.z053003
  is '沪深300';
comment on column GTJA_SSGZ_PROD_POINT.z053004
  is '中证500';
comment on column GTJA_SSGZ_PROD_POINT.z053005
  is '中证1000';
comment on column GTJA_SSGZ_PROD_POINT.z053006
  is '中证2000';


