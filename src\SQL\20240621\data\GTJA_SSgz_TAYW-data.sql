prompt Importing table fbzx.GTJA_SSgz_TAYW...
set feedback off
set define off

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, T<PERSON><PERSON><PERSON>_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7921077, '31276', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 29337.8800, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SB7800', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7941086, '29841', '********', 'CNY', ********, ********, null, null, 5624887.4800, 5624887.4800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7952893, '30101', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, null, 2480.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7952782, '30101', '********', 'CNY', ********, ********, null, null, 1009920.0000, 1009920.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7944904, '30101', '********', 'CNY', ********, ********, null, null, 5196279.5700, 5196279.5700, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7967805, '30590', '********', 'CNY', ********, ********, null, null, ********.4900, ********.4900, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7963574, '27362', '********', 'CNY', ********, ********, null, null, 2897018.9700, 2897018.9700, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979105, '21581', '********', 'CNY', ********, ********, null, null, 1457800.0000, 1457800.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979106, '21581', '********', 'CNY', ********, ********, null, null, 1457600.0000, 1457600.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979928, '27362', '********', 'CNY', ********, ********, null, null, 5007612.9400, 5007612.9400, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979914, '27234', '********', 'CNY', ********, ********, null, null, 4880000.0000, 4880000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXE013', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7968008, '30590', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 90952.6200, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988142, '30653', '********', 'CNY', ********, ********, null, null, ********.6500, ********.6500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979094, '21581', '********', 'CNY', ********, ********, null, null, 728800.0000, 728800.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988141, '30590', '********', 'CNY', ********, ********, null, null, 5496000.0000, 5496000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988149, '30616', '********', 'CNY', ********, ********, null, null, ********.2600, ********.2600, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZP865', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988146, '30653', '********', 'CNY', ********, ********, null, null, 7648000.0000, 7648000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7980177, '27362', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 10808.0600, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7995630, '27362', '********', 'CNY', ********, ********, null, null, 1045501.5500, 1045501.5500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 8005699, '30653', '********', 'CNY', ********, ********, null, null, 9120000.0000, 9120000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 8011325, '27234', '********', 'CNY', ********, ********, null, null, 5124398.2400, 5124398.2400, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXE013', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7685017, '26536', '********', 'CNY', ********, ********, null, null, ********.8800, ********.8800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, ********.8800, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7685015, '26261', '********', 'CNY', ********, ********, null, null, ********.8500, ********.8500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, ********.8500, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7660790, '26536', '********', 'CNY', ********, ********, null, null, 1347868.6800, 1347868.6800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, 1347868.6800, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:02', 'dd-mm-yyyy hh24:mi:ss'), ********, 8005116, '26536', '********', 'CNY', ********, ********, null, null, 599942.7200, 599942.7200, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, 599942.7200, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:02', 'dd-mm-yyyy hh24:mi:ss'), ********, 7983746, '26536', '********', 'CNY', ********, ********, null, null, 2005949.7000, 2005949.7000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, 2005949.7000, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7934957, '26536', '********', 'CNY', ********, ********, null, null, 500000.0000, 500000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7963255, '31276', '********', 'CNY', ********, ********, null, null, 500000.0000, 500000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SB7800', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7947561, '29841', '********', 'CNY', ********, ********, null, null, 1247524.7500, 1247524.7500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7947562, '29842', '********', 'CNY', ********, ********, null, null, 495049.5000, 495049.5000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 6295641, '13437', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, 0.0000, null, null, null, null, 2277375.2500, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 6295667, '13437', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, 0.0000, null, null, null, null, 217642.7400, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 7182826, '28896', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, 1238863.6400, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXZ374', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 7188007, '28843', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, 2109040.6700, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7921077, '31276', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 29337.8800, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SB7800', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7941086, '29841', '********', 'CNY', ********, ********, null, null, 5624887.4800, 5624887.4800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7952893, '30101', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, null, 2480.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7952782, '30101', '********', 'CNY', ********, ********, null, null, 1009920.0000, 1009920.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7944904, '30101', '********', 'CNY', ********, ********, null, null, 5196279.5700, 5196279.5700, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZK321', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7967805, '30590', '********', 'CNY', ********, ********, null, null, ********.4900, ********.4900, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7963574, '27362', '********', 'CNY', ********, ********, null, null, 2897018.9700, 2897018.9700, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979105, '21581', '********', 'CNY', ********, ********, null, null, 1457800.0000, 1457800.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979106, '21581', '********', 'CNY', ********, ********, null, null, 1457600.0000, 1457600.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979928, '27362', '********', 'CNY', ********, ********, null, null, 5007612.9400, 5007612.9400, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979914, '27234', '********', 'CNY', ********, ********, null, null, 4880000.0000, 4880000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXE013', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7968008, '30590', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 90952.6200, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988142, '30653', '********', 'CNY', ********, ********, null, null, ********.6500, ********.6500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7979094, '21581', '********', 'CNY', ********, ********, null, null, 728800.0000, 728800.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SQA854', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988141, '30590', '********', 'CNY', ********, ********, null, null, 5496000.0000, 5496000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZR839', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988149, '30616', '********', 'CNY', ********, ********, null, null, ********.2600, ********.2600, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZP865', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7988146, '30653', '********', 'CNY', ********, ********, null, null, 7648000.0000, 7648000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7980177, '27362', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, null, null, null, null, null, 10808.0600, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 7995630, '27362', '********', 'CNY', ********, ********, null, null, 1045501.5500, 1045501.5500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SVY443', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 8005699, '30653', '********', 'CNY', ********, ********, null, null, 9120000.0000, 9120000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SZZ676', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 8011325, '27234', '********', 'CNY', ********, ********, null, null, 5124398.2400, 5124398.2400, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXE013', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7685017, '26536', '********', 'CNY', ********, ********, null, null, ********.8800, ********.8800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, ********.8800, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7685015, '26261', '********', 'CNY', ********, ********, null, null, ********.8500, ********.8500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, ********.8500, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:51', 'dd-mm-yyyy hh24:mi:ss'), ********, 7660790, '26536', '********', 'CNY', ********, ********, null, null, 1347868.6800, 1347868.6800, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, 1347868.6800, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:02', 'dd-mm-yyyy hh24:mi:ss'), ********, 8005116, '26536', '********', 'CNY', ********, ********, null, null, 599942.7200, 599942.7200, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, 599942.7200, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:02', 'dd-mm-yyyy hh24:mi:ss'), ********, 7983746, '26536', '********', 'CNY', ********, ********, null, null, 2005949.7000, 2005949.7000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, 2005949.7000, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7934957, '26536', '********', 'CNY', ********, ********, null, null, 500000.0000, 500000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7963255, '31276', '********', 'CNY', ********, ********, null, null, 500000.0000, 500000.0000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SB7800', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7947561, '29841', '********', 'CNY', ********, ********, null, null, 1247524.7500, 1247524.7500, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:17:24', 'dd-mm-yyyy hh24:mi:ss'), ********, 7947562, '29842', '********', 'CNY', ********, ********, null, null, 495049.5000, 495049.5000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 6295641, '13437', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, 0.0000, null, null, null, null, 2277375.2500, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 6295667, '13437', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, 0.0000, null, null, null, null, 217642.7400, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 7182826, '28896', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, 1238863.6400, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', 'SXZ374', null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('03-07-2024 09:18:12', 'dd-mm-yyyy hh24:mi:ss'), ********, 7188007, '28843', '********', 'CNY', ********, ********, null, null, 0.0000, 0.0000, 0.0000, null, null, null, null, null, null, null, null, null, 2109040.6700, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'test', null, null);

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('13-12-2024 13:55:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 1126282, null, '********', null, null, ********, ********, null, null, 6237869.3000, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'SQA854', 'SQA854');

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('13-12-2024 13:55:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 1126243, null, '********', null, null, ********, ********, null, null, 2653984.1600, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'SQA854', 'SQA854');

insert into fbzx.GTJA_SSgz_TAYW (CREATE_DATE, BUSI_DATE, BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER, CPDM, FJCPDM)
values (to_date('13-12-2024 13:55:37', 'dd-mm-yyyy hh24:mi:ss'), ********, 1126089, null, '********', null, null, ********, ********, null, null, 853402.6300, null, null, null, null, null, 0.0000, null, null, null, null, 0.0000, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'SQA854', 'SQA854');

prompt Done.
