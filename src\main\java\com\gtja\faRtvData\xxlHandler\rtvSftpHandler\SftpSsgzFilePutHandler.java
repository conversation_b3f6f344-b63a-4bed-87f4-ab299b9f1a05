package com.gtja.faRtvData.xxlHandler.rtvSftpHandler;

import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.sftp.SftpSsgzFileDealService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @FileName:  SftpSsgzFilePutHandler.java
 * @anthor: Magic
 * @create: 2025/1/21- 16:44
 * @TODO:   //TODO   第一版 使用sftp 文件传输 ，目前不使用  end
 * @Version：1.0
 */
@Slf4j
@Component
public class SftpSsgzFilePutHandler {

    private String RTV_BASE_PATH = "D:\\Users\\User\\Desktop\\实时估值\\YYYYMMDD\\";
    //  @Value("${baseFilePath.sftpTargetPath}")
    protected static String SFTP_PATH = "SSGZ";
    @Autowired
    SftpInfoModel dbinfo;
    @Autowired
    private SftpSsgzFileDealService sftpSsgzFileDealService;

    /**
     * 把实时估值文件夹下的文件copy （put）到sftp 目标路径下
     *
     * @param param
     * @return
     */
    @XxlJob("SftpSsgzFilePutHandler")
    public ReturnT<String> SftpSsgzFilePut(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值 sftp 文件处理 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        try {
            String message = sftpSsgzFileDealService.SftpSsgzFilePut(RTV_BASE_PATH, SFTP_PATH, null, dbinfo);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("sftp文件落库失败：" + e.toString());
            log.info("sftp文件落库失败:{}", e.toString());
        } finally {
            return result;
        }
    }
}
