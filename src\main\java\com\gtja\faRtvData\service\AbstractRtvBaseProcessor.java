package com.gtja.faRtvData.service;

import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.dao.fbzx.RealTimeValueDao;
import com.gtja.faRtvData.dao.fbzx.SsqsFileOutFogDao;
import com.gtja.faRtvData.model.RtvCommonPdfModel;
import com.gtja.faRtvData.model.SsqsFileOutFogModel;
import fileTools.FileTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.io.IOException;
import java.sql.SQLRecoverableException;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * @Method: AbstractMailSortProcessor  邮件智能分拣 抽象类
 * @Descripton :DESC
 * <AUTHOR>
 * @Date :2021/7/2817:40
 * @Return:
 * @Exception :
 */
@Slf4j
public abstract class AbstractRtvBaseProcessor implements IRealTimeValueJob {
    @Autowired
    private RealTimeValueDao realTimeValueDao;
    @Autowired
    private SsqsFileOutFogDao ssqsFileOutFogDao;

    @Value("${baseFilePath.nfsTargetPath}")
    private String nfsTargetPath;

    //记录日志 产品/公共
    public void insertLog(RtvCommonPdfModel param, String type, String successOrFail) {
        SsqsFileOutFogModel model = new SsqsFileOutFogModel();
        model.setCpdm(type);
        model.setId(param.getId());
        model.setBatchId(param.getBatchNo());
        model.setStatus(successOrFail);
        model.setYwrq(param.getYwrq());
        model.setFileName(param.getRtvDbfName());
        model.setFilePath(param.getRtvDbfPath());
        ssqsFileOutFogDao.insertLog(model);
    }

    /**
     * pdf 生成的公共方法 单文件级别
     *
     * @param absolutePath
     * @param data
     * @param tclass
     * @throws IOException
     */
    public abstract void pdfCreateCommonMothod(String fileName, String absolutePath, List<T> data, Class tclass) throws IOException;

    //查询数据集的方法1
    protected abstract List<T> queryRtvDataList(String fileName) throws Exception;

    //查询数据集的方法1
    protected abstract List<T> queryRtvDataList(String fileName, String cpdm, String ywrq) throws Exception;
    //@Override


    /**
     * @demoName: rtvCpdmFileProcessor
     * @Anthor: Magic
     * @Create: 2025/4/14- 15:38
     * @TODO: //TODO  以产品为维度生成文件 end
     * @Param:
     * @Return:
     */
    @Override
    // @Transactional(value = "fbzxTransactionManager", propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void rtvCpdmFileProcessor(ExecutorService executor, String ywrq, List<RtvCommonPdfModel> fileModels, RtvCommonPdfModel cpdmModel) throws Exception {
        CountDownLatch unHandleLatch = new CountDownLatch(fileModels.size());
        fileModels.stream().forEach(param -> {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    //是否需要产品层目录
                    param.setYwrq(ywrq);
                    param.setRtvDbfPath(nfsTargetPath.replace("YYYYMMDD", ywrq));
                    String rtvDbfName = param.getRtvDbfName();

                    log.info(cpdmModel.getCpdm() + "：开始生成实时估值dbf:" + rtvDbfName);
                    if (param.getType().equals(Constants.DBF_FILE_TYPE.CPDM)) {
                        String AbsolutePath = (param.getRtvDbfPath() + "CPDM\\" + "BATCH_NO" + "\\" + param.getRtvDbfName()).replace("YYYYMMDD", param.getYwrq());
                        //需要根据产品生成pdf
                        //2.查询数据
                        List<T> data = null;
                        String id = UUID.randomUUID().toString();
                        try {
                            data = queryRtvDataList(param.getRtvDbfName(), cpdmModel.getCpdm(), param.getYwrq());
                            String createPath = AbsolutePath.replace("CPDM", cpdmModel.getCpdm()).replace("BATCH_NO", cpdmModel.getBatchNo());
                            param.setRtvDbfPath(createPath);
                            param.setId(id);
                            param.setBatchNo(cpdmModel.getBatchNo());
                            insertLog(param, cpdmModel.getCpdm(), "0");
                            pdfCreateCommonMothod(param.getRtvDbfName(), createPath, data, T.class);
                        } catch (IOException e) {
                            log.error("生成产品{}业务日期{}数据异常", cpdmModel.getCpdm(), param.getYwrq(), e.getMessage());
                            insertLog(param, cpdmModel.getCpdm(), "1");
                            //throw new RuntimeException(e);
                        } catch (SQLRecoverableException ex) {
                            log.info("查询产品{}业务日期{},链接占满：{}", cpdmModel.getCpdm(), param.getYwrq(), ex.getMessage());
                            ex.printStackTrace();
                        } catch (Exception e) {
                            log.error("查询产品{}业务日期{}数据异常", cpdmModel.getCpdm(), param.getYwrq(), e.getMessage());
                        } finally {
                            unHandleLatch.countDown();
                        }
                    }
                }
            });
        });
        try {
            unHandleLatch.await();
        } catch (InterruptedException e) {
            log.error(cpdmModel.getCpdm() + "：dbf文件生成异常：" + e.getMessage());
        }
        log.info(cpdmModel.getCpdm() + "：dbf文件生成结束，开始生成。ok 文件");
        String id = UUID.randomUUID().toString();
        cpdmModel.setId(id);
        cpdmModel.setYwrq(ywrq);
        try {
            String createPath = nfsTargetPath.replace("YYYYMMDD", ywrq);
            cpdmModel.setRtvDbfPath(createPath);
            cpdmModel.setRtvDbfName(cpdmModel.getCpdm() + "_" + ywrq + "_" + cpdmModel.getBatchNo() + ".OK");
            insertLog(cpdmModel, cpdmModel.getCpdm(), "0");
            FileTools.createFileIfNotExists((createPath + cpdmModel.getRtvDbfName()));
        } catch (IOException e) {
            log.info("实时估值.OK文件创建失败：" + e.toString());
            insertLog(cpdmModel, cpdmModel.getCpdm(), "1");
        }
        log.info("实时估值产品{}文件生成完成", cpdmModel.getCpdm());
    }


    /**
     * @demoName: realTimeValueProcessor
     * @Anthor: Magic
     * @Create: 2025/2/19- 17:18
     * @TODO: //TODO   end
     * @Param:
     * @Return:
     */
    @Override
    public String rtvGGFileProcessor(String ywrq, RtvCommonPdfModel param, List<RtvCommonPdfModel> cpdmModels) throws Exception {
        //是否需要产品层目录
        param.setYwrq(ywrq);
        param.setRtvDbfPath(nfsTargetPath.replace("YYYYMMDD", ywrq));
        String rtvDbfName = param.getRtvDbfName();
        //1.查询 产品代码 如果产品代码为空，则不重复生成公共文件及公共的ok文件
        //List<RtvCommonPdfModel> cpdmModels = realTimeValueDao.queryRealTimeValueCpConfig(param.getYwrq());
        if (cpdmModels.size() > 0) {
            log.info("开始生成实时估值dbf:" + rtvDbfName);
            if (param.getType().equals(Constants.DBF_FILE_TYPE.CPDM)) {
                String AbsolutePath = (param.getRtvDbfPath() + "CPDM\\" + "BATCH_NO" + "\\" + param.getRtvDbfName()).replace("YYYYMMDD", param.getYwrq());
                //需要根据产品生成pdf
                //2.查询数据
                cpdmModels.stream().forEach(cpdmModel -> {
                    List<T> data = null;
                    String id = UUID.randomUUID().toString();
                    try {
                        data = queryRtvDataList(param.getRtvDbfName(), cpdmModel.getCpdm(), param.getYwrq());
                    } catch (SQLRecoverableException ex) {
                        log.info("查询产品{}业务日期{},链接占满：{}", cpdmModel.getCpdm(), param.getYwrq(), ex.getMessage());
                        ex.printStackTrace();
                    } catch (Exception e) {
                        log.info("查询产品{}业务日期{}数据异常", cpdmModel.getCpdm(), param.getYwrq(), e.getMessage());
                        throw new RuntimeException(e);
                    }
                    //生成pdf
                    try {
                        String createPath = AbsolutePath.replace("CPDM", cpdmModel.getCpdm()).replace("BATCH_NO", cpdmModel.getBatchNo());
                        param.setRtvDbfPath(createPath);
                        param.setId(id);
                        param.setBatchNo(cpdmModel.getBatchNo());
                        insertLog(param, cpdmModel.getCpdm(), "0");
                        pdfCreateCommonMothod(param.getRtvDbfName(), createPath, data, T.class);
                    } catch (IOException e) {
                        log.info("生成产品{}业务日期{}数据异常", cpdmModel.getCpdm(), param.getYwrq(), e.getMessage());
                        insertLog(param, cpdmModel.getCpdm(), "1");
                        throw new RuntimeException(e);
                    }
                });
            } else if (param.getType().equals(Constants.DBF_FILE_TYPE.COMMON)) {
                String AbsolutePath = (param.getRtvDbfPath() + param.getRtvDbfName()).replace("YYYYMMDD", param.getYwrq());
                //不需要根据产品生成pdf
                List<T> data = queryRtvDataList(param.getRtvDbfName());
                String id = UUID.randomUUID().toString();
                try {
                    param.setId(id);
                    param.setBatchNo("1");//默认 从1 开始
                    param.setRtvDbfPath(AbsolutePath);
                    insertLog(param, "GG", "0");
                    pdfCreateCommonMothod(param.getRtvDbfName(), AbsolutePath, data, T.class);
                } catch (IOException e) {
                    insertLog(param, "GG", "1");
                    // ssqsFileOutFogDao.updateFileLog(id);
                    // insertLog(param, "GG", "1");
                    throw new RuntimeException(e);
                }
            }
        }
        return rtvDbfName;
    }

}
