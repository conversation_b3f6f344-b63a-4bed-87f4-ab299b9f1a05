<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.SsqsFileOutFogDao">
    <resultMap id="BaseResultMap" type="com.gtja.faRtvData.model.SsqsFileOutFogModel">
        <result column="YWRQ" jdbcType="VARCHAR" property="ywrq"/>
        <result column="CPDM" jdbcType="VARCHAR" property="cpdm"/>
        <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName"/>
        <result column="FILE_PATH" jdbcType="VARCHAR" property="filePath"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="createDate"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="ID" jdbcType="VARCHAR" property="id"/>
        <result column="BATCH_ID" jdbcType="DECIMAL" property="batchId"/>
        <result column="pjpath" jdbcType="DECIMAL" property="pjPath"/>
    </resultMap>

    <sql id="Base_Column_List">
        YWRQ
        , CPDM, FILE_NAME, FILE_PATH, CREATE_DATE, STATUS,ID,BATCH_ID
    </sql>

    <select id="selectSsgzLog" parameterType="com.gtja.faRtvData.model.SsqsFileOutFogModel" resultMap="BaseResultMap">
        select t.id
        ,t.ywrq
        ,t.cpdm
        ,t.file_name
        ,t.file_path
        ,t.batch_no
        ,replace(substr(t.file_path,instr(t.file_path,t.ywrq),(instr(t.file_path,'\',-1)+1-instr(t.file_path,t.ywrq))),'\','/') pjPath
        from gtja_ssgz_file_out_log t
        where 1=1
        <if test="ywrq!=null or ywrq !=''">
            and t.ywrq = #{ywrq}
        </if>
        and t.status &lt;>1
    </select>


    <delete id="deleteByExample" parameterType="com.gtja.faRtvData.model.SsqsFileOutFogModel">
        delete
        from fbzx.gtja_ssgz_file_out_log
    </delete>

    <insert id="insertLog" parameterType="com.gtja.faRtvData.model.SsqsFileOutFogModel">
        insert into fbzx.gtja_ssgz_file_out_log (YWRQ, CPDM, FILE_NAME,
                                                 FILE_PATH, CREATE_DATE, STATUS, id, batch_no)
        values (#{ywrq,jdbcType=VARCHAR}, #{cpdm,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR},
                #{filePath,jdbcType=VARCHAR}, SYSDATE, #{status,jdbcType=VARCHAR}, #{id}, #{batchId})
    </insert>

    <update id="updateFileLog" parameterType="java.lang.String">
        update FBZX.gtja_ssgz_file_out_log t
        set t.status='1'
        where t.id = #{id}
    </update>

    <update id="updateFileLogByIds" parameterType="com.gtja.faRtvData.model.SsqsFileOutFogModel">
        <foreach collection="infos" item="item" index="index" open="begin" close=";end;" separator=";">
            update FBZX.gtja_ssgz_file_out_log t set t.status=#{item.status} where t.id=#{item.id}
        </foreach>
    </update>

    <insert id="insertSelective" parameterType="com.gtja.faRtvData.model.SsqsFileOutFogModel">
        insert into FBZX.gtja_ssgz_file_out_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ywrq != null">
                YWRQ,
            </if>
            <if test="cpdm != null">
                CPDM,
            </if>
            <if test="fileName != null">
                FILE_NAME,
            </if>
            <if test="filePath != null">
                FILE_PATH,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="status != null">
                STATUS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ywrq != null">
                #{ywrq,jdbcType=VARCHAR},
            </if>
            <if test="cpdm != null">
                #{cpdm,jdbcType=VARCHAR},
            </if>
            <if test="fileName != null">
                #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="filePath != null">
                #{filePath,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=DATE},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

</mapper>