package com.gtja.faRtvData.service.sftp.impl;

import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.sftp.SftpSsgzFileDealService;
import com.gtja.faRtvData.common.utils.fileDownLoad.FileTools;
import com.gtja.faRtvData.common.utils.sftp.SftpUtils;
import com.jcraft.jsch.ChannelSftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.OutputStream;
import java.util.List;
import java.util.Vector;

@Service
@Slf4j
public class SftpSsgzFileDealServiceImpl implements SftpSsgzFileDealService {


    @Autowired
    SftpUtils sftpUtils;
    @Autowired
    FileTools fileTools;

    /**
     * 上传制定spft 文件
     *
     * @param sourcePath
     * @param targetPath
     * @param fileName
     * @param sftpSendConfig
     * @return
     */
    public String SftpSsgzFilePut(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpSendConfig) throws Exception {
        ChannelSftp sftp = sftpUtils.getChannelSftp(sftpSendConfig);

        log.info("connect sftp success, Change path to {}", sourcePath);
        if (StringUtils.isEmpty(fileName)) {
            sftp.put(sourcePath, targetPath + sourcePath);
            List<java.io.File> list = fileTools.getAllFile(sourcePath);
            //循环下载
            for (File file : list) {
                sftp.put(file.getAbsoluteFile() + file.getName(), targetPath + file.getAbsoluteFile() + file.getName());
            }
        } else {
            sftp.put(fileName, targetPath + fileName);
        }
        log.info("Download file success. sourcePath = {}, targetPath = {}， fileName = {}.", sourcePath, targetPath, fileName);

        return "文件上传成功";
    }

    /**
     * 下载指定文件
     *
     * @param sourcePath     待下载的源文件
     * @param targetPath     下载的路径
     * @param fileName       下载的文件名称，若无指定文件名称，则下载该路径下的所有文件
     * @param sftpSendConfig
     * @return
     * @throws Exception
     */
    @Override
    public boolean downloadFile(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpSendConfig) throws Exception {
        ChannelSftp sftp = sftpUtils.getChannelSftp(sftpSendConfig);
        OutputStream outputStream = null;
        try {
            sftpUtils.changeDirectory(sourcePath, sftp);
            log.info("connect sftp success, Change path to {}", sourcePath);
            if (StringUtils.isEmpty(fileName)) {
                Vector<ChannelSftp.LsEntry> list = sftp.ls(sourcePath);
                //循环下载
                for (ChannelSftp.LsEntry lsEntry : list) {
                    sftp.get(lsEntry.getFilename(), targetPath + lsEntry.getFilename());
                }
            } else {
                sftp.get(fileName, targetPath + fileName);
            }
            log.info("Download file success. sourcePath = {}, targetPath = {}， fileName = {}.", sourcePath, targetPath, fileName);
        } catch (Exception e) {
            log.error("Download file failure. sourcePath = {}, targetPath = {}， fileName = {}.", sourcePath, targetPath, fileName, e);
            throw new Exception("sftp download file failure, sourcePath = " + sourcePath + ", targetPath = " + targetPath + ", fileName = " + fileName);
        } finally {
            if (outputStream != null) {
                outputStream.close();
            }
            sftpUtils.disconnect(sftp);
        }
        return false;
    }
}
