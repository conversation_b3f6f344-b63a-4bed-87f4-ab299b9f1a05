package com.gtja.faRtvData.model;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class GtjaSsgzPointModel  implements Serializable {
    private Integer busi_date;

    //private String msg_id;
    String MSG_ID ;
    String MSG_GROUP_SNO ;
    String MSG_GROUP_ID ;
    private String account_set_no;

    private String ccy_code;
    private BigDecimal z052001;

    private BigDecimal z052002;

    private BigDecimal z052003;

    private BigDecimal z052004;

    private BigDecimal z052005;

    private BigDecimal z052006;

    private BigDecimal z052007;

    private BigDecimal z052008;


    private BigDecimal z053001;
    private BigDecimal z053002;
    private BigDecimal z053003;
    private BigDecimal z053004;
    private BigDecimal z053005;
    private BigDecimal z053006;
}