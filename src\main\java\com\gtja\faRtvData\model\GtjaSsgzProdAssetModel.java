package generator.create;

import java.math.BigDecimal;

public class GtjaSsgzProdAssetModel {
    private String msgId;

    private BigDecimal busiDate;

    private String accountSetNo;

    private String ccyCode;

    private BigDecimal exchRate;

    private BigDecimal f100201;

    private BigDecimal f100211;

    private BigDecimal f100212;

    private BigDecimal f100213;

    private BigDecimal f100214;

    private BigDecimal f102101;

    private BigDecimal f102111;

    private BigDecimal f102112;

    private BigDecimal f102113;

    private BigDecimal f103101;

    private BigDecimal f103111;

    private BigDecimal f103121;

    private BigDecimal f103131;

    private BigDecimal f103141;

    private BigDecimal f110201;

    private BigDecimal f110301;

    private BigDecimal f110401;

    private BigDecimal f110501;

    private BigDecimal f110601;

    private BigDecimal f110901;

    private BigDecimal f111001;

    private BigDecimal f120201;

    private BigDecimal f120301;

    private BigDecimal f120401;

    private BigDecimal f120499;

    private BigDecimal f120701;

    private BigDecimal f122101;

    private BigDecimal f130301;

    private BigDecimal f150101;

    private BigDecimal f151101;

    private BigDecimal f152101;

    private BigDecimal f160101;

    private BigDecimal f160601;

    private BigDecimal f170101;

    private BigDecimal f200101;

    private BigDecimal f210101;

    private BigDecimal f220201;

    private BigDecimal f220301;

    private BigDecimal f220401;

    private BigDecimal f220501;

    private BigDecimal f220601;

    private BigDecimal f220701;

    private BigDecimal f220801;

    private BigDecimal f220901;

    private BigDecimal f220902;

    private BigDecimal f220903;

    private BigDecimal f222101;

    private BigDecimal f223101;

    private BigDecimal f223201;

    private BigDecimal f224101;

    private BigDecimal f224199;

    private BigDecimal f300301;

    private BigDecimal f300302;

    private BigDecimal f300303;

    private BigDecimal f300304;

    private BigDecimal f300305;

    private BigDecimal f300306;

    private BigDecimal f300307;

    private BigDecimal f300308;

    private BigDecimal f300309;

    private BigDecimal f300310;

    private BigDecimal f300311;

    private BigDecimal f300312;

    private BigDecimal f300313;

    private BigDecimal f300314;

    private BigDecimal f300315;

    private BigDecimal f300316;

    private BigDecimal f300317;

    private BigDecimal f300318;

    private BigDecimal f300319;

    private BigDecimal f300320;

    private BigDecimal f300321;

    private BigDecimal f300330;

    private BigDecimal f300331;

    private BigDecimal f400101;

    private BigDecimal f400102;

    private BigDecimal f400103;

    private BigDecimal f400199;

    private BigDecimal f400201;

    private BigDecimal f400301;

    private BigDecimal f401101;

    private BigDecimal f401102;

    private BigDecimal f410301;

    private BigDecimal f410302;

    private BigDecimal f410401;

    private BigDecimal f410402;

    private BigDecimal f410411;

    private BigDecimal f410421;

    private BigDecimal f410431;

    private BigDecimal f601101;

    private BigDecimal f601102;

    private BigDecimal f601104;

    private BigDecimal f601198;

    private BigDecimal f601199;

    private BigDecimal f606101;

    private BigDecimal f610101;

    private BigDecimal f610198;

    private BigDecimal f611101;

    private BigDecimal f611102;

    private BigDecimal f611103;

    private BigDecimal f611104;

    private BigDecimal f611106;

    private BigDecimal f611107;

    private BigDecimal f611108;

    private BigDecimal f611198;

    private BigDecimal f611199;

    private BigDecimal f630299;

    private BigDecimal f640301;

    private BigDecimal f640401;

    private BigDecimal f640501;

    private BigDecimal f640601;

    private BigDecimal f640701;

    private BigDecimal f641101;

    private BigDecimal f641199;

    private BigDecimal f660501;

    private BigDecimal f660599;

    private BigDecimal f670101;

    private BigDecimal f680201;

    private BigDecimal f820201;

    private BigDecimal f820203;

    private BigDecimal f820204;

    private BigDecimal f820205;

    private BigDecimal f820303;

    private BigDecimal f820304;

    private BigDecimal f820305;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public BigDecimal getBusiDate() {
        return busiDate;
    }

    public void setBusiDate(BigDecimal busiDate) {
        this.busiDate = busiDate;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getCcyCode() {
        return ccyCode;
    }

    public void setCcyCode(String ccyCode) {
        this.ccyCode = ccyCode;
    }

    public BigDecimal getExchRate() {
        return exchRate;
    }

    public void setExchRate(BigDecimal exchRate) {
        this.exchRate = exchRate;
    }

    public BigDecimal getF100201() {
        return f100201;
    }

    public void setF100201(BigDecimal f100201) {
        this.f100201 = f100201;
    }

    public BigDecimal getF100211() {
        return f100211;
    }

    public void setF100211(BigDecimal f100211) {
        this.f100211 = f100211;
    }

    public BigDecimal getF100212() {
        return f100212;
    }

    public void setF100212(BigDecimal f100212) {
        this.f100212 = f100212;
    }

    public BigDecimal getF100213() {
        return f100213;
    }

    public void setF100213(BigDecimal f100213) {
        this.f100213 = f100213;
    }

    public BigDecimal getF100214() {
        return f100214;
    }

    public void setF100214(BigDecimal f100214) {
        this.f100214 = f100214;
    }

    public BigDecimal getF102101() {
        return f102101;
    }

    public void setF102101(BigDecimal f102101) {
        this.f102101 = f102101;
    }

    public BigDecimal getF102111() {
        return f102111;
    }

    public void setF102111(BigDecimal f102111) {
        this.f102111 = f102111;
    }

    public BigDecimal getF102112() {
        return f102112;
    }

    public void setF102112(BigDecimal f102112) {
        this.f102112 = f102112;
    }

    public BigDecimal getF102113() {
        return f102113;
    }

    public void setF102113(BigDecimal f102113) {
        this.f102113 = f102113;
    }

    public BigDecimal getF103101() {
        return f103101;
    }

    public void setF103101(BigDecimal f103101) {
        this.f103101 = f103101;
    }

    public BigDecimal getF103111() {
        return f103111;
    }

    public void setF103111(BigDecimal f103111) {
        this.f103111 = f103111;
    }

    public BigDecimal getF103121() {
        return f103121;
    }

    public void setF103121(BigDecimal f103121) {
        this.f103121 = f103121;
    }

    public BigDecimal getF103131() {
        return f103131;
    }

    public void setF103131(BigDecimal f103131) {
        this.f103131 = f103131;
    }

    public BigDecimal getF103141() {
        return f103141;
    }

    public void setF103141(BigDecimal f103141) {
        this.f103141 = f103141;
    }

    public BigDecimal getF110201() {
        return f110201;
    }

    public void setF110201(BigDecimal f110201) {
        this.f110201 = f110201;
    }

    public BigDecimal getF110301() {
        return f110301;
    }

    public void setF110301(BigDecimal f110301) {
        this.f110301 = f110301;
    }

    public BigDecimal getF110401() {
        return f110401;
    }

    public void setF110401(BigDecimal f110401) {
        this.f110401 = f110401;
    }

    public BigDecimal getF110501() {
        return f110501;
    }

    public void setF110501(BigDecimal f110501) {
        this.f110501 = f110501;
    }

    public BigDecimal getF110601() {
        return f110601;
    }

    public void setF110601(BigDecimal f110601) {
        this.f110601 = f110601;
    }

    public BigDecimal getF110901() {
        return f110901;
    }

    public void setF110901(BigDecimal f110901) {
        this.f110901 = f110901;
    }

    public BigDecimal getF111001() {
        return f111001;
    }

    public void setF111001(BigDecimal f111001) {
        this.f111001 = f111001;
    }

    public BigDecimal getF120201() {
        return f120201;
    }

    public void setF120201(BigDecimal f120201) {
        this.f120201 = f120201;
    }

    public BigDecimal getF120301() {
        return f120301;
    }

    public void setF120301(BigDecimal f120301) {
        this.f120301 = f120301;
    }

    public BigDecimal getF120401() {
        return f120401;
    }

    public void setF120401(BigDecimal f120401) {
        this.f120401 = f120401;
    }

    public BigDecimal getF120499() {
        return f120499;
    }

    public void setF120499(BigDecimal f120499) {
        this.f120499 = f120499;
    }

    public BigDecimal getF120701() {
        return f120701;
    }

    public void setF120701(BigDecimal f120701) {
        this.f120701 = f120701;
    }

    public BigDecimal getF122101() {
        return f122101;
    }

    public void setF122101(BigDecimal f122101) {
        this.f122101 = f122101;
    }

    public BigDecimal getF130301() {
        return f130301;
    }

    public void setF130301(BigDecimal f130301) {
        this.f130301 = f130301;
    }

    public BigDecimal getF150101() {
        return f150101;
    }

    public void setF150101(BigDecimal f150101) {
        this.f150101 = f150101;
    }

    public BigDecimal getF151101() {
        return f151101;
    }

    public void setF151101(BigDecimal f151101) {
        this.f151101 = f151101;
    }

    public BigDecimal getF152101() {
        return f152101;
    }

    public void setF152101(BigDecimal f152101) {
        this.f152101 = f152101;
    }

    public BigDecimal getF160101() {
        return f160101;
    }

    public void setF160101(BigDecimal f160101) {
        this.f160101 = f160101;
    }

    public BigDecimal getF160601() {
        return f160601;
    }

    public void setF160601(BigDecimal f160601) {
        this.f160601 = f160601;
    }

    public BigDecimal getF170101() {
        return f170101;
    }

    public void setF170101(BigDecimal f170101) {
        this.f170101 = f170101;
    }

    public BigDecimal getF200101() {
        return f200101;
    }

    public void setF200101(BigDecimal f200101) {
        this.f200101 = f200101;
    }

    public BigDecimal getF210101() {
        return f210101;
    }

    public void setF210101(BigDecimal f210101) {
        this.f210101 = f210101;
    }

    public BigDecimal getF220201() {
        return f220201;
    }

    public void setF220201(BigDecimal f220201) {
        this.f220201 = f220201;
    }

    public BigDecimal getF220301() {
        return f220301;
    }

    public void setF220301(BigDecimal f220301) {
        this.f220301 = f220301;
    }

    public BigDecimal getF220401() {
        return f220401;
    }

    public void setF220401(BigDecimal f220401) {
        this.f220401 = f220401;
    }

    public BigDecimal getF220501() {
        return f220501;
    }

    public void setF220501(BigDecimal f220501) {
        this.f220501 = f220501;
    }

    public BigDecimal getF220601() {
        return f220601;
    }

    public void setF220601(BigDecimal f220601) {
        this.f220601 = f220601;
    }

    public BigDecimal getF220701() {
        return f220701;
    }

    public void setF220701(BigDecimal f220701) {
        this.f220701 = f220701;
    }

    public BigDecimal getF220801() {
        return f220801;
    }

    public void setF220801(BigDecimal f220801) {
        this.f220801 = f220801;
    }

    public BigDecimal getF220901() {
        return f220901;
    }

    public void setF220901(BigDecimal f220901) {
        this.f220901 = f220901;
    }

    public BigDecimal getF220902() {
        return f220902;
    }

    public void setF220902(BigDecimal f220902) {
        this.f220902 = f220902;
    }

    public BigDecimal getF220903() {
        return f220903;
    }

    public void setF220903(BigDecimal f220903) {
        this.f220903 = f220903;
    }

    public BigDecimal getF222101() {
        return f222101;
    }

    public void setF222101(BigDecimal f222101) {
        this.f222101 = f222101;
    }

    public BigDecimal getF223101() {
        return f223101;
    }

    public void setF223101(BigDecimal f223101) {
        this.f223101 = f223101;
    }

    public BigDecimal getF223201() {
        return f223201;
    }

    public void setF223201(BigDecimal f223201) {
        this.f223201 = f223201;
    }

    public BigDecimal getF224101() {
        return f224101;
    }

    public void setF224101(BigDecimal f224101) {
        this.f224101 = f224101;
    }

    public BigDecimal getF224199() {
        return f224199;
    }

    public void setF224199(BigDecimal f224199) {
        this.f224199 = f224199;
    }

    public BigDecimal getF300301() {
        return f300301;
    }

    public void setF300301(BigDecimal f300301) {
        this.f300301 = f300301;
    }

    public BigDecimal getF300302() {
        return f300302;
    }

    public void setF300302(BigDecimal f300302) {
        this.f300302 = f300302;
    }

    public BigDecimal getF300303() {
        return f300303;
    }

    public void setF300303(BigDecimal f300303) {
        this.f300303 = f300303;
    }

    public BigDecimal getF300304() {
        return f300304;
    }

    public void setF300304(BigDecimal f300304) {
        this.f300304 = f300304;
    }

    public BigDecimal getF300305() {
        return f300305;
    }

    public void setF300305(BigDecimal f300305) {
        this.f300305 = f300305;
    }

    public BigDecimal getF300306() {
        return f300306;
    }

    public void setF300306(BigDecimal f300306) {
        this.f300306 = f300306;
    }

    public BigDecimal getF300307() {
        return f300307;
    }

    public void setF300307(BigDecimal f300307) {
        this.f300307 = f300307;
    }

    public BigDecimal getF300308() {
        return f300308;
    }

    public void setF300308(BigDecimal f300308) {
        this.f300308 = f300308;
    }

    public BigDecimal getF300309() {
        return f300309;
    }

    public void setF300309(BigDecimal f300309) {
        this.f300309 = f300309;
    }

    public BigDecimal getF300310() {
        return f300310;
    }

    public void setF300310(BigDecimal f300310) {
        this.f300310 = f300310;
    }

    public BigDecimal getF300311() {
        return f300311;
    }

    public void setF300311(BigDecimal f300311) {
        this.f300311 = f300311;
    }

    public BigDecimal getF300312() {
        return f300312;
    }

    public void setF300312(BigDecimal f300312) {
        this.f300312 = f300312;
    }

    public BigDecimal getF300313() {
        return f300313;
    }

    public void setF300313(BigDecimal f300313) {
        this.f300313 = f300313;
    }

    public BigDecimal getF300314() {
        return f300314;
    }

    public void setF300314(BigDecimal f300314) {
        this.f300314 = f300314;
    }

    public BigDecimal getF300315() {
        return f300315;
    }

    public void setF300315(BigDecimal f300315) {
        this.f300315 = f300315;
    }

    public BigDecimal getF300316() {
        return f300316;
    }

    public void setF300316(BigDecimal f300316) {
        this.f300316 = f300316;
    }

    public BigDecimal getF300317() {
        return f300317;
    }

    public void setF300317(BigDecimal f300317) {
        this.f300317 = f300317;
    }

    public BigDecimal getF300318() {
        return f300318;
    }

    public void setF300318(BigDecimal f300318) {
        this.f300318 = f300318;
    }

    public BigDecimal getF300319() {
        return f300319;
    }

    public void setF300319(BigDecimal f300319) {
        this.f300319 = f300319;
    }

    public BigDecimal getF300320() {
        return f300320;
    }

    public void setF300320(BigDecimal f300320) {
        this.f300320 = f300320;
    }

    public BigDecimal getF300321() {
        return f300321;
    }

    public void setF300321(BigDecimal f300321) {
        this.f300321 = f300321;
    }

    public BigDecimal getF300330() {
        return f300330;
    }

    public void setF300330(BigDecimal f300330) {
        this.f300330 = f300330;
    }

    public BigDecimal getF300331() {
        return f300331;
    }

    public void setF300331(BigDecimal f300331) {
        this.f300331 = f300331;
    }

    public BigDecimal getF400101() {
        return f400101;
    }

    public void setF400101(BigDecimal f400101) {
        this.f400101 = f400101;
    }

    public BigDecimal getF400102() {
        return f400102;
    }

    public void setF400102(BigDecimal f400102) {
        this.f400102 = f400102;
    }

    public BigDecimal getF400103() {
        return f400103;
    }

    public void setF400103(BigDecimal f400103) {
        this.f400103 = f400103;
    }

    public BigDecimal getF400199() {
        return f400199;
    }

    public void setF400199(BigDecimal f400199) {
        this.f400199 = f400199;
    }

    public BigDecimal getF400201() {
        return f400201;
    }

    public void setF400201(BigDecimal f400201) {
        this.f400201 = f400201;
    }

    public BigDecimal getF400301() {
        return f400301;
    }

    public void setF400301(BigDecimal f400301) {
        this.f400301 = f400301;
    }

    public BigDecimal getF401101() {
        return f401101;
    }

    public void setF401101(BigDecimal f401101) {
        this.f401101 = f401101;
    }

    public BigDecimal getF401102() {
        return f401102;
    }

    public void setF401102(BigDecimal f401102) {
        this.f401102 = f401102;
    }

    public BigDecimal getF410301() {
        return f410301;
    }

    public void setF410301(BigDecimal f410301) {
        this.f410301 = f410301;
    }

    public BigDecimal getF410302() {
        return f410302;
    }

    public void setF410302(BigDecimal f410302) {
        this.f410302 = f410302;
    }

    public BigDecimal getF410401() {
        return f410401;
    }

    public void setF410401(BigDecimal f410401) {
        this.f410401 = f410401;
    }

    public BigDecimal getF410402() {
        return f410402;
    }

    public void setF410402(BigDecimal f410402) {
        this.f410402 = f410402;
    }

    public BigDecimal getF410411() {
        return f410411;
    }

    public void setF410411(BigDecimal f410411) {
        this.f410411 = f410411;
    }

    public BigDecimal getF410421() {
        return f410421;
    }

    public void setF410421(BigDecimal f410421) {
        this.f410421 = f410421;
    }

    public BigDecimal getF410431() {
        return f410431;
    }

    public void setF410431(BigDecimal f410431) {
        this.f410431 = f410431;
    }

    public BigDecimal getF601101() {
        return f601101;
    }

    public void setF601101(BigDecimal f601101) {
        this.f601101 = f601101;
    }

    public BigDecimal getF601102() {
        return f601102;
    }

    public void setF601102(BigDecimal f601102) {
        this.f601102 = f601102;
    }

    public BigDecimal getF601104() {
        return f601104;
    }

    public void setF601104(BigDecimal f601104) {
        this.f601104 = f601104;
    }

    public BigDecimal getF601198() {
        return f601198;
    }

    public void setF601198(BigDecimal f601198) {
        this.f601198 = f601198;
    }

    public BigDecimal getF601199() {
        return f601199;
    }

    public void setF601199(BigDecimal f601199) {
        this.f601199 = f601199;
    }

    public BigDecimal getF606101() {
        return f606101;
    }

    public void setF606101(BigDecimal f606101) {
        this.f606101 = f606101;
    }

    public BigDecimal getF610101() {
        return f610101;
    }

    public void setF610101(BigDecimal f610101) {
        this.f610101 = f610101;
    }

    public BigDecimal getF610198() {
        return f610198;
    }

    public void setF610198(BigDecimal f610198) {
        this.f610198 = f610198;
    }

    public BigDecimal getF611101() {
        return f611101;
    }

    public void setF611101(BigDecimal f611101) {
        this.f611101 = f611101;
    }

    public BigDecimal getF611102() {
        return f611102;
    }

    public void setF611102(BigDecimal f611102) {
        this.f611102 = f611102;
    }

    public BigDecimal getF611103() {
        return f611103;
    }

    public void setF611103(BigDecimal f611103) {
        this.f611103 = f611103;
    }

    public BigDecimal getF611104() {
        return f611104;
    }

    public void setF611104(BigDecimal f611104) {
        this.f611104 = f611104;
    }

    public BigDecimal getF611106() {
        return f611106;
    }

    public void setF611106(BigDecimal f611106) {
        this.f611106 = f611106;
    }

    public BigDecimal getF611107() {
        return f611107;
    }

    public void setF611107(BigDecimal f611107) {
        this.f611107 = f611107;
    }

    public BigDecimal getF611108() {
        return f611108;
    }

    public void setF611108(BigDecimal f611108) {
        this.f611108 = f611108;
    }

    public BigDecimal getF611198() {
        return f611198;
    }

    public void setF611198(BigDecimal f611198) {
        this.f611198 = f611198;
    }

    public BigDecimal getF611199() {
        return f611199;
    }

    public void setF611199(BigDecimal f611199) {
        this.f611199 = f611199;
    }

    public BigDecimal getF630299() {
        return f630299;
    }

    public void setF630299(BigDecimal f630299) {
        this.f630299 = f630299;
    }

    public BigDecimal getF640301() {
        return f640301;
    }

    public void setF640301(BigDecimal f640301) {
        this.f640301 = f640301;
    }

    public BigDecimal getF640401() {
        return f640401;
    }

    public void setF640401(BigDecimal f640401) {
        this.f640401 = f640401;
    }

    public BigDecimal getF640501() {
        return f640501;
    }

    public void setF640501(BigDecimal f640501) {
        this.f640501 = f640501;
    }

    public BigDecimal getF640601() {
        return f640601;
    }

    public void setF640601(BigDecimal f640601) {
        this.f640601 = f640601;
    }

    public BigDecimal getF640701() {
        return f640701;
    }

    public void setF640701(BigDecimal f640701) {
        this.f640701 = f640701;
    }

    public BigDecimal getF641101() {
        return f641101;
    }

    public void setF641101(BigDecimal f641101) {
        this.f641101 = f641101;
    }

    public BigDecimal getF641199() {
        return f641199;
    }

    public void setF641199(BigDecimal f641199) {
        this.f641199 = f641199;
    }

    public BigDecimal getF660501() {
        return f660501;
    }

    public void setF660501(BigDecimal f660501) {
        this.f660501 = f660501;
    }

    public BigDecimal getF660599() {
        return f660599;
    }

    public void setF660599(BigDecimal f660599) {
        this.f660599 = f660599;
    }

    public BigDecimal getF670101() {
        return f670101;
    }

    public void setF670101(BigDecimal f670101) {
        this.f670101 = f670101;
    }

    public BigDecimal getF680201() {
        return f680201;
    }

    public void setF680201(BigDecimal f680201) {
        this.f680201 = f680201;
    }

    public BigDecimal getF820201() {
        return f820201;
    }

    public void setF820201(BigDecimal f820201) {
        this.f820201 = f820201;
    }

    public BigDecimal getF820203() {
        return f820203;
    }

    public void setF820203(BigDecimal f820203) {
        this.f820203 = f820203;
    }

    public BigDecimal getF820204() {
        return f820204;
    }

    public void setF820204(BigDecimal f820204) {
        this.f820204 = f820204;
    }

    public BigDecimal getF820205() {
        return f820205;
    }

    public void setF820205(BigDecimal f820205) {
        this.f820205 = f820205;
    }

    public BigDecimal getF820303() {
        return f820303;
    }

    public void setF820303(BigDecimal f820303) {
        this.f820303 = f820303;
    }

    public BigDecimal getF820304() {
        return f820304;
    }

    public void setF820304(BigDecimal f820304) {
        this.f820304 = f820304;
    }

    public BigDecimal getF820305() {
        return f820305;
    }

    public void setF820305(BigDecimal f820305) {
        this.f820305 = f820305;
    }
}