package com.gtja.faRtvData.common;

import com.gtja.faRtvData.common.exception.IErrorCodeType;
import com.gtja.faRtvData.common.ienum.ErrorCode;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @Description: TODO  返回请求的封装返回类
 */
@Data
@ToString
public class ResponseObj<T> implements Serializable {
    private static final long serialVersionUID = -6324077546420954309L;
    public static final String CODE_FORMAT = "%s-%s-%d";

    /*
     * 标识成功或失败
     */
    private boolean success = false;
    /**
     * 错误编码
     */
    private String code;
    /**
     * 描述
     */
    private String message;
    /**
     * 异常信息
     */
    private String cause;
    /**
     * 具体业务
     */
    private T data;

    public ResponseObj() {
    }

    public ResponseObj setCode(String code) {
        this.code = code;
        return this;
    }

    public ResponseObj setSuccess(boolean success) {
        this.success = success;
        return this;
    }

    public ResponseObj setMessage(String message) {
        this.message = message;
        return this;
    }

    public ResponseObj setCause(String cause) {
        this.cause = cause;
        return this;
    }

    public ResponseObj setData(T data) {
        this.data = data;
        return this;
    }

    public ResponseObj(boolean success, String code, String message, T data) {
        this.success = success;
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResponseObj(boolean success) {
        this.success = success;
    }

    public boolean isSuccess() {
        return success;
    }

    /**
     * 查询或一般处理的成功返回方法,传递的参数为希望返回的提示信息
     *
     * @param message
     * @return
     */
    public static ResponseObj getSuccessRespObj(String message) {
        return (new ResponseObj()).setSuccess(true).setMessage(message);
    }

    /**
     * 查询或一般处理的成功返回方法,传递的参数为希望返回的提示信息
     *
     * @param data
     * @return
     */
    public static ResponseObj getSuccessRespObj(Object data) {
        return (new ResponseObj()).setSuccess(true).setData(data).setMessage("成功");
    }

    /**
     * 查询或一般处理的成功返回方法,传递的参数为希望返回的提示信息
     *
     * @param data
     * @return
     */
    public static ResponseObj getSuccessRespObj2(String code, Object data) {
        return (new ResponseObj()).setSuccess(true).setCode(code).setData(data).setMessage("成功");
    }

    /**
     * 查询或一般处理的成功返回方法,传递的参数提示信息和返回数据
     *
     * @param message
     * @return
     */
    public static ResponseObj getSuccessRespObj(String message, Object data) {
        return (new ResponseObj(true, null, message, data));
    }

    /**
     * 传入错误信息和错误编码
     *
     * @param code
     * @param message
     * @return
     */
    public static ResponseObj getBusinessErrorRespObj(String code, String message) {
        return (new ResponseObj()).setCode(code).setMessage(message);
    }

    /**
     * 传入错误信息和错误编码和后台报错信息
     *
     * @param code
     * @param message
     * @param cause
     * @return
     */
    public static ResponseObj getBusinessErrorRespObj(String code, String message, String cause) {
        return (new ResponseObj()).setCode(code).setMessage(message).setCause(cause);
    }

    /**
     * 只传入错误信息 （包括错误编号和内容）
     * 使用ErrorCode传参
     *
     * @param message
     * @return
     */
    public static ResponseObj getBusinessErrorRespObj(IErrorCodeType message) {
        return (new ResponseObj()).setCode(String.valueOf(message.getCode())).setMessage(message.getMessage());
    }

    /**
     * 传入系统id、错误信息参数（包括错误编号和内容）
     *
     * @param appId--系统id，可以根据系统id 区分错误信息的来源
     * @param errorCodeType        异常信息
     * @return
     */
    public static ResponseObj getBusinessErrorRespObj(String appId, IErrorCodeType errorCodeType) {
        return (new ResponseObj()).setCode(formatCode(appId, ErrorCode.BUSINESS_ERROR, errorCodeType.getCode())).setMessage(errorCodeType.getMessage());
    }


    /**
     * 传入系统id、错误信息、异常信息参数（e）
     *
     * @param appId--系统id
     * @param errorCodeType 异常信息
     * @param cause         异常
     * @return
     */
    public static ResponseObj getBusinessErrorRespObj(String appId, IErrorCodeType errorCodeType, String cause) {
        return (new ResponseObj()).setCode(formatCode(appId, ErrorCode.BUSINESS_ERROR, errorCodeType.getCode())).setMessage(errorCodeType.getMessage()).setCause(cause);
    }

    /**
     * 样式拼接
     *
     * @param appId
     * @param errType
     * @param code
     * @return
     */
    public static String formatCode(String appId, String errType, int code) {
        return String.format(CODE_FORMAT, appId, errType, code);
    }
}
