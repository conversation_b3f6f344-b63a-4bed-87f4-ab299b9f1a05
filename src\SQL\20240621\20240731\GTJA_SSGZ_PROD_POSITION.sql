-- Create table
create table GTJA_SSGZ_PROD_POSITION
(
    msg_id                 VARCHAR2(50),
    busi_date              NUMBER not null,
    account_set_no         VARCHAR2(32) not null,
    position_code          VARCHAR2(128) not null,
    stk_acct               VARCHAR2(32),
    trustee_seat           VARCHAR2(16),
    market_code            CHAR(3) not null,
    scr_code               <PERSON><PERSON><PERSON>R2(64) not null,
    scr_id                 VARCHAR2(64),
    contract_code          VARCHAR2(32),
    remain_mark            CHAR(2),
    position_direction     CHAR(2),
    position_type          CHAR(2),
    fnc_tool_class         CHAR(2),
    listed_circulate_situa CHAR(2),
    apply_way              CHAR(2),
    ccy_code               CHAR(3),
    exch_rate              NUMBER(16,8),
    fair_price_type        CHAR(2),
    valu_fair_price        NUMBER(18,12),
    s10101                 NUMBER(18,4),
    s10311                 NUMBER(18,4),
    s11001                 NUMBER(18,4),
    s11003                 NUMBER(18,4),
    s11020                 NUMBER(18,4),
    s11021                 NUMBER(18,4),
    s11031                 NUMBER(18,4),
    s11032                 NUMBER(18,4),
    s11033                 NUMBER(18,4),
    s12030                 NUMBER(18,4),
    s12040                 NUMBER(18,4),
    s22310                 NUMBER(18,4),
    s60110                 NUMBER(18,4),
    s60610                 NUMBER(18,4),
    s61010                 NUMBER(18,4),
    s61111                 NUMBER(18,4),
    s61112                 NUMBER(18,4),
    s61113                 NUMBER(18,4),
    s61114                 NUMBER(18,4),
    s64070                 NUMBER(18,4),
    s64110                 NUMBER(18,4),
    s67010                 NUMBER(18,4),
    s70010                 NUMBER(18,4),
    s70011                 NUMBER(18,4),
    s70012                 NUMBER(18,4),
    s70013                 NUMBER(18,4),
    s70014                 NUMBER(18,4),
    s70020                 NUMBER(18,4),
    s70030                 NUMBER(18,4),
    s70040                 NUMBER(18,4),
    s70050                 NUMBER(18,4),
    s70060                 NUMBER(18,4),
    s70110                 NUMBER(18,4),
    s90201                 NUMBER(18,4),
    s90202                 NUMBER(18,4),
    s90203                 NUMBER(18,4),
    s90204                 NUMBER(18,4),
    s90901                 NUMBER(18,4),
    s90902                 NUMBER(18,4),
    s90903                 NUMBER(18,4),
    s90904                 NUMBER(18,4),
    s90911                 NUMBER(18,4),
    s90912                 NUMBER(18,4),
    s90913                 NUMBER(18,4),
    s90914                 NUMBER(18,4),
    s90921                 NUMBER(18,4),
    s90922                 NUMBER(18,4),
    s90931                 NUMBER(18,4),
    s90932                 NUMBER(18,4),
    s90933                 NUMBER(18,4),
    s90934                 NUMBER(18,4),
    s90935                 NUMBER(18,4),
    s90936                 NUMBER(18,4),
    s91001                 NUMBER(22,6),
    s91002                 NUMBER(22,6),
    s91003                 NUMBER(22,6),
    s91004                 NUMBER(22,6),
    s91005                 NUMBER(22,6),
    due_date               NUMBER,
    create_date            NUMBER,
    cpdm          varchar2(20),
    load_time    VARCHAR2(20)  default to_char(sysdate,'yyyymmdd.hh24miss') not null,
    source_time VARCHAR2(20)
);
-- Add comments to the table
comment on table GTJA_SSGZ_PROD_POSITION
  is 'FV_产品持仓表（实时估值）';
-- Add comments to the columns
comment on column GTJA_SSGZ_PROD_POSITION.msg_id
  is '消息ID';
comment on column GTJA_SSGZ_PROD_POSITION.busi_date
  is '业务日期';
comment on column GTJA_SSGZ_PROD_POSITION.account_set_no
  is '账套号';
comment on column GTJA_SSGZ_PROD_POSITION.position_code
  is '持仓编码';
comment on column GTJA_SSGZ_PROD_POSITION.stk_acct
  is '证券账号';
comment on column GTJA_SSGZ_PROD_POSITION.trustee_seat
  is '托管席位';
comment on column GTJA_SSGZ_PROD_POSITION.market_code
  is '交易市场 字典：600001，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.scr_code
  is '证券代码';
comment on column GTJA_SSGZ_PROD_POSITION.scr_id
  is '证券内码';
comment on column GTJA_SSGZ_PROD_POSITION.contract_code
  is '合约编号';
comment on column GTJA_SSGZ_PROD_POSITION.remain_mark
  is '存量标识 字典：615025，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.position_direction
  is '持仓方向 字典：615005，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.position_type
  is '持仓类型 字典：615010，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.fnc_tool_class
  is '金融工具分类 字典：615020，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.listed_circulate_situa
  is '流通性质 字典：615015，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.apply_way
  is '股份来源 字典：600080，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.ccy_code
  is '币种代码 字典：600050，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.exch_rate
  is '汇率';
comment on column GTJA_SSGZ_PROD_POSITION.fair_price_type
  is '公允价类型 字典：615230，见字典说明    ';
comment on column GTJA_SSGZ_PROD_POSITION.valu_fair_price
  is '估值公允价';
comment on column GTJA_SSGZ_PROD_POSITION.s10101
  is '科目S_持仓数量';
comment on column GTJA_SSGZ_PROD_POSITION.s10311
  is '科目S_持仓保证金';
comment on column GTJA_SSGZ_PROD_POSITION.s11001
  is '科目S_成本';
comment on column GTJA_SSGZ_PROD_POSITION.s11003
  is '科目S_溢折价';
comment on column GTJA_SSGZ_PROD_POSITION.s11020
  is '科目S_估值增值';
comment on column GTJA_SSGZ_PROD_POSITION.s11021
  is '科目S_估值增值_其他';
comment on column GTJA_SSGZ_PROD_POSITION.s11031
  is '科目S_减值准备一阶段';
comment on column GTJA_SSGZ_PROD_POSITION.s11032
  is '科目S_减值准备二阶段';
comment on column GTJA_SSGZ_PROD_POSITION.s11033
  is '科目S_减值准备三阶段';
comment on column GTJA_SSGZ_PROD_POSITION.s12030
  is '科目S_应收股利';
comment on column GTJA_SSGZ_PROD_POSITION.s12040
  is '科目S_应收利息';
comment on column GTJA_SSGZ_PROD_POSITION.s22310
  is '科目S_应付利息';
comment on column GTJA_SSGZ_PROD_POSITION.s60110
  is '科目S_当日利息收入';
comment on column GTJA_SSGZ_PROD_POSITION.s60610
  is '科目S_当日汇兑损益';
comment on column GTJA_SSGZ_PROD_POSITION.s61010
  is '科目S_当日公允价值变动损益';
comment on column GTJA_SSGZ_PROD_POSITION.s61111
  is '科目S_当日实现盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s61112
  is '科目S_当日红利收入';
comment on column GTJA_SSGZ_PROD_POSITION.s61113
  is '科目S_平仓盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s61114
  is '科目S_持仓盯市盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s64070
  is '科目S_交易费用';
comment on column GTJA_SSGZ_PROD_POSITION.s64110
  is '科目S_当日利息支出';
comment on column GTJA_SSGZ_PROD_POSITION.s67010
  is '科目S_当日资产减值损失';
comment on column GTJA_SSGZ_PROD_POSITION.s70010
  is '科目S_累计实现盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s70011
  is '科目S_累计利息收入';
comment on column GTJA_SSGZ_PROD_POSITION.s70012
  is '科目S_累计红利收入';
comment on column GTJA_SSGZ_PROD_POSITION.s70013
  is '科目S_累计平仓盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s70014
  is '科目S_累计持仓盯市盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s70020
  is '科目S_累计配股数量';
comment on column GTJA_SSGZ_PROD_POSITION.s70030
  is '科目S_累计利息支出';
comment on column GTJA_SSGZ_PROD_POSITION.s70040
  is '科目S_累计汇兑损益';
comment on column GTJA_SSGZ_PROD_POSITION.s70050
  is '科目S_累计资产减值损失';
comment on column GTJA_SSGZ_PROD_POSITION.s70060
  is '科目S_累计交易费用';
comment on column GTJA_SSGZ_PROD_POSITION.s70110
  is '科目S_市值';
comment on column GTJA_SSGZ_PROD_POSITION.s90201
  is '科目S_当日买入数量';
comment on column GTJA_SSGZ_PROD_POSITION.s90202
  is '科目S_当日买入金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90203
  is '科目S_当日卖出数量';
comment on column GTJA_SSGZ_PROD_POSITION.s90204
  is '科目S_当日卖出金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90901
  is '科目S_T1买入待交收数量';
comment on column GTJA_SSGZ_PROD_POSITION.s90902
  is '科目S_T1买入待交收成本';
comment on column GTJA_SSGZ_PROD_POSITION.s90903
  is '科目S_T1买入待交收溢折价';
comment on column GTJA_SSGZ_PROD_POSITION.s90904
  is '科目S_T1买入待交收利息';
comment on column GTJA_SSGZ_PROD_POSITION.s90911
  is '科目S_T1卖出待交收数量';
comment on column GTJA_SSGZ_PROD_POSITION.s90912
  is '科目S_T1卖出待交收成本';
comment on column GTJA_SSGZ_PROD_POSITION.s90913
  is '科目S_T1卖出待交收溢折价';
comment on column GTJA_SSGZ_PROD_POSITION.s90914
  is '科目S_T1卖出待交收利息';
comment on column GTJA_SSGZ_PROD_POSITION.s90921
  is '科目S_T0买入数量';
comment on column GTJA_SSGZ_PROD_POSITION.s90922
  is '科目S_T0买入金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90931
  is '科目S_T0买入成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90932
  is '科目S_T0卖出成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90933
  is '科目S_T1买入成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90934
  is '科目S_T1卖出成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90935
  is '科目S_T2买入成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s90936
  is '科目S_T2卖出成交金额';
comment on column GTJA_SSGZ_PROD_POSITION.s91001
  is '科目S_交易成本';
comment on column GTJA_SSGZ_PROD_POSITION.s91002
  is '科目S_交易当日浮动盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s91003
  is '科目S_交易浮动盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s91004
  is '科目S_交易当日实现盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.s91005
  is '科目S_交易累计实现盈亏';
comment on column GTJA_SSGZ_PROD_POSITION.due_date
  is '到期日期';
comment on column GTJA_SSGZ_PROD_POSITION.create_date
  is '开始日期';
comment on column GTJA_SSGZ_PROD_POSITION.cpdm
  is '产品代码';
comment on column GTJA_SSGZ_PROD_POSITION.load_time
  is '数据落地时间';
comment on column GTJA_SSGZ_PROD_POSITION.source_time
  is '数据计算时间';



