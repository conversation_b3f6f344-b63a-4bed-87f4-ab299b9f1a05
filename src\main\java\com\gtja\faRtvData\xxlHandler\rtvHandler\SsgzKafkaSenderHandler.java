package com.gtja.faRtvData.xxlHandler.rtvHandler;

import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.mq.MessageSender;
import com.gtja.faRtvData.service.sftp.FtpSsgzFileDealService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tools.DateTimeTools;
import tools.RegularTools;
/**
 * @FileName:  SsgzKafkaSenderHandler.java
 * @anthor: Magic
 * @create: 2025/2/12- 17:59
 * @TODO:   //TODO end
 * @Version：1.0
 */
@Slf4j
@Component
public class SsgzKafkaSenderHandler {

    @Autowired
    private MessageSender messageSender;

    /**
     * 实时估值-sjzx-sender
     *
     * @param param
     * @return
     */
    @XxlJob("ZqtzKafkaSenderHandler")
    public ReturnT<String> ZqtzKafkaSenderHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值kafka 消息发送 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        String ywlx = RegularTools.getParamFromCmd(param, "ywlx", Constants.RJZJBD_YWLX.ZQTZ);
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());

        try {
            String message = messageSender.sendMessage(ywlx, ywrq);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("实时估值kafka 消息发送失败：" + e.toString());
            log.info("实时估值kafka 消息发送失败:{}", e.toString());
        } finally {
            return result;
        }
    }

    @XxlJob("TaKafkaSenderHandler")
    public ReturnT<String> TaKafkaSenderHandler(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值kafka 消息发送 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        String ywlx = RegularTools.getParamFromCmd(param, "ywlx", Constants.RJZJBD_YWLX.TA);
        String ywrq = RegularTools.getParamFromCmd(param, "ywrq", DateTimeTools.getDateNumNow());

        try {
            String message = messageSender.sendMessage(ywlx, ywrq);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("实时估值kafka 消息发送失败：" + e.toString());
            log.info("实时估值kafka 消息发送失败:{}", e.toString());
        } finally {
            return result;
        }
    }
}
