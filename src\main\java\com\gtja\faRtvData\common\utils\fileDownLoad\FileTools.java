package com.gtja.faRtvData.common.utils.fileDownLoad;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.LinkedList;
import java.util.List;


@Slf4j
@Component
public class FileTools {

    /**
     * 通过 文件路径下载文件
     *
     * @param response
     * @param filePath
     * @param fileName
     * @return
     * @throws Exception
     */
    public Boolean downloadByPath(HttpServletResponse response, String filePath, String fileName) throws Exception {
        log.info("进入下载邮件附件方法");
        if (StringUtils.isBlank(fileName) || StringUtils.isBlank(filePath)) {
            return false;
        }
        String path = filePath + File.separator + fileName;
        //读取文件
        File file = new File(path);
        if (!file.exists()) {
            return false;
        }
        BufferedInputStream bis = null;
        OutputStream os = null;
        response.reset();
        response.setCharacterEncoding("UTF-8");
        response.setContentType("multipart/form-data");
        fileName = new String(fileName.getBytes(), "ISO-8859-1");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        //文件输出
        try {
            bis = new BufferedInputStream(new FileInputStream(file));
            byte[] b = new byte[bis.available() + 1024];
            int i = 0;
            os = response.getOutputStream();
            while ((i = bis.read(b)) != -1) {
                os.write(b, 0, i);
            }
            os.flush();
            os.close();
        } catch (IOException e) {
            log.info("下载邮件附件失败");
            e.printStackTrace();
            return false;
        } finally {
            if (os != null)
                os.close();
        }
        return true;
    }


    /**
     * 递归获取所有文件
     *
     * @param path
     */
    public static List<File> getAllFile(String path) {
        return getAllFile(path, true);
    }

    public static List<File> getAllFile(String path, boolean recursion) {
        List<File> listFile = new LinkedList<>();
        File filePath = new File(path);
        if (filePath.isDirectory() && filePath.exists()) {
            File[] files = filePath.listFiles();
            if (null != files) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        //System.out.println("文件夹:" + file2.getAbsolutePath());
                        if (recursion) {
                            listFile.addAll(getAllFile(file.getAbsolutePath()));
                        }
                    } else {
                        //System.out.println("文件:" + file2.getAbsolutePath());
                        listFile.add(file);
                    }
                }
            }
        } else if (filePath.isFile() && filePath.exists()) {
            listFile.add(filePath);
        } else {
//            log.info("文件夹或文件不存在：" + filePath.getPath());
        }
        return listFile;
    }
}
