package com.gtja.faRtvData.dao.fbzx;

import com.gtja.faRtvData.model.RtvCommonPdfModel;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ProjectName: RealTimeValueDao.java
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2022/9/2211:32
 * @Version :1.0
 */
@Repository
public interface RealTimeValueDao {
    List<String> queryRealTimeValueCpConfig1();

    List<RtvCommonPdfModel> queryRealTimeValueCpConfig(String ywrq);

    RtvCommonPdfModel queryRealTimeValueCpqd(@Param("ywrq") String ywrq, @Param("cpdm") String cpdm);

    List<RtvCommonPdfModel> queryRtvFileOk(@Param("infos") List<RtvCommonPdfModel> infos);

    List<RtvCommonPdfModel> queryRtvFileCommonOk(String ywrq);

    List<RtvCommonPdfModel> queryRtvFileConfig(String type);

    List<T> queryRtvTfundinfoList(String cpdm);

    List<T> queryRtvTjjxsflList(String cpdm);

    List<T> queryRtvThkzhList(String cpdm);

    List<T> queryRtvTgdxwList(String cpdm);

    List<T> queryRtvTyttxList(String cpdm);

    List<T> queryRtvTaccountList(String cpdm);

    List<T> queryRtvTgpsdList(String cpdm);

    List<T> queryRtvBalanceList(@Param("cpdm") String cpdm, @Param("ywrq") String ywrq);

    List<T> queryRtvTxtcsList(String cpdm);

    List<T> queryRtvGtjaAccountList(String cpdm);

    List<T> queryRtvTllList(String cpdm);

    //--------------公共级别-------------------------------
    List<T> queryRtvTqsList();

    List<T> queryRtvTxtcsCommonList();


    void updateRtvCpqdGzrq();

    List<RtvCommonPdfModel> quertNotCreateGGFile(String ywrq);
}
