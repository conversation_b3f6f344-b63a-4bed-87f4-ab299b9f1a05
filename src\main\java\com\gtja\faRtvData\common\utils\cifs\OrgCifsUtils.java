//package com.gtja.faRtvData.common.utils.cifs;
//
//
//import com.gtja.faRtvData.model.NfsInfoModel;
//import jcifs.CIFSContext;
//import jcifs.config.PropertyConfiguration;
//import jcifs.context.BaseContext;
//import jcifs.smb.SmbException;
//import jcifs.smb.SmbFile;
//import jcifs.smb.SmbFileOutputStream;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.io.BufferedInputStream;
//import java.io.File;
//import java.io.FileInputStream;
//import java.io.IOException;
//import java.net.UnknownHostException;
//import java.util.Properties;
//
///**
// *  CIFS 依赖
// *   <groupId>org.codelibs</groupId>
// *             <artifactId>jcifs</artifactId>
// *             <version>2.1.31</version>
// */
//@Slf4j
//@Component
//public class OrgCifsUtils {
//
//
//    /**
//     * @param localAbsoluteFile 本地文件的绝对路径
//     * @param ftpPath           FTP服务器的绝对路径目录（只能是单层目录： /20240701/）
//     * @throws IOException
//     * @demoName: UploadCifsFile
//     * @Anthor: Magic
//     * @Create: 2025/2/13- 11:26
//     * @TODO: //TODO end
//     */
//    public static void UploadCifsFile(NfsInfoModel sftpInfoModel, String localAbsoluteFile, String ftpPath) throws Exception {
//        //创建一个远程文件夹
//        String smbUrl = "smb://" + sftpInfoModel.getIp() + "/" + sftpInfoModel.getShare() + sftpInfoModel.getPath() + ftpPath;
//
//        log.info("connect cifs start:ip:{};path:{}", sftpInfoModel.getIp(), sftpInfoModel.getPath());
////        byte[] context = new byte[0];
////        context = Files.readAllBytes(Paths.get(localAbsoluteFile));
//
//        Properties ps = new Properties();
//        ps.setProperty("jcifs.smb.client.domain", sftpInfoModel.ip);
//        ps.setProperty("jcifs.smb.client.username", sftpInfoModel.userName);
//        ps.setProperty("jcifs.smb.client.password", sftpInfoModel.passWord);
//        ps.setProperty("jcifs.smb.client.dfs.disabled", "true");
//        CIFSContext cifs = new BaseContext(new PropertyConfiguration(ps));
//        try {
//            SmbFile smbFolder = new SmbFile(smbUrl, cifs);
//            if (!smbFolder.exists()) {
//                smbFolder.mkdir();
//            }
//        } catch (SmbException e) {
//            throw new RuntimeException(e);
//        }
//
//        //向远程文件夹上传文件
//        File file = new File(localAbsoluteFile);
//        SmbFile smbFile = new SmbFile(smbUrl + "/" + file.getName(), cifs);
//
//        SmbFileOutputStream smbOut = null;
//        BufferedInputStream bf = null;
//        try {
//            smbOut = new SmbFileOutputStream(smbFile);
//            bf = new BufferedInputStream(new FileInputStream(file));
//            byte[] bt = new byte[8192];
//            int n = bf.read(bt);
//            while (n != -1) {
//                smbOut.write(bt, 0, n);
//                smbOut.flush();
//                n = bf.read(bt);
//            }
//        } catch (UnknownHostException ue) {
//            log.error("文件上传失败，失败原因:{}", ue);
//            throw new Exception("文件上传失败" + ue.getMessage());
//        } catch (Exception e) {
//            log.error("文件上传失败，失败原因:{}", e);
//            throw new Exception("文件上传失败");
//        } finally {
//            if (smbOut != null) {
//                smbOut.close();
//            }
//            if (bf != null) {
//                bf.close();
//            }
//        }
//    }
//
//}
