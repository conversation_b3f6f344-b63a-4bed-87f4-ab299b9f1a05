package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

@Data
public class RtvTjjxsflDbfModel {
    @DbfAnnotation(fieldName = "VC_FJJJDM", fieldLength = 15)
    private String vc_fjjjdm;
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 6)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_FJJJMC", fieldLength = 100)
    private String vc_fjjjmc;
    @DbfAnnotation(fieldName = "VC_FJJB", fieldLength = 1)
    private String vc_fjjb;
    @DbfAnnotation(fieldName = "D_KSRQ", fieldLength = 15)
    private String d_ksrq;
    @DbfAnnotation(fieldName = "D_JSRQ", fieldLength = 15)
    private String d_jsrq;
    @DbfAnnotation(fieldName = "VC_JSBZ", fieldLength = 3)
    private String vc_jsbz;
    @DbfAnnotation(fieldName = "D_TQJSRQ", fieldLength = 15)
    private String d_tqjsrq;
    @DbfAnnotation(fieldName = "EN_YQSY", fieldLength = 25)
    private String en_yqsy;
    @DbfAnnotation(fieldName = "L_YXBZ", fieldLength = 1)
    private String l_yxbz;

}
