package com.gtja.faRtvData.common.utils.sftp;


import com.github.pagehelper.util.StringUtil;
import com.gtja.faRtvData.common.ienum.SftpConstants;
import com.gtja.faRtvData.common.utils.fileDownLoad.FileTools;
import com.gtja.faRtvData.model.SftpInfoModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * FTP服务器文件上传下载工具类
 */
@Slf4j
@Component
public class FtpUtils {

    /**
     * FTP服务器连接工具方法
     *
     * @param host     FTP服务器IP
     * @param port     FTP服务器端口
     * @param userName FTP服务器用户名称
     * @param password FTP服务器用户密码
     */
    public static FTPClient connectFTP(String host, Integer port, String userName, String password) throws IOException {

        FTPClient client = new FTPClient();
        //连接服务器
        client.connect(host, port);
        //登录服务器
        client.login(userName, password);
        //设置连接超时时间
        client.setConnectTimeout(SftpConstants.SESSION_CONNECT_TIMEOUT);
        //设置编码格式
        client.setControlEncoding(SftpConstants.ENCODING);
        // 设置文件传输编码类型， 字节传输：BINARY_FILE_TYPE, 文本传输：ASCII_FILE_TYPE， 建议使用BINARY_FILE_TYPE进行文件传输
        client.setFileType(FTP.BINARY_FILE_TYPE);
        // 设置为被动模式: enterLocalPassiveMode()
        client.enterLocalPassiveMode();
        return client;
    }

    /**
     * 从FTP服务上下载文件到本地目录
     *
     * @param host            FTP服务器IP
     * @param port            FTP服务器端口
     * @param userName        FTP服务器用户名称
     * @param password        FTP服务器用户密码
     * @param ftpAbsoluteFile ftp服务器的文件绝对路径
     * @param localPath       本地目录
     * @throws IOException
     */
    public static void downloadFTPFile(String host, Integer port, String userName, String password, String ftpAbsoluteFile, String localPath) throws IOException {

        FTPClient client = null;
        FileOutputStream fileOutputStream = null;
        try {
            client = connectFTP(host, port, userName, password);
            File file = new File(localPath);
            if (!file.exists()) {
                file.mkdirs();
            }
            //获取远程服务器上的文件名称和目录
            String[] split = ftpAbsoluteFile.split("/");
            String localFileName = split[split.length - 1];

            String ftpAbsolutePath = ftpAbsoluteFile.substring(0, ftpAbsoluteFile.length() - localFileName.length());
            //进入目录
            client.changeWorkingDirectory(ftpAbsolutePath);
            fileOutputStream = new FileOutputStream(file.getAbsolutePath() + File.separator + localFileName);
            boolean pullStatus = client.retrieveFile(localFileName, fileOutputStream);
            if (pullStatus) {
                log.info("文件下载成功!");
            } else {
                log.info("文件下载失败!");
            }
        } catch (Exception e) {
            log.info("文件下载失败,原因原因:{}", e);
            e.printStackTrace();
        } finally {
            if (fileOutputStream != null) {
                fileOutputStream.close();
            }
            if (client != null) {
                client.logout();
                client.disconnect();
            }
        }
    }

    /**
     * 上传本地文件到FTP文件目录
     * FTP服务器连接工具方法
     *
     * @param host              FTP服务器IP
     * @param port              FTP服务器端口
     * @param userName          FTP服务器用户名称
     * @param password          FTP服务器用户密码
     * @param localAbsoluteFile 本地文件的绝对路径
     * @param ftpPath           FTP服务器的绝对路径目录
     * @throws IOException
     */
    public static void UploadFTPFile(String host, Integer port, String userName, String password, String localAbsoluteFile, String ftpPath) throws IOException {

        //连接服务器
        FTPClient client = null;
        FileInputStream fileInputStream = null;
        try {
            client = connectFTP(host, port, userName, password);
            //进入目录
            boolean existStatus = client.changeWorkingDirectory(ftpPath);
            if (!existStatus) {
                client.makeDirectory(ftpPath);// createDir(client,ftpPath);
            }
            File file = new File(localAbsoluteFile);
            fileInputStream = new FileInputStream(file);
            //设置编码格式，防止出现乱码
            String tempPath = ftpPath + file.getName();
            String ftpFileName = new String(tempPath.getBytes(StandardCharsets.UTF_8), "ISO-8859-1");
            boolean pushStatus = client.storeFile(ftpFileName, fileInputStream);
            if (pushStatus) {
                log.info("文件上传成功!");
            } else {
                log.info("文件上传失败!");
            }
        } catch (Exception e) {
            log.error("文件上传失败，失败原因:{}", e);
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
            if (client != null) {
                client.logout();
                client.disconnect();
            }
        }
    }

    /**
     * 上传本地文件到FTP文件目录
     * FTP服务器连接工具方法
     *
     * @param localAbsoluteFile 本地文件的绝对路径
     * @param ftpPath           FTP服务器的绝对路径目录（只能是单层目录： /20240701/）
     * @throws IOException
     */
    public static void UploadFTPFile(SftpInfoModel sftpInfoModel, String localAbsoluteFile, String ftpPath) throws IOException {
        //连接服务器
        FTPClient client = null;
        FileInputStream fileInputStream = null;
        try {
            log.info("connect sftp start:ip:{};port:{}", sftpInfoModel.getIp(), sftpInfoModel.getPort());
            client = connectFTP(sftpInfoModel.getIp(), Integer.parseInt(sftpInfoModel.getPort()), sftpInfoModel.getUserName(), sftpInfoModel.getPassWord());
            log.info("connect sftp success, Change path to {}", ftpPath);
            //进入目录
            boolean existStatus = client.changeWorkingDirectory(ftpPath);
            if (!existStatus) {
                // client.makeDirectory(ftpPath);
                createDir(client, ftpPath);
            }
            File file = new File(localAbsoluteFile);
            fileInputStream = new FileInputStream(file);
            //设置编码格式，防止出现乱码
            String tempPath = ftpPath + file.getName();
            String ftpFileName = new String(tempPath.getBytes(StandardCharsets.UTF_8), "ISO-8859-1");
            boolean pushStatus = client.storeFile(ftpFileName, fileInputStream);
            if (pushStatus) {
                log.info("文件上传成功!");
            } else {
                log.info("文件上传失败!");
            }
        } catch (Exception e) {
            log.error("文件上传失败，失败原因:{}", e);
        } finally {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
            if (client != null) {
                client.logout();
                client.disconnect();
            }
        }
    }

    /**
     * ftp创建多层目录 并进行切换
     *
     * @param ftpClient
     * @param ftpPath
     * @return
     * @throws IOException
     */
    public static boolean createDir(FTPClient ftpClient, String ftpPath) throws IOException {
        String[] dirs = ftpPath.split("/");
        StringBuilder currentDir = new StringBuilder("/");
        for (String dir : dirs) {
            if (!dir.isEmpty()) {
                currentDir.append(dir).append("/");
                if (!ftpClient.changeWorkingDirectory(currentDir.toString())) {
                    if (ftpClient.makeDirectory(currentDir.toString())) {
                        return false;//创建目录失败
                    }
                    if (!ftpClient.changeWorkingDirectory(currentDir.toString())) {
                        return false;//切换目录失败
                    }
                }
            }
        }
        return true;
    }

    /**
     * 上传本地文件到FTP文件目录
     * FTP服务器连接工具方法
     *
     * @param sftpInfoModel 包含：
     *                      host              FTP服务器IP
     *                      port              FTP服务器端口
     *                      userName          FTP服务器用户名称
     *                      password          FTP服务器用户密码
     * @param localAbsolute 本地文件路径   路径下的所有文件都会被上传
     * @param ftpPath       FTP服务器的 外层 目标目录
     * @throws IOException
     */
    public static void UploadFTPFiles(SftpInfoModel sftpInfoModel, String localAbsolute, String ftpPath) throws IOException {
        //获取本地文件目录
        List<File> files = FileTools.getAllFile(localAbsolute);
        //循环处理
        for (File f : files) {
            if (f.isFile()) {
                String localAbsoluteFile = f.getAbsolutePath();
                String ftpLastPath = ftpPath;
                String ftpSubPath = f.getAbsolutePath().replace(localAbsoluteFile, "").replace(f.getName(), "");
                if (StringUtil.isNotEmpty(ftpSubPath)) {
                    ftpLastPath = ftpPath + ftpSubPath;
                }
                UploadFTPFile(sftpInfoModel, localAbsoluteFile, ftpLastPath);
            }
        }
    }

    /**
     * FTP文件下载
     *
     * @param remotePath    FTP服务器上的相对路径
     * @param localPath     下载后保存到本地的路径
     * @param sftpInfoModel 文件发送配置
     * @return
     */
    public boolean downFileFtp(SftpInfoModel sftpInfoModel, String localPath, String remotePath, String fileName) throws Exception {
        boolean result = false;
        OutputStream os = null;
        FTPClient ftp = new FTPClient();
        try {
            ftp.connect(sftpInfoModel.getHost(), Integer.parseInt(sftpInfoModel.getPort()));
            log.info("ftp connect success.");

            ftp.login(sftpInfoModel.getUserName(), sftpInfoModel.getPassWord());
            log.info("ftp login success.");
            if (!FTPReply.isPositiveCompletion(ftp.getReplyCode())) {
                ftp.disconnect();
                log.info("ftp connect failed, ftp is unpositive complection");
                return result;
            }
            //切换FTP服务器目录
            if (ftp.changeWorkingDirectory(remotePath)) {
                log.info("change directory success, remotePath = {}.", remotePath);

                FTPFile[] files = ftp.listFiles();
                if (files != null && files.length > 0) {
                    for (FTPFile file : files) {
                        File localFilePath = new File(localPath);
                        if (!localFilePath.exists()) {
                            localFilePath.mkdirs();
                        }
                        log.info("ftp download file, remote path = {}, local path = {}, file name = {}.", remotePath, localPath, fileName);
                        File localFile = new File(localPath + File.separator + fileName);
                        os = new FileOutputStream(localFile);
                        ftp.setFileType(FTP.BINARY_FILE_TYPE);
                        result = ftp.retrieveFile(file.getName(), os);
                        if (!result) {
                            log.info("下载文件失败");
                        } else {
                            log.info("下载文件成功");
                        }
                        os.close();
                    }
                }
            }
            ftp.logout();
            result = true;
        } catch (Exception e) {
            log.info("## Exception ftp download file failed, error ##", e);
        } finally {
            try {
                if (ftp.isConnected()) {
                    ftp.disconnect();
                }
                if (null != os) {
                    os.close();
                }
            } catch (IOException e) {
                log.info("## Exception ftp download file failed, error ##", e);
            }
        }
        return result;
    }
}
