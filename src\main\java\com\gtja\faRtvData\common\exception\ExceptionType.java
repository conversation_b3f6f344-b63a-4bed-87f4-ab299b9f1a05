package com.gtja.faRtvData.common.exception;

/**
 * @Method: ExceptionType
 * @Descripton :DESC 异常类型
 * <AUTHOR>
 * @Date :2020/12/914:36
 * @Return:
 * @Exception :
 */
public enum ExceptionType {

    CLIENT(4),//客户端
    SERVER(5),//服务
    BUSINNESS(6);//业务

    private final int value;

    private ExceptionType(int value) {
        this.value = value;
    }
    public int value() {
        return this.value;
    }

    public static ExceptionType valueOf(int type) {
        ExceptionType[] exceptionTypes = values();
        for(int i = 0; i < exceptionTypes.length; ++i) {
            ExceptionType exceptionType = exceptionTypes[i];
            if (exceptionType.value == type) {
                return exceptionType;
            }
        }
        throw new IllegalArgumentException("No matching constant for [" + type + "]");
    }
}
