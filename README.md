调度器:实时估值	地址:http://*************/xxl-job-admin/jobinfo?jobGroup=1121
用户/密码	zhaoxiang/zhaoxiang
    
实时估值代码地址	https://gitlab.gtja.net/zctg/data/fa-rtv-data.git
相关分支	master
    
服务器地址	************
用户/密码	administrator/tzzz$223
    
服务地址	E:\xxl\faRtvdata
swagger地址	http://localhost:8094/fa-rtv-data/swagger-ui.html#/
目前存在CPT验密接口提供给服务平台	
    
技术部kafka地址	
userName: zctgKafkaUser	
passWord: zctgKafkaUser@2025	
servers: ***********:9093,***********:9093,***********:9093,************:9093,************:9093,************:9093,************:9093	
    
技术部接口地址	
技术部支持实时估值状态	http://*************:6601/adapter/api/81050000
CPT验密	http://*************:6601/adapter/api/81050001
    
为技术部提供的实时估值文件生成地址	//************/frac_file/tgshare/YYYYMMDD/
    
    
管理端配置前台页面的后端代码目前放在市场管理下面	
代码地址	https://gitlab.gtja.net/zctg/fbzx/fbzx.git
相关分支	feature-scgl
接口地址	GtjaSsgzGlrConfigController
"后续可以迁移至实时估值，需配置NG需与前端一起修改上线，
CPT验密也需配置NG后上线，目前仿真NG配置对接的为，
fa-rtv-data"	"CPT接口地址CptValidateController
前端对接人为：服务平台-梁亚明"




项目	任务类型	估值维度	估值时点	估值类型	关联资产类型	关联资产类型明细	参数	版本
实时估值	估值	估值时点、估值类型	T-0、T-1	日估值、快照估值	货币资产、固定收益	活期存款、定期存款、同业存单	估值日期、产品代码	V1.0
实时估值	回溯	估值时点、估值类型	T-0、T-1	日估值、快照估值	货币资产、固定收益	活期存款、定期存款、同业存单	估值日期、产品代码	V1.0

id
任务类型
名称
说明
数据源
调度类型
cron表达式
状态
负责人
最近更新时间
创建人
创建时间
脚本
参数
1
估值
日估值
日估值任务
PostgreSQL
定时
0 0 1 * * ?
已启用
张三
2023-01-01 10:00:00
张三
2023-01-01 09:00:00
run_daily_valuation.sh
{"估值日期": "2023-01-01"}
2
估值
快照估值
快照估值任务
MySQL
定时
0 30 14 * * ?
已启用
李四
2023-01-02 11:30:00
李四
2023-01-02 10:00:00
run_snapshot_valuation.py
{"产品代码": "A123"}
3
回溯
回溯任务
回溯任务
Oracle
手动
-
已禁用
王五
2023-01-03 15:00:00
王五
2023-01-03 14:00:00
run_backtrace.sh
{"产品代码": "B456"}