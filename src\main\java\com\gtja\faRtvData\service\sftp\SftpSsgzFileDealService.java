package com.gtja.faRtvData.service.sftp;

import com.gtja.faRtvData.model.SftpInfoModel;

public interface SftpSsgzFileDealService {
    public String SftpSsgzFilePut(String sourcePath, String targetPath,String fileName, SftpInfoModel sftpSendConfig) throws Exception;

    boolean downloadFile(String sourcePath, String targetPath, String fileName, SftpInfoModel sftpInfoModel) throws Exception;

  //  String SftpSsgzFilePut(String rtvBasePath, String sftpPath, SftpInfoModel dbinfo);
}
