package com.gtja.faRtvData.common;

/**
 * 对外发送频率枚举值
 */
public class Frequency {

    //----------------------交易日模式----------------------

    /**
     * 每个交易日
     */
    public final static String DAY = "d";

    /**
     * 每周最后一个交易日
     */
    public final static String WEEK = "w";

    /**
     * 每月最后一个交易日
     */
    public final static String MONTH = "m";

    /**
     * 每季最后一个交易日
     */
    public final static String QUARTER = "q";

    /**
     * 每半年最后一个交易日
     */
    public final static String HALF_YEAR = "hy";

    /**
     * 每年最后一个交易日
     */
    public final static String YEAR = "y";

//    /**
//     * 每月最后自然日
//     * todo 旧版为z修改时需要兼容修改
//     */
//    public final static String MONTH_Z = "z";

    //----------------------自然日模式----------------------

    /**
     * 每个自然日
     */
    public final static String DAY_Z = "dz";

    /**
     * 每周最后一个自然日
     */
    public final static String WEEK_Z = "wz";

    /**
     * 每月最后一个自然日
     */
    public final static String MONTH_Z = "mz";

    /**
     * 每季最后一个自然日
     */
    public final static String QUARTER_Z = "qz";

    /**
     * 每半年最后一个自然日
     */
    public final static String HALF_YEAR_Z = "hyz";

    /**
     * 每年最后一个自然日
     */
    public final static String YEAR_Z = "yz";

    //----------------------固定日模式-交易日----------------------

    /**
     * 每周固定交易日
     */
    public final static String WEEK_G = "wg";

    /**
     * 每月固定交易日
     */
    public final static String MONTH_G = "mg";

    /**
     * 每季固定交易日
     */
    public final static String QUARTER_G = "qg";

    /**
     * 每半年固定交易日
     */
    public final static String HALF_YEAR_G = "hyg";

    /**
     * 每年固定交易日
     */
    public final static String YEAR_G = "yg";

    //----------------------固定日模式-自然日----------------------

    /**
     * 每周固定自然日
     */
    public final static String WEEK_G_Z = "wgz";

    /**
     * 每月固定自然日
     */
    public final static String MONTH_G_Z = "mgz";

    /**
     * 每季固定自然日
     */
    public final static String QUARTER_G_Z = "qgz";

    /**
     * 每半年固定自然日
     */
    public final static String HALF_YEAR_G_Z = "hygz";

    /**
     * 每年固定自然日
     */
    public final static String YEAR_G_Z = "ygz";

    /**
     * 日期顺序，正数第多少天
     * 默认为正序
     */
    public final static String ORDER_ASC = "1";

    /**
     * 日期顺序，倒数第多少天
     */
    public final static String ORDER_DESC = "2";


    /**
     * 非交易日处理模式 1-顺延下一交易日（若为下月第一个交易日，则不发送）
     * 默认为顺延
     */
    public final static String MODE_NEXT = "1";

    /**
     * 非交易日处理模式 2-提前上一交易日
     */
    public final static String MODE_LAST = "2";


    //----------------------固定日模式----------------------

    /**
     * 发送开放日净值
     */
    public final static String FWPT_AUTO_SEND_FREQUENCY_OPENDAY = "k";

    /**
     * 固定日
     */
    public final static String FWPT_AUTO_SEND_FREQUENCY_FIXEDDAY = "g";

    /**
     * 周合并
     */
    public final static String FWPT_AUTO_SEND_FREQUENCY_ZHB = "whb";

    /**
     * 重发
     */
    public final static String FWPT_AUTO_SEND_FREQUENCY_REISSUE = "R";

    public final static int SEND_FREQUENCY_DAY = 0;

    public final static int SEND_FREQUENCY_WEEK = 1;

    public final static int SEND_FREQUENCY_MONTH = 2;



}
