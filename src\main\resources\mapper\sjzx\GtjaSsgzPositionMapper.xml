<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.sjzx.RtvFracResultDao">
  <resultMap id="BasePositionResultMap" type="com.gtja.faRtvData.model.GtjaSsgzPositionModel">
    <result column="MSG_ID" jdbcType="VARCHAR" property="MSG_ID" />
      <result column="MSG_GROUP_ID" jdbcType="DECIMAL" property="MSG_GROUP_ID"/>
      <result column="MSG_GROUP_SNO" jdbcType="DECIMAL" property="MSG_GROUP_SNO"/>

    <result column="BUSI_DATE" jdbcType="DECIMAL" property="busi_date" />
    <result column="ACCOUNT_SET_NO" jdbcType="VARCHAR" property="account_set_no" />
    <result column="POSITION_CODE" jdbcType="VARCHAR" property="position_code" />
    <result column="STK_ACCT" jdbcType="VARCHAR" property="stk_acct" />
    <result column="TRUSTEE_SEAT" jdbcType="VARCHAR" property="trustee_seat" />
    <result column="MARKET_CODE" jdbcType="CHAR" property="market_code" />
    <result column="SCR_CODE" jdbcType="VARCHAR" property="scr_code" />
    <result column="SCR_ID" jdbcType="VARCHAR" property="scr_id" />
    <result column="CONTRACT_CODE" jdbcType="VARCHAR" property="contract_code" />
    <result column="REMAIN_MARK" jdbcType="CHAR" property="remain_mark" />
    <result column="POSITION_DIRECTION" jdbcType="CHAR" property="position_direction" />
    <result column="POSITION_TYPE" jdbcType="CHAR" property="position_type" />
    <result column="FNC_TOOL_CLASS" jdbcType="CHAR" property="fnc_tool_class" />
    <result column="LISTED_CIRCULATE_SITUA" jdbcType="CHAR" property="listed_circulate_situa" />
    <result column="APPLY_WAY" jdbcType="CHAR" property="apply_way" />
    <result column="CCY_CODE" jdbcType="CHAR" property="ccy_code" />
    <result column="EXCH_RATE" jdbcType="DECIMAL" property="exch_rate" />
    <result column="FAIR_PRICE_TYPE" jdbcType="CHAR" property="fair_price_type" />
    <result column="VALU_FAIR_PRICE" jdbcType="DECIMAL" property="valu_fair_price" />
    <result column="S10101" jdbcType="DECIMAL" property="s10101" />
    <result column="S10311" jdbcType="DECIMAL" property="s10311" />
    <result column="S11001" jdbcType="DECIMAL" property="s11001" />
    <result column="S11003" jdbcType="DECIMAL" property="s11003" />
    <result column="S11020" jdbcType="DECIMAL" property="s11020" />
    <result column="S11021" jdbcType="DECIMAL" property="s11021" />
    <result column="S11031" jdbcType="DECIMAL" property="s11031" />
    <result column="S11032" jdbcType="DECIMAL" property="s11032" />
    <result column="S11033" jdbcType="DECIMAL" property="s11033" />
    <result column="S12030" jdbcType="DECIMAL" property="s12030" />
    <result column="S12040" jdbcType="DECIMAL" property="s12040" />
    <result column="S22310" jdbcType="DECIMAL" property="s22310" />
    <result column="S60110" jdbcType="DECIMAL" property="s60110" />
    <result column="S60610" jdbcType="DECIMAL" property="s60610" />
    <result column="S61010" jdbcType="DECIMAL" property="s61010" />
    <result column="S61111" jdbcType="DECIMAL" property="s61111" />
    <result column="S61112" jdbcType="DECIMAL" property="s61112" />
    <result column="S61113" jdbcType="DECIMAL" property="s61113" />
    <result column="S61114" jdbcType="DECIMAL" property="s61114" />
    <result column="S64070" jdbcType="DECIMAL" property="s64070" />
    <result column="S64110" jdbcType="DECIMAL" property="s64110" />
    <result column="S67010" jdbcType="DECIMAL" property="s67010" />
    <result column="S70010" jdbcType="DECIMAL" property="s70010" />
    <result column="S70011" jdbcType="DECIMAL" property="s70011" />
    <result column="S70012" jdbcType="DECIMAL" property="s70012" />
    <result column="S70013" jdbcType="DECIMAL" property="s70013" />
    <result column="S70014" jdbcType="DECIMAL" property="s70014" />
    <result column="S70020" jdbcType="DECIMAL" property="s70020" />
    <result column="S70030" jdbcType="DECIMAL" property="s70030" />
    <result column="S70040" jdbcType="DECIMAL" property="s70040" />
    <result column="S70050" jdbcType="DECIMAL" property="s70050" />
    <result column="S70060" jdbcType="DECIMAL" property="s70060" />
    <result column="S70110" jdbcType="DECIMAL" property="s70110" />
    <result column="S90201" jdbcType="DECIMAL" property="s90201" />
    <result column="S90202" jdbcType="DECIMAL" property="s90202" />
    <result column="S90203" jdbcType="DECIMAL" property="s90203" />
    <result column="S90204" jdbcType="DECIMAL" property="s90204" />
    <result column="S90901" jdbcType="DECIMAL" property="s90901" />
    <result column="S90902" jdbcType="DECIMAL" property="s90902" />
    <result column="S90903" jdbcType="DECIMAL" property="s90903" />
    <result column="S90904" jdbcType="DECIMAL" property="s90904" />
    <result column="S90911" jdbcType="DECIMAL" property="s90911" />
    <result column="S90912" jdbcType="DECIMAL" property="s90912" />
    <result column="S90913" jdbcType="DECIMAL" property="s90913" />
    <result column="S90914" jdbcType="DECIMAL" property="s90914" />
    <result column="S90921" jdbcType="DECIMAL" property="s90921" />
    <result column="S90922" jdbcType="DECIMAL" property="s90922" />
    <result column="S90931" jdbcType="DECIMAL" property="s90931" />
    <result column="S90932" jdbcType="DECIMAL" property="s90932" />
    <result column="S90933" jdbcType="DECIMAL" property="s90933" />
    <result column="S90934" jdbcType="DECIMAL" property="s90934" />
    <result column="S90935" jdbcType="DECIMAL" property="s90935" />
    <result column="S90936" jdbcType="DECIMAL" property="s90936" />
    <result column="S91001" jdbcType="DECIMAL" property="s91001" />
    <result column="S91002" jdbcType="DECIMAL" property="s91002" />
    <result column="S91003" jdbcType="DECIMAL" property="s91003" />
    <result column="S91004" jdbcType="DECIMAL" property="s91004" />
    <result column="S91005" jdbcType="DECIMAL" property="s91005" />
    <result column="DUE_DATE" jdbcType="DECIMAL" property="due_date" />
    <result column="CREATE_DATE" jdbcType="DECIMAL" property="create_date" />
  </resultMap>
  <sql id="Base_Position_Column_List">
    MSG_ID, BUSI_DATE, ACCOUNT_SET_NO, POSITION_CODE, STK_ACCT, TRUSTEE_SEAT, MARKET_CODE, 
    SCR_CODE, SCR_ID, CONTRACT_CODE, REMAIN_MARK, POSITION_DIRECTION, POSITION_TYPE, 
    FNC_TOOL_CLASS, LISTED_CIRCULATE_SITUA, APPLY_WAY, CCY_CODE, EXCH_RATE, FAIR_PRICE_TYPE, 
    VALU_FAIR_PRICE, S10101, S10311, S11001, S11003, S11020, S11021, S11031, S11032, 
    S11033, S12030, S12040, S22310, S60110, S60610, S61010, S61111, S61112, S61113, S61114, 
    S64070, S64110, S67010, S70010, S70011, S70012, S70013, S70014, S70020, S70030, S70040, 
    S70050, S70060, S70110, S90201, S90202, S90203, S90204, S90901, S90902, S90903, S90904, 
    S90911, S90912, S90913, S90914, S90921, S90922, S90931, S90932, S90933, S90934, S90935, 
    S90936, S91001, S91002, S91003, S91004, S91005, DUE_DATE, CREATE_DATE
  </sql>
  <select id="selectPositionResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzPositionModel" resultMap="BasePositionResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Position_Column_List" />
    from GTJA_SSGZ_PROD_POSITION
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <delete id="deletePositionResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzPositionModel">
    delete from GTJA_SSGZ_PROD_POSITION
  </delete>


    <update id="updatePositionCpdm">
        update GTJA_SSGZ_PROD_POSITION t
        set t.cpdm =
                (select b.cpdm from fbzx.gtja_ssgz_cpqd@zctg_query b where t.account_set_no = b.ztbh)
        where t.account_set_no is not null
          and t.cpdm is null
    </update>

  <insert id="insertPosition" parameterType="com.gtja.faRtvData.model.GtjaSsgzPositionModel">
      <foreach collection="infos" item="item" index="index" open="begin" close=";end;" separator=";">
          insert into GTJA_SSGZ_PROD_POSITION (MSG_ID, BUSI_DATE, ACCOUNT_SET_NO,
          POSITION_CODE, STK_ACCT, TRUSTEE_SEAT,
          MARKET_CODE, SCR_CODE, SCR_ID,
          CONTRACT_CODE, REMAIN_MARK, POSITION_DIRECTION,
          POSITION_TYPE, FNC_TOOL_CLASS, LISTED_CIRCULATE_SITUA,
          APPLY_WAY, CCY_CODE, EXCH_RATE,
          FAIR_PRICE_TYPE, VALU_FAIR_PRICE, S10101,
          S10311, S11001, S11003,
          S11020, S11021, S11031,
          S11032, S11033, S12030,
          S12040, S22310, S60110,
          S60610, S61010, S61111,
          S61112, S61113, S61114,
          S64070, S64110, S67010,
          S70010, S70011, S70012,
          S70013, S70014, S70020,
          S70030, S70040, S70050,
          S70060, S70110, S90201,
          S90202, S90203, S90204,
          S90901, S90902, S90903,
          S90904, S90911, S90912,
          S90913, S90914, S90921,
          S90922, S90931, S90932,
          S90933, S90934, S90935,
          S90936, S91001, S91002,
          S91003, S91004, S91005,
          DUE_DATE, CREATE_DATE, SOURCE_TIME)
          values (#{msgId,jdbcType=VARCHAR}, #{item.busi_date,jdbcType=DECIMAL}, #{item.account_set_no,jdbcType=VARCHAR},
          #{item.position_code,jdbcType=VARCHAR},#{item.stk_acct,jdbcType=VARCHAR}, #{item.trustee_seat,jdbcType=VARCHAR},
          #{item.market_code,jdbcType=CHAR}, 	#{item.scr_code,jdbcType=VARCHAR}, #{item.scr_id,jdbcType=VARCHAR},
          #{item.contract_code,jdbcType=VARCHAR},#{item.remain_mark,jdbcType=CHAR}, #{item.position_direction,jdbcType=CHAR},
          #{item.position_type,jdbcType=CHAR}, 	#{item.fnc_tool_class,jdbcType=CHAR}, #{item.listed_circulate_situa,jdbcType=CHAR},
          #{item.apply_way,jdbcType=CHAR}, 		#{item.ccy_code,jdbcType=CHAR}, 		#{item.exch_rate,jdbcType=DECIMAL},
          #{item.fair_price_type,jdbcType=CHAR}, #{item.valu_fair_price ,jdbcType=DECIMAL}, #{item.s10101,jdbcType=DECIMAL},
          #{item.s10311,jdbcType=DECIMAL}, #{item.s11001,jdbcType=DECIMAL}, #{item.s11003,jdbcType=DECIMAL},
          #{item.s11020,jdbcType=DECIMAL}, #{item.s11021,jdbcType=DECIMAL}, #{item.s11031,jdbcType=DECIMAL},
          #{item.s11032,jdbcType=DECIMAL}, #{item.s11033,jdbcType=DECIMAL}, #{item.s12030,jdbcType=DECIMAL},
          #{item.s12040,jdbcType=DECIMAL}, #{item.s22310,jdbcType=DECIMAL}, #{item.s60110,jdbcType=DECIMAL},
          #{item.s60610,jdbcType=DECIMAL}, #{item.s61010,jdbcType=DECIMAL}, #{item.s61111,jdbcType=DECIMAL},
          #{item.s61112,jdbcType=DECIMAL}, #{item.s61113,jdbcType=DECIMAL}, #{item.s61114,jdbcType=DECIMAL},
          #{item.s64070,jdbcType=DECIMAL}, #{item.s64110,jdbcType=DECIMAL}, #{item.s67010,jdbcType=DECIMAL},
          #{item.s70010,jdbcType=DECIMAL}, #{item.s70011,jdbcType=DECIMAL}, #{item.s70012,jdbcType=DECIMAL},
          #{item.s70013,jdbcType=DECIMAL}, #{item.s70014,jdbcType=DECIMAL}, #{item.s70020,jdbcType=DECIMAL},
          #{item.s70030,jdbcType=DECIMAL}, #{item.s70040,jdbcType=DECIMAL}, #{item.s70050,jdbcType=DECIMAL},
          #{item.s70060,jdbcType=DECIMAL}, #{item.s70110,jdbcType=DECIMAL}, #{item.s90201,jdbcType=DECIMAL},
          #{item.s90202,jdbcType=DECIMAL}, #{item.s90203,jdbcType=DECIMAL}, #{item.s90204,jdbcType=DECIMAL},
          #{item.s90901,jdbcType=DECIMAL}, #{item.s90902,jdbcType=DECIMAL}, #{item.s90903,jdbcType=DECIMAL},
          #{item.s90904,jdbcType=DECIMAL}, #{item.s90911,jdbcType=DECIMAL}, #{item.s90912,jdbcType=DECIMAL},
          #{item.s90913,jdbcType=DECIMAL}, #{item.s90914,jdbcType=DECIMAL}, #{item.s90921,jdbcType=DECIMAL},
          #{item.s90922,jdbcType=DECIMAL}, #{item.s90931,jdbcType=DECIMAL}, #{item.s90932,jdbcType=DECIMAL},
          #{item.s90933,jdbcType=DECIMAL}, #{item.s90934,jdbcType=DECIMAL}, #{item.s90935,jdbcType=DECIMAL},
          #{item.s90936,jdbcType=DECIMAL}, #{item.s91001,jdbcType=DECIMAL}, #{item.s91002,jdbcType=DECIMAL},
          #{item.s91003,jdbcType=DECIMAL}, #{item.s91004,jdbcType=DECIMAL}, #{item.s91005,jdbcType=DECIMAL},
          #{item.due_date,jdbcType=DECIMAL}, #{item.create_date,jdbcType=DECIMAL},#{sourceTime,jdbcType=VARCHAR} )
      </foreach>
  </insert>

</mapper>