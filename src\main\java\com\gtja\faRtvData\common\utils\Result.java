package com.gtja.faRtvData.common.utils;


import com.alibaba.fastjson.JSON;
import com.gtja.faRtvData.common.exception.ExceptionType;
import com.gtja.faRtvData.common.exception.IErrorCodeType;

import java.io.Serializable;

/**
 * <p>
 * 错误码格式 状态标志1位-应用内错误码-应用代码
 * 状态标志: 0-成功  4-客户端错误(如客户端参数非法等)  5-服务端错误(如服务端内部调用出错)
 * 应用标志: 固定两位长度,每个应用唯一,编号从1开始
 * 应用内部错误码 :  应用内部的错误码,两位英文字符
 * </p>
 */
public class Result<T> implements Serializable {
    /**
     * Result默认信息
     */
    private static final String DEFAULT_SUCCESS_MESSAGE = "成功";
    private static final String WARING_SUCCESS_MESSAGE = "请注意，您持有的基金份额的期限不得少于6个月";
    /**
     * Result错误码格式
     */
    public static final String CODE_FORMAT = "%d-%d-%s";
    private static final long serialVersionUID = 515730635420960456L;

    private String code;
    private String message;
    private String cause;
    private T data;
    private Object controlData;

    public String getCode() {
        return code;
    }

    public Result setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return message;
    }

    public Result setMessage(String message) {
        this.message = message;
        return this;
    }

    public String getCause() {
        return cause;
    }

    public Result setCause(String cause) {
        this.cause = cause;
        return this;
    }

    public T getData() {
        return data;
    }

    public Result setData(T data) {
        this.data = data;
        return this;
    }

    public Object getControlData() {
        return controlData;
    }

    public void setControlData(Object controlData) {
        this.controlData = controlData;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static Result genSuccessResult(Object data,String message) {
        return new Result()
                .setCode("0")
                .setMessage(message)
                .setData(data);
    }

    /**
     * 成功
     *
     * @param data
     * @return
     */
    @SuppressWarnings({"unchecked","rawtypes"})
    public static Result genSuccessResult(Object data) {
        return new Result()
                .setCode("0")
                .setMessage(DEFAULT_SUCCESS_MESSAGE)
                .setData(data);
    }

    public static Result genWarningSuccessResult(Object data) {
        return new Result()
                .setCode("1")
                .setMessage(WARING_SUCCESS_MESSAGE)
                .setData(data);
    }

    /**
     * 客户端非法参数 请求错误
     * 需要客户端也只能客户端做处理后再重新提交 例如后台校验到参数存在错误的情况
     * 不需要返回堆栈错误的情况
     * @param appId 应用名
     * @param errorCodeType
     * @return
     */
    public static Result genInvalidParamResult(String appId, IErrorCodeType errorCodeType) {
        return new Result()
                .setCode(formatCode(ExceptionType.CLIENT, errorCodeType.getCode(), appId))
                .setMessage(errorCodeType.getMessage());
    }

    /**
     * 客户端非法参数 请求错误
     * 需要客户端也只能客户端做处理后再重新提交 例如后台校验到参数存在错误的情况
     * 不需要返回堆栈错误的情况
     * @param appId 应用ID
     * @param code 错误码
     * @param message 给用户看的信息
     * @return
     */
    public static Result genInvalidParamResult(String appId, int code, String message) {
        return new Result()
                .setCode(formatCode(ExceptionType.CLIENT, code, appId))
                .setMessage(message);
    }

    /**
     * 客户端请求错误
     * 需要客户端也只能客户端做处理后再重新提交 例如后台校验到参数存在错误的情况
     * @param appId 应用名
     * @param errorCodeType
     * @param cause 给开发看的信息
     * @return
     */
    public static Result genFailResult(String appId, IErrorCodeType errorCodeType, String cause) {
        return new Result()
                .setCode(formatCode(ExceptionType.CLIENT, errorCodeType.getCode(), appId))
                .setMessage(errorCodeType.getMessage())
                .setCause(cause);
    }

    /**
     * 客户端请求错误
     * 需要客户端也只能客户端做处理后再重新提交 例如后台校验到参数存在错误的情况
     *
     * @param code    错误码
     * @param message 给用户看的信息
     * @param cause   给开发看的信息
     * 建议使用带IErrorCodeType参数的方法精简代码量
     * @return
     */
    public static Result genFailResult(String appId, int code, String message, String cause) {
        return new Result()
                .setCode(formatCode(ExceptionType.CLIENT, code, appId))
                .setMessage(message)
                .setCause(cause);
    }

    /**
     * 服务端处理错误
     * 客户端只需要友好提醒一下客户，不需要也没办法做任何处理。  例如后台调用服务出错
     * @param appId
     * @param errorCodeType
     * @param cause
     * @return
     */
    public static Result genErrorResult(String appId, IErrorCodeType errorCodeType, String cause) {
        return new Result()
                .setCode(formatCode(ExceptionType.SERVER, errorCodeType.getCode(), appId))
                .setMessage(errorCodeType.getMessage())
                .setCause(cause);
    }


    /**
     * 服务端处理错误
     * 客户端只需要友好提醒一下客户，不需要也没办法做任何处理。  例如后台调用服务出错
     *
     * @param appId   应用ID
     * @param code    错误码
     * @param message 给用户看的信息
     * @param cause   给开发开的信息
     * 建议使用带IErrorCodeType参数的方法精简代码量
     * @return
     */
    public static Result genErrorResult(String appId, int code, String message, String cause) {
        return new Result()
                .setCode(formatCode(ExceptionType.SERVER, code, appId))
                .setMessage(message)
                .setCause(cause);
    }

    /**
     *供和其它平台联调使用，不需要返回微服务标示
     * @param errorCodeType
     * @param message
     * @return
     */
    public static Result genErrorResult(IErrorCodeType errorCodeType, String message) {
        return new Result()
                .setCode(String.valueOf(errorCodeType.getCode()))
                .setMessage(message);
    }

    /**
     *供和其它平台联调使用，不需要返回微服务标示
     * @param code
     * @param message
     * @return
     */
    public static Result genErrorResult(int code,String message) {
        return new Result()
                .setCode(String.valueOf(code))
                .setMessage(message);
    }

    /**
     *供和其它平台联调使用，不需要返回微服务标示
     * @param message
     * @return
     */
    public static Result genErrorResult(String message) {
        return Result.genErrorResult(-1, message);
    }

    /**
     * 判定是否成功
     * @param result
     * @return
     */
    public static Boolean isSuccess(Result result){
        return null != result && "0".equals(result.getCode());
    }

    @SuppressWarnings({"unchecked","rawtypes"})
    public static String formatCode(ExceptionType exceptionType, int code, String appId){
        return String.format(CODE_FORMAT, exceptionType.value(), code, appId);
    }

}
