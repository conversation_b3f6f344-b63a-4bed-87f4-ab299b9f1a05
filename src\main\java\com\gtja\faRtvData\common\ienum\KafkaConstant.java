package com.gtja.faRtvData.common.ienum;

/**
 * kafka静态类
 * <AUTHOR>
 */
public class KafkaConstant {

	/**
	 * 成功
	 */
	public static final String SUCCESS_CODE = "00000";
	public static final String SUCCESS_MES = "成功";

	/**
	 * 错误码
	 */
	public static final String KAFKA_SEND_ERROR_CODE = "30001";
	public static final String KAFKA_NO_RESULT_CODE = "30002";
	public static final String KAFKA_NO_OFFSET_CODE = "30003";

	/**
	 * 错误信息
	 */
	public static final String KAFKA_SEND_ERROR_MES = "发送消息超时,联系相关技术人员";
	public static final String KAFKA_NO_RESULT_MES = "未查询到返回结果,联系相关技术人员";
	public static final String KAFKA_NO_OFFSET_MES = "未查到返回数据的offset,联系相关技术人员";




	public static final String  DM_SEND_MESSAGE_ATTACHED_FILE="dm_send_message_attached_file";
	/**************************kafka-producer***************************************/
	public static final String BOOTSTARP_SERVERS_CONFIG = "bootstrap.servers"; //kafka 生产配置
	public static final String RETIES_CONFIG = "retries"; //kafka  生产配置
	public static final String ACKS_CONFIG = "acks"; //kafka  acks
	public static final String BATCH_SIZE_CONFIG = "batch.size"; //kafka  生产配置
	public static final String LINGER_MS_CONFIG = "linger.ms"; //kafka  生产配置
	public static final String BUFFER_MEMORY_CONFIG = "buffer.memory"; //kafka  生产配置
	public static final String KEY_SERIALIZER_CLASS_CONFIG = "key.serializer"; //kafka  生产配置
	public static final String VALUE_SERIALIZER_CLASS_CONFIG = "value.serializer"; //kafka  生产配置

	/**************************kafka-consumer***************************************/
	public static final String ENABKE_AUTO_COMMIT_CONFIG = "commit"; //kafka  消费配置
	public static final String INTERVAL_MS_CONFIG = "auto.commit.interval"; //kafka  服务
	public static final String SESSION_TIMEOUT_CONFIG = "session.timeout"; //kafka  服务
	public static final String GROUP_ID_CONFIG = "group.id"; //kafka  服务
	public static final String OFFSET_RESET_CONFIG = "auto.offset.reset"; //kafka  服务
	public static final String KEY_DESERIALIZER_CLASS_CONFIG = "key.deserializer"; //kafka
	public static final String VALUE_DESERIALIZER_CLASS_CONFIG = "value.deserializer"; //kafka

}
