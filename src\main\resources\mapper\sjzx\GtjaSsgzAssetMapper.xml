<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.sjzx.RtvFracResultDao">
  <resultMap id="BaseAssetResultMap" type="com.gtja.faRtvData.model.GtjaSsgzAssetModel">
    <result column="MSG_ID" jdbcType="VARCHAR" property="MSG_ID" />
      <result column="MSG_GROUP_ID" jdbcType="DECIMAL" property="MSG_GROUP_ID"/>
      <result column="MSG_GROUP_SNO" jdbcType="DECIMAL" property="MSG_GROUP_SNO"/>

    <result column="BUSI_DATE" jdbcType="DECIMAL" property="busi_date" />
    <result column="ACCOUNT_SET_NO" jdbcType="VARCHAR" property="account_set_no" />
    <result column="CCY_CODE" jdbcType="CHAR" property="ccy_code" />
    <result column="EXCH_RATE" jdbcType="DECIMAL" property="exch_rate" />
    <result column="F100201" jdbcType="DECIMAL" property="f100201" />
    <result column="F100211" jdbcType="DECIMAL" property="f100211" />
    <result column="F100212" jdbcType="DECIMAL" property="f100212" />
    <result column="F100213" jdbcType="DECIMAL" property="f100213" />
    <result column="F100214" jdbcType="DECIMAL" property="f100214" />
    <result column="F102101" jdbcType="DECIMAL" property="f102101" />
    <result column="F102111" jdbcType="DECIMAL" property="f102111" />
    <result column="F102112" jdbcType="DECIMAL" property="f102112" />
    <result column="F102113" jdbcType="DECIMAL" property="f102113" />
    <result column="F103101" jdbcType="DECIMAL" property="f103101" />
    <result column="F103111" jdbcType="DECIMAL" property="f103111" />
    <result column="F103121" jdbcType="DECIMAL" property="f103121" />
    <result column="F103131" jdbcType="DECIMAL" property="f103131" />
    <result column="F103141" jdbcType="DECIMAL" property="f103141" />
    <result column="F110201" jdbcType="DECIMAL" property="f110201" />
    <result column="F110301" jdbcType="DECIMAL" property="f110301" />
    <result column="F110401" jdbcType="DECIMAL" property="f110401" />
    <result column="F110501" jdbcType="DECIMAL" property="f110501" />
    <result column="F110601" jdbcType="DECIMAL" property="f110601" />
    <result column="F110901" jdbcType="DECIMAL" property="f110901" />
    <result column="F111001" jdbcType="DECIMAL" property="f111001" />
    <result column="F120201" jdbcType="DECIMAL" property="f120201" />
    <result column="F120301" jdbcType="DECIMAL" property="f120301" />
    <result column="F120401" jdbcType="DECIMAL" property="f120401" />
    <result column="F120499" jdbcType="DECIMAL" property="f120499" />
    <result column="F120701" jdbcType="DECIMAL" property="f120701" />
    <result column="F122101" jdbcType="DECIMAL" property="f122101" />
    <result column="F130301" jdbcType="DECIMAL" property="f130301" />
    <result column="F150101" jdbcType="DECIMAL" property="f150101" />
    <result column="F151101" jdbcType="DECIMAL" property="f151101" />
    <result column="F152101" jdbcType="DECIMAL" property="f152101" />
    <result column="F160101" jdbcType="DECIMAL" property="f160101" />
    <result column="F160601" jdbcType="DECIMAL" property="f160601" />
    <result column="F170101" jdbcType="DECIMAL" property="f170101" />
    <result column="F200101" jdbcType="DECIMAL" property="f200101" />
    <result column="F210101" jdbcType="DECIMAL" property="f210101" />
    <result column="F220201" jdbcType="DECIMAL" property="f220201" />
    <result column="F220301" jdbcType="DECIMAL" property="f220301" />
    <result column="F220401" jdbcType="DECIMAL" property="f220401" />
    <result column="F220501" jdbcType="DECIMAL" property="f220501" />
    <result column="F220601" jdbcType="DECIMAL" property="f220601" />
    <result column="F220701" jdbcType="DECIMAL" property="f220701" />
    <result column="F220801" jdbcType="DECIMAL" property="f220801" />
    <result column="F220901" jdbcType="DECIMAL" property="f220901" />
    <result column="F220902" jdbcType="DECIMAL" property="f220902" />
    <result column="F220903" jdbcType="DECIMAL" property="f220903" />
    <result column="F222101" jdbcType="DECIMAL" property="f222101" />
    <result column="F223101" jdbcType="DECIMAL" property="f223101" />
    <result column="F223201" jdbcType="DECIMAL" property="f223201" />
    <result column="F224101" jdbcType="DECIMAL" property="f224101" />
    <result column="F224199" jdbcType="DECIMAL" property="f224199" />
    <result column="F300301" jdbcType="DECIMAL" property="f300301" />
    <result column="F300302" jdbcType="DECIMAL" property="f300302" />
    <result column="F300303" jdbcType="DECIMAL" property="f300303" />
    <result column="F300304" jdbcType="DECIMAL" property="f300304" />
    <result column="F300305" jdbcType="DECIMAL" property="f300305" />
    <result column="F300306" jdbcType="DECIMAL" property="f300306" />
    <result column="F300307" jdbcType="DECIMAL" property="f300307" />
    <result column="F300308" jdbcType="DECIMAL" property="f300308" />
    <result column="F300309" jdbcType="DECIMAL" property="f300309" />
    <result column="F300310" jdbcType="DECIMAL" property="f300310" />
    <result column="F300311" jdbcType="DECIMAL" property="f300311" />
    <result column="F300312" jdbcType="DECIMAL" property="f300312" />
    <result column="F300313" jdbcType="DECIMAL" property="f300313" />
    <result column="F300314" jdbcType="DECIMAL" property="f300314" />
    <result column="F300315" jdbcType="DECIMAL" property="f300315" />
    <result column="F300316" jdbcType="DECIMAL" property="f300316" />
    <result column="F300317" jdbcType="DECIMAL" property="f300317" />
    <result column="F300318" jdbcType="DECIMAL" property="f300318" />
    <result column="F300319" jdbcType="DECIMAL" property="f300319" />
    <result column="F300320" jdbcType="DECIMAL" property="f300320" />
    <result column="F300321" jdbcType="DECIMAL" property="f300321" />
    <result column="F300330" jdbcType="DECIMAL" property="f300330" />
    <result column="F300331" jdbcType="DECIMAL" property="f300331" />
    <result column="F400101" jdbcType="DECIMAL" property="f400101" />
    <result column="F400102" jdbcType="DECIMAL" property="f400102" />
    <result column="F400103" jdbcType="DECIMAL" property="f400103" />
    <result column="F400199" jdbcType="DECIMAL" property="f400199" />
    <result column="F400201" jdbcType="DECIMAL" property="f400201" />
    <result column="F400301" jdbcType="DECIMAL" property="f400301" />
    <result column="F401101" jdbcType="DECIMAL" property="f401101" />
    <result column="F401102" jdbcType="DECIMAL" property="f401102" />
    <result column="F410301" jdbcType="DECIMAL" property="f410301" />
    <result column="F410302" jdbcType="DECIMAL" property="f410302" />
    <result column="F410401" jdbcType="DECIMAL" property="f410401" />
    <result column="F410402" jdbcType="DECIMAL" property="f410402" />
    <result column="F410411" jdbcType="DECIMAL" property="f410411" />
    <result column="F410421" jdbcType="DECIMAL" property="f410421" />
    <result column="F410431" jdbcType="DECIMAL" property="f410431" />
    <result column="F601101" jdbcType="DECIMAL" property="f601101" />
    <result column="F601102" jdbcType="DECIMAL" property="f601102" />
    <result column="F601104" jdbcType="DECIMAL" property="f601104" />
    <result column="F601198" jdbcType="DECIMAL" property="f601198" />
    <result column="F601199" jdbcType="DECIMAL" property="f601199" />
    <result column="F606101" jdbcType="DECIMAL" property="f606101" />
    <result column="F610101" jdbcType="DECIMAL" property="f610101" />
    <result column="F610198" jdbcType="DECIMAL" property="f610198" />
    <result column="F611101" jdbcType="DECIMAL" property="f611101" />
    <result column="F611102" jdbcType="DECIMAL" property="f611102" />
    <result column="F611103" jdbcType="DECIMAL" property="f611103" />
    <result column="F611104" jdbcType="DECIMAL" property="f611104" />
    <result column="F611106" jdbcType="DECIMAL" property="f611106" />
    <result column="F611107" jdbcType="DECIMAL" property="f611107" />
    <result column="F611108" jdbcType="DECIMAL" property="f611108" />
    <result column="F611198" jdbcType="DECIMAL" property="f611198" />
    <result column="F611199" jdbcType="DECIMAL" property="f611199" />
    <result column="F630299" jdbcType="DECIMAL" property="f630299" />
    <result column="F640301" jdbcType="DECIMAL" property="f640301" />
    <result column="F640401" jdbcType="DECIMAL" property="f640401" />
    <result column="F640501" jdbcType="DECIMAL" property="f640501" />
    <result column="F640601" jdbcType="DECIMAL" property="f640601" />
    <result column="F640701" jdbcType="DECIMAL" property="f640701" />
    <result column="F641101" jdbcType="DECIMAL" property="f641101" />
    <result column="F641199" jdbcType="DECIMAL" property="f641199" />
    <result column="F660501" jdbcType="DECIMAL" property="f660501" />
    <result column="F660599" jdbcType="DECIMAL" property="f660599" />
    <result column="F670101" jdbcType="DECIMAL" property="f670101" />
    <result column="F680201" jdbcType="DECIMAL" property="f680201" />
    <result column="F820201" jdbcType="DECIMAL" property="f820201" />
    <result column="F820203" jdbcType="DECIMAL" property="f820203" />
    <result column="F820204" jdbcType="DECIMAL" property="f820204" />
    <result column="F820205" jdbcType="DECIMAL" property="f820205" />
    <result column="F820303" jdbcType="DECIMAL" property="f820303" />
    <result column="F820304" jdbcType="DECIMAL" property="f820304" />
    <result column="F820305" jdbcType="DECIMAL" property="f820305" />
  </resultMap>
  <sql id="Base_Asset_Column_List">
    MSG_ID, BUSI_DATE, ACCOUNT_SET_NO, CCY_CODE, EXCH_RATE, F100201, F100211, F100212, 
    F100213, F100214, F102101, F102111, F102112, F102113, F103101, F103111, F103121, 
    F103131, F103141, F110201, F110301, F110401, F110501, F110601, F110901, F111001, 
    F120201, F120301, F120401, F120499, F120701, F122101, F130301, F150101, F151101, 
    F152101, F160101, F160601, F170101, F200101, F210101, F220201, F220301, F220401, 
    F220501, F220601, F220701, F220801, F220901, F220902, F220903, F222101, F223101, 
    F223201, F224101, F224199, F300301, F300302, F300303, F300304, F300305, F300306, 
    F300307, F300308, F300309, F300310, F300311, F300312, F300313, F300314, F300315, 
    F300316, F300317, F300318, F300319, F300320, F300321, F300330, F300331, F400101, 
    F400102, F400103, F400199, F400201, F400301, F401101, F401102, F410301, F410302, 
    F410401, F410402, F410411, F410421, F410431, F601101, F601102, F601104, F601198, 
    F601199, F606101, F610101, F610198, F611101, F611102, F611103, F611104, F611106, 
    F611107, F611108, F611198, F611199, F630299, F640301, F640401, F640501, F640601, 
    F640701, F641101, F641199, F660501, F660599, F670101, F680201, F820201, F820203, 
    F820204, F820205, F820303, F820304, F820305
  </sql>
  <select id="selectAssetResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzAssetModel" resultMap="BaseAssetResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Asset_Column_List" />
    from GTJA_SSGZ_PROD_ASSET
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteAssetResult" parameterType="com.gtja.faRtvData.model.GtjaSsgzAssetModel">
    delete from GTJA_SSGZ_PROD_ASSET
  </delete>
    
  <insert id="insertAsset" parameterType="com.gtja.faRtvData.model.GtjaSsgzAssetModel">
      <foreach collection="infos" item="item" index="index" open="begin" close=";end;" separator=";">
          insert into GTJA_SSGZ_PROD_ASSET (MSG_ID, BUSI_DATE, ACCOUNT_SET_NO,
          CCY_CODE, EXCH_RATE, F100201,
          F100211, F100212, F100213,
          F100214, F102101, F102111,
          F102112, F102113, F103101,
          F103111, F103121, F103131,
          F103141, F110201, F110301,
          F110401, F110501, F110601,
          F110901, F111001, F120201,
          F120301, F120401, F120499,
          F120701, F122101, F130301,
          F150101, F151101, F152101,
          F160101, F160601, F170101,
          F200101, F210101, F220201,
          F220301, F220401, F220501,
          F220601, F220701, F220801,
          F220901, F220902, F220903,
          F222101, F223101, F223201,
          F224101, F224199, F300301,
          F300302, F300303, F300304,
          F300305, F300306, F300307,
          F300308, F300309, F300310,
          F300311, F300312, F300313,
          F300314, F300315, F300316,
          F300317, F300318, F300319,
          F300320, F300321, F300330,
          F300331, F400101, F400102,
          F400103, F400199, F400201,
          F400301, F401101, F401102,
          F410301, F410302, F410401,
          F410402, F410411, F410421,
          F410431, F601101, F601102,
          F601104, F601198, F601199,
          F606101, F610101, F610198,
          F611101, F611102, F611103,
          F611104, F611106, F611107,
          F611108, F611198, F611199,
          F630299, F640301, F640401,
          F640501, F640601, F640701,
          F641101, F641199, F660501,
          F660599, F670101, F680201,
          F820201, F820203, F820204,
          F820205, F820303, F820304,
          F820305,source_time)
          values (#{msgId,jdbcType=VARCHAR}, #{item.busi_date,jdbcType=DECIMAL}, #{item.account_set_no,jdbcType=VARCHAR},
          #{item.ccy_code,jdbcType=CHAR}, #{item.exch_rate,jdbcType=DECIMAL}, #{item.f100201,jdbcType=DECIMAL},
          #{item.f100211,jdbcType=DECIMAL}, #{item.f100212,jdbcType=DECIMAL}, #{item.f100213,jdbcType=DECIMAL},
          #{item.f100214,jdbcType=DECIMAL}, #{item.f102101,jdbcType=DECIMAL}, #{item.f102111,jdbcType=DECIMAL},
          #{item.f102112,jdbcType=DECIMAL}, #{item.f102113,jdbcType=DECIMAL}, #{item.f103101,jdbcType=DECIMAL},
          #{item.f103111,jdbcType=DECIMAL}, #{item.f103121,jdbcType=DECIMAL}, #{item.f103131,jdbcType=DECIMAL},
          #{item.f103141,jdbcType=DECIMAL}, #{item.f110201,jdbcType=DECIMAL}, #{item.f110301,jdbcType=DECIMAL},
          #{item.f110401,jdbcType=DECIMAL}, #{item.f110501,jdbcType=DECIMAL}, #{item.f110601,jdbcType=DECIMAL},
          #{item.f110901,jdbcType=DECIMAL}, #{item.f111001,jdbcType=DECIMAL}, #{item.f120201,jdbcType=DECIMAL},
          #{item.f120301,jdbcType=DECIMAL}, #{item.f120401,jdbcType=DECIMAL}, #{item.f120499,jdbcType=DECIMAL},
          #{item.f120701,jdbcType=DECIMAL}, #{item.f122101,jdbcType=DECIMAL}, #{item.f130301,jdbcType=DECIMAL},
          #{item.f150101,jdbcType=DECIMAL}, #{item.f151101,jdbcType=DECIMAL}, #{item.f152101,jdbcType=DECIMAL},
          #{item.f160101,jdbcType=DECIMAL}, #{item.f160601,jdbcType=DECIMAL}, #{item.f170101,jdbcType=DECIMAL},
          #{item.f200101,jdbcType=DECIMAL}, #{item.f210101,jdbcType=DECIMAL}, #{item.f220201,jdbcType=DECIMAL},
          #{item.f220301,jdbcType=DECIMAL}, #{item.f220401,jdbcType=DECIMAL}, #{item.f220501,jdbcType=DECIMAL},
          #{item.f220601,jdbcType=DECIMAL}, #{item.f220701,jdbcType=DECIMAL}, #{item.f220801,jdbcType=DECIMAL},
          #{item.f220901,jdbcType=DECIMAL}, #{item.f220902,jdbcType=DECIMAL}, #{item.f220903,jdbcType=DECIMAL},
          #{item.f222101,jdbcType=DECIMAL}, #{item.f223101,jdbcType=DECIMAL}, #{item.f223201,jdbcType=DECIMAL},
          #{item.f224101,jdbcType=DECIMAL}, #{item.f224199,jdbcType=DECIMAL}, #{item.f300301,jdbcType=DECIMAL},
          #{item.f300302,jdbcType=DECIMAL}, #{item.f300303,jdbcType=DECIMAL}, #{item.f300304,jdbcType=DECIMAL},
          #{item.f300305,jdbcType=DECIMAL}, #{item.f300306,jdbcType=DECIMAL}, #{item.f300307,jdbcType=DECIMAL},
          #{item.f300308,jdbcType=DECIMAL}, #{item.f300309,jdbcType=DECIMAL}, #{item.f300310,jdbcType=DECIMAL},
          #{item.f300311,jdbcType=DECIMAL}, #{item.f300312,jdbcType=DECIMAL}, #{item.f300313,jdbcType=DECIMAL},
          #{item.f300314,jdbcType=DECIMAL}, #{item.f300315,jdbcType=DECIMAL}, #{item.f300316,jdbcType=DECIMAL},
          #{item.f300317,jdbcType=DECIMAL}, #{item.f300318,jdbcType=DECIMAL}, #{item.f300319,jdbcType=DECIMAL},
          #{item.f300320,jdbcType=DECIMAL}, #{item.f300321,jdbcType=DECIMAL}, #{item.f300330,jdbcType=DECIMAL},
          #{item.f300331,jdbcType=DECIMAL}, #{item.f400101,jdbcType=DECIMAL}, #{item.f400102,jdbcType=DECIMAL},
          #{item.f400103,jdbcType=DECIMAL}, #{item.f400199,jdbcType=DECIMAL}, #{item.f400201,jdbcType=DECIMAL},
          #{item.f400301,jdbcType=DECIMAL}, #{item.f401101,jdbcType=DECIMAL}, #{item.f401102,jdbcType=DECIMAL},
          #{item.f410301,jdbcType=DECIMAL}, #{item.f410302,jdbcType=DECIMAL}, #{item.f410401,jdbcType=DECIMAL},
          #{item.f410402,jdbcType=DECIMAL}, #{item.f410411,jdbcType=DECIMAL}, #{item.f410421,jdbcType=DECIMAL},
          #{item.f410431,jdbcType=DECIMAL}, #{item.f601101,jdbcType=DECIMAL}, #{item.f601102,jdbcType=DECIMAL},
          #{item.f601104,jdbcType=DECIMAL}, #{item.f601198,jdbcType=DECIMAL}, #{item.f601199,jdbcType=DECIMAL},
          #{item.f606101,jdbcType=DECIMAL}, #{item.f610101,jdbcType=DECIMAL}, #{item.f610198,jdbcType=DECIMAL},
          #{item.f611101,jdbcType=DECIMAL}, #{item.f611102,jdbcType=DECIMAL}, #{item.f611103,jdbcType=DECIMAL},
          #{item.f611104,jdbcType=DECIMAL}, #{item.f611106,jdbcType=DECIMAL}, #{item.f611107,jdbcType=DECIMAL},
          #{item.f611108,jdbcType=DECIMAL}, #{item.f611198,jdbcType=DECIMAL}, #{item.f611199,jdbcType=DECIMAL},
          #{item.f630299,jdbcType=DECIMAL}, #{item.f640301,jdbcType=DECIMAL}, #{item.f640401,jdbcType=DECIMAL},
          #{item.f640501,jdbcType=DECIMAL}, #{item.f640601,jdbcType=DECIMAL}, #{item.f640701,jdbcType=DECIMAL},
          #{item.f641101,jdbcType=DECIMAL}, #{item.f641199,jdbcType=DECIMAL}, #{item.f660501,jdbcType=DECIMAL},
          #{item.f660599,jdbcType=DECIMAL}, #{item.f670101,jdbcType=DECIMAL}, #{item.f680201,jdbcType=DECIMAL},
          #{item.f820201,jdbcType=DECIMAL}, #{item.f820203,jdbcType=DECIMAL}, #{item.f820204,jdbcType=DECIMAL},
          #{item.f820205,jdbcType=DECIMAL}, #{item.f820303,jdbcType=DECIMAL}, #{item.f820304,jdbcType=DECIMAL},
          #{item.f820305,jdbcType=DECIMAL},#{sourceTime,jdbcType=VARCHAR} )
      </foreach>
  </insert>

    <update id="updateAssetCpdm">
        update GTJA_SSGZ_PROD_ASSET t
        set t.cpdm =
                (select b.cpdm from fbzx.gtja_ssgz_cpqd@zctg_query b where t.account_set_no = b.ztbh)
        where t.account_set_no is not null
          and t.cpdm is null
    </update>

</mapper>