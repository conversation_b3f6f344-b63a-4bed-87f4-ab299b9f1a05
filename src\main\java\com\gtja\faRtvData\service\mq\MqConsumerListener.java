package com.gtja.faRtvData.service.mq;

import com.alibaba.fastjson.JSON;
import com.gtja.faRtvData.dao.sjzx.RtvFracResultDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class MqConsumerListener {
    @Autowired
    private RtvFracResultDao rtvFracResultDao;

    //  @KafkaListener(id = TopicConstans.TOPIC_RJZJBD, clientIdPrefix = "FA-RTV", topics = TopicConstans.TOPIC_RJZJBD)
    // @KafkaListener(topics = TopicConstans.TOPIC_RJZJBD,containerFactory = "kafkaListenerContainerFactory")
  //  @KafkaListener(id = TopicConstans.KAFKA_GROUP, clientIdPrefix = "FA-RTV", topics = TopicConstans.TOPIC_RJZJJS)
    public void onMessage(List<ConsumerRecord<?, ?>> records, Consumer<?, ?> consumer) {
        log.debug("FA-RTV OnMessage::records = [{}], consumer = [{}]", records, consumer);
        try {
            records.forEach(record -> {
                Optional<?> kafkaMessage = Optional.ofNullable(record.value());
                String message = JSON.parseObject(kafkaMessage.get().toString(), String.class);

                log.info("***开始消费******************************************************************************************"
                        + message.toString());
                // GtjaSsgzMqRecLogModel logInfo = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzMqRecLogModel.class);
                // GtjaSsgzAssetModel assetModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzAssetModel.class);
                // GtjaSsgzPointModel pointModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzPointModel.class);
                // GtjaSsgzPositionModel positionModel = JSON.parseObject(kafkaMessage.get().toString(), GtjaSsgzPositionModel.class);
//                rtvFracResultDao.insertMqLog(logInfo);
//                rtvFracResultDao.insertMqLog(assetModel);
//                rtvFracResultDao.insertMqLog(pointModel);
//                rtvFracResultDao.insertMqLog(positionModel);
            });
            // 提交kafka offset
            //  consumer.commitSync();
        } catch (Exception e) {
            log.error("Kafka监听异常", e);
        }
        log.debug("onMessage::records = [{}], consumer = [{}]", records, consumer);
    }
}
