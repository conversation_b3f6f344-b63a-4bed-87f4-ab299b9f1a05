package com.gtja.faRtvData.model;

import lombok.Data;

/**
 * KAFKA 报文表头 公共类
 */
@Data
public class RtvRjzjbdTaMqModel {
//             2."SRC": "19",     // 系统来源 
//            3."SRC_IP": "**************",  // 系统来源IP地址
//            4."MSG_TYPE": "NRNG",  // 消息类型
//            5."MSG_SUBTYPE": "NOTICE",  // 消息子类型
//            6."MSG_ID": "790c7661-698e-40d6-a8be-989171c3b71b", // 消息ID
//            7."TARGET": "["02","10"]", // 目标系统
//            8."SEND_DATE": 20161201, // 发送日期
//            9."SEND_TIME": 111831, // 发送时间
/*           2."SRC": "ZCTG",     // 系统来源 
            3."SRC_IP": "",  // 系统来源IP地址
            4."MSG_TYPE": "RJZJBD",  // 消息类型
            5."MSG_SUBTYPE": "RJZJBD_TA",  // 消息子类型
            6."MSG_ID": "", // 消息ID
            7."TARGET": "["146"]", // 目标系统
            8."SEND_DATE": 20240618, // 发送日期
            9."SEND_TIME": 111831, // 发送时间*/

    String SRC ="ZCTG";
    String SRC_IP ;
    String MSG_TYPE="RJZJBD" ;
    String MSG_SUBTYPE ;
    String MSG_ID ;
    String TARGET="[146]" ;
    String SEND_DATE ;
    String SEND_TIME ;

    GtjaSsgzTaywModel CONTENT =new GtjaSsgzTaywModel();

}
