package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvTqyxxModel {

    @DbfAnnotation(fieldName = "D_DJRQ", fieldLength = 12)
    private String d_djrq;
    @DbfAnnotation(fieldName = "L_YWLB", fieldLength = 4)
    private String l_ywlb;
    @DbfAnnotation(fieldName = "L_ZQNM", fieldLength = 12)
    private String l_zqnm;
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;
    @DbfAnnotation(fieldName = "EN_SYBL", fieldLength = 40)
    private String en_sybl;
    @DbfAnnotation(fieldName = "L_CLBZ", fieldLength = 12)
    private String l_clbz;
    @DbfAnnotation(fieldName = "VC_BZ", fieldLength = 254)
    private String vc_bz;
    @DbfAnnotation(fieldName = "D_YWRQ", fieldLength = 12)
    private String d_ywrq;
    @DbfAnnotation(fieldName = "D_CXRQ", fieldLength = 12)
    private String d_cxrq;
    @DbfAnnotation(fieldName = "D_DZRQ", fieldLength = 12)
    private String d_dzrq;
    @DbfAnnotation(fieldName = "VC_DJBZ", fieldLength = 12)
    private String vc_djbz;
    @DbfAnnotation(fieldName = "VC_DZBZ", fieldLength = 12)
    private String vc_dzbz;
    @DbfAnnotation(fieldName = "EN_SHBL", fieldLength = 40)
    private String en_shbl;
    @DbfAnnotation(fieldName = "L_QYZQNM", fieldLength = 12)
    private String l_qyzqnm;
    @DbfAnnotation(fieldName = "VC_FZDM1", fieldLength = 20)
    private String vc_fzdm1;
    @DbfAnnotation(fieldName = "D_XQJSRQ", fieldLength = 12)
    private String d_xqjsrq;
    @DbfAnnotation(fieldName = "EN_PGJG", fieldLength = 25)
    private String en_pgjg;
    @DbfAnnotation(fieldName = "EN_BL", fieldLength = 40)
    private String en_bl;
    @DbfAnnotation(fieldName = "VC_QYBH", fieldLength = 12)
    private String vc_qybh;
    @DbfAnnotation(fieldName = "D_QYJSRQ", fieldLength = 12)
    private String d_qyjsrq;
    @DbfAnnotation(fieldName = "D_HSSBQSR", fieldLength = 12)
    private String d_hssbqsr;
    @DbfAnnotation(fieldName = "D_HSSBJZR", fieldLength = 12)
    private String d_hssbjzr;
    @DbfAnnotation(fieldName = "D_HSCXQSR", fieldLength = 12)
    private String d_hscxqsr;
    @DbfAnnotation(fieldName = "D_HSCXJZR", fieldLength = 12)
    private String d_hscxjzr;
    @DbfAnnotation(fieldName = "D_ZQHSXQR", fieldLength = 12)
    private String d_zqhsxqr;
    @DbfAnnotation(fieldName = "D_ZQHSDZR", fieldLength = 12)
    private String d_zqhsdzr;
    @DbfAnnotation(fieldName = "L_HSCS", fieldLength = 12)
    private String l_hscs;
    @DbfAnnotation(fieldName = "D_GJSCQRQ", fieldLength = 12)
    private String d_gjscqrq;



}
