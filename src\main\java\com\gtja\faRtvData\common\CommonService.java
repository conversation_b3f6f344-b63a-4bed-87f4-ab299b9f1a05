package com.gtja.faRtvData.common;

import com.gtja.faRtvData.dao.fbzx.CommonDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/****
 *
 * zhanghao
 * 20201126
 */
@Service
public class CommonService {

    @Autowired
    CommonDao commonDao;

    /***
     * 判断当前ywrq是否工作日
     * @param ywrq（yyyymmdd）
     * @return
     */
    public Boolean isTradeDay(String ywrq) {
        return commonDao.isTradeDay(ywrq);
    }

    /***
     * 判断当前ywrq的上一个工作日期
     * @param ywrq（yyyymmdd）
     * @return
     */
    public String getLastTradeDay(String ywrq) {
        return commonDao.getLastTradeDay(ywrq);
    }

    /***
     * @param date 获取date时间往direction(方向)num天工作日
     * @param direction -1 向前  +1 向后
     * @param num 间隔多少个交易日
     * @return
     */
    public String getTradeDay(String date,String direction,Integer num) {
        return commonDao.getTradeday(date,direction,num);
    }

    /**
     * 根据指定日期获取上月第一天
     */
    public String getLastMonthOneDay(String data){
        String lastMonthOneDay = "";
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            Calendar instances = Calendar.getInstance();
            Date parses = format.parse(data);
            instances.setTime(parses);
            instances.add(Calendar.MONTH, -1);
            instances.set(Calendar.DAY_OF_MONTH, 1);
            instances.set(Calendar.HOUR_OF_DAY, 0);
            instances.set(Calendar.MINUTE, 0);
            instances.set(Calendar.SECOND, 0);
            //上个月第一天
            lastMonthOneDay = format.format(instances.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return lastMonthOneDay;
    }

    /**
     * 获取上月最后一天
     * @param date
     * @return
     */
    public String getLastMonthLastDay(String date){
        String lastMonthLastDay = "";
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
            Calendar instance = Calendar.getInstance();
            Date parse = format.parse(date);
            instance.setTime(parse);
            instance.set(Calendar.DAY_OF_MONTH, 0);
            instance.set(Calendar.HOUR_OF_DAY, 23);
            instance.set(Calendar.MINUTE, 59);
            instance.set(Calendar.SECOND, 59);
            // 上个月最后一天
            lastMonthLastDay = format.format(instance.getTime());
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return lastMonthLastDay;
    }
}
