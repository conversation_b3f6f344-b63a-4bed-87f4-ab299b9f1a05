package com.gtja.faRtvData.common.page;

import com.github.pagehelper.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
  *@ProjectName: GenericPageVO.java
  *@Description: TODO 分页查询进行分页的公共类
  *                 参考运营中心的设计
  *<AUTHOR> mayangyang
  *@Date        : 2020/10/3016:21
  *@Version     :1.0
  */
public class GenericPageVO<T> implements Serializable {
    private static final Logger logger = LoggerFactory.getLogger(GenericPageVO.class);
    private static final long serialVersionUID = -6687589586327568483L;

    /**
     * 总页数
     */
    private Integer totalPage;

    /**
     * 当前页记录数
     */
    private Integer currentCount;

    /**
     * 总记录数
     */
    private Long totalCount;

    /**
     * 当前页返回结果
     */
    private List<T> results;
    /**
     * 当前页码
     */
    private Integer pageNo;

    public GenericPageVO() {

    }

    public static <T> GenericPageVO<T> build(Page<T> page) {
        return new GenericPageVO<T>(page.getTotalCount(), page.getTotalPages(),page.getPageNo(), page.getResults());
    }

    public static <T> GenericPageVO<T> buildTo(Page<?> page, Class<T> clazz) {
        List<T> results = null;
        if (null != page.getResults() && page.getResults().size() > 0) {
            results = new ArrayList<T>();
            for (int i = 0; i < page.getResults().size(); i++) {
                try {
                    Object targetObj = clazz.newInstance();
                    BeanUtils.copyProperties(page.getResults().get(i), targetObj);
                    results.add((T) targetObj);
                } catch (Exception e) {
                    logger.error("GenericPageVO Exception",e);
                }
            }
        }
        return new GenericPageVO(page.getTotalCount(), page.getTotalPages(),page.getPageNo(),results);
    }

	public GenericPageVO(List results) {
		this.results = results;
	}

    public GenericPageVO(Integer totalCount, Integer totalPage, Integer pageNo, List<T> results) {
        this.totalCount = totalCount == null?0L: totalCount;
        this.totalPage = totalPage;
        this.results = results;
        this.currentCount = null == results ? 0 : results.size();
        this.pageNo = pageNo;
    }


    public GenericPageVO(Long totalCount, Integer totalPage, Integer pageNo, List<T> results) {
        this.totalCount = totalCount == null?0L: totalCount;
        this.totalPage = totalPage;
        this.results = results;
        this.currentCount = null == results ? 0 : results.size();
        this.pageNo = pageNo;
    }

    public GenericPageVO(Long totalCount, Integer totalPage, List<T> results) {
         this.totalCount = totalCount;
         this.totalPage = totalPage;
         this.results = results;
         this.currentCount = null == results ? 0 : results.size();
    }

    /**
     * pageHelper 分页插件 创建
     * */
    public static <T> GenericPageVO<T> buildTo(PageInfo<?> pageInfo, Class<T> clazz) {
        List<T> results = null;
        List<?> datas = pageInfo.getList();
        if (null != datas && datas.size() > 0) {
            results = new ArrayList<T>();
            for (int i = 0; i <datas.size(); i++) {
                try {
                    Object targetObj = clazz.newInstance();
                    BeanUtils.copyProperties(datas.get(i), targetObj);
                    results.add((T) targetObj);
                } catch (Exception e) {
                    logger.error("GenericPageVO Exception",e);
                }
            }
        }
        return new GenericPageVO(pageInfo.getTotal(), pageInfo.getPages(),pageInfo.getPageNum(),results);
    }

    /**
     *  pageHelper 分页插件
     * */
    public static <T> GenericPageVO<T> build(PageInfo<T> page) {
        return new GenericPageVO(page.getTotal(), page.getPages(),page.getPageNum(), page.getList());
    }

	public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Integer totalPage) {
        this.totalPage = totalPage;
    }

    public Integer getCurrentCount() {
        return currentCount;
    }

    public void setCurrentCount(Integer currentCount) {
        this.currentCount = currentCount;
    }

    public List<T> getResults() {
        return results;
    }

    public void setResults(List<T> results) {
        this.results = results;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"totalPage\":")
                .append(totalPage);
        sb.append(",\"currentCount\":")
                .append(currentCount);
        sb.append(",\"totalCount\":")
                .append(totalCount);
        sb.append(",\"results\":")
                .append(results);
        sb.append(",\"pageNo\":")
                .append(pageNo);
        sb.append('}');
        return sb.toString();
    }
}
