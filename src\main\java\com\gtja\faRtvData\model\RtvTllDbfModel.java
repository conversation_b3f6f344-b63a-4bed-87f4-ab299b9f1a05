package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：银行存款计息利率及相关参数
 */
@Data
public class RtvTllDbfModel {
    @DbfAnnotation(fieldName = "L_ZTBH", fieldLength = 6)
    private String l_ztbh;
    @DbfAnnotation(fieldName = "VC_ZH", fieldLength = 32)
    private String vc_zh;
    @DbfAnnotation(fieldName = "D_BEGIN", fieldLength = 16)
    private String d_begin;
    @DbfAnnotation(fieldName = "L_JZRQ", fieldLength = 2)
    private String l_jzrq;
    @DbfAnnotation(fieldName = "L_JXTS", fieldLength = 4)
    private String l_jxts;
    @DbfAnnotation(fieldName = "VC_JXLB", fieldLength = 2)
    private String vc_jxlb;
    @DbfAnnotation(fieldName = "D_END", fieldLength = 16)
    private String d_end;
    @DbfAnnotation(fieldName = "EN_NLL", fieldLength = 25)
    private String en_nll;
}
