package com.gtja.faRtvData.config.db;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.gtja.faRtvData.config.interceptor.SqlCheckInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.gtja.faRtvData.dao.fbzx", sqlSessionFactoryRef = "fbzxSqlSessionFactory")
public class DataSourceConfigfbzx {
    @Bean(name = "fbzxDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.fbzx")
    public DataSource getDataSourcefbzx(){
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "fbzxSqlSessionFactory")
    @Primary
    public SqlSessionFactory fbzxSqlSessionFactory(@Qualifier("fbzxDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:/mapper/fbzx/*.xml")
        );
        return bean.getObject();
    }

    @Bean("fbzxSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate fbzxSqlSessionTemplate(
        @Qualifier("fbzxSqlSessionFactory") SqlSessionFactory sessionFactory
    ){
        return new SqlSessionTemplate(sessionFactory);
    }

    @Bean("fbzxTransactionManager")
    @Primary
    public PlatformTransactionManager fbzxTransactionManager(@Qualifier("fbzxDataSource") DataSource dataSource){
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager(dataSource);
        return dataSourceTransactionManager;
    }

    @Bean("sqlCheckInterceptor")
    public SqlCheckInterceptor getSqlCheckInterceptor() {
        return new SqlCheckInterceptor();
    }

    @Bean("fbzxSqlCheckInterceptor")
    public SqlCheckInterceptor sqlCheckInterceptor(@Qualifier("fbzxSqlSessionFactory") SqlSessionFactory sessionFactory, @Qualifier("sqlCheckInterceptor") SqlCheckInterceptor sqlCheckInterceptor) {
        sessionFactory.getConfiguration().addInterceptor(sqlCheckInterceptor);
        return sqlCheckInterceptor;
    }

}
