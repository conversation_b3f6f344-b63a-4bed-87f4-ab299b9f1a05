-- Create table
create table GTJA_SSgz_ZJGPYW
(
    busi_date      NUMBER not null,
    busi_flow_no   VARCHAR2(128) not null,
    account_set_no VARCHAR2(32),
    prod_code      VARCHAR2(20),
    digest_id      VARCHAR2(32),
    ccy_code       CHAR(3),
    secuid         VARCHAR2(32),
    market_code    CHAR(3),
    scr_code       VARCHAR2(32),
    fundeffect     NUMBER(18,4),
    stkeffect      NUMBER(18,4),
    bank_acct      VARCHAR2(64),
    provision_acct VARCHAR2(64),
    check_status   VARCHAR2(2),
    send_data      DATE,
    send_status    VARCHAR2(2),
    check_data     DATE,
    oper           VARCHAR2(64),
    create_date    date default sysdate
);
-- Add comments to the columns
comment on column GTJA_SSgz_ZJGPYW.busi_date
  is '业务日期';
comment on column GTJA_SSgz_ZJGPYW.busi_flow_no
  is '流水号';
comment on column GTJ<PERSON>_SSgz_ZJGPYW.account_set_no
  is '账套号';
comment on column GTJA_SSgz_ZJGPYW.digest_id
  is '业务摘要代码';
comment on column GTJA_SSgz_ZJGPYW.ccy_code
  is '币种代码';
comment on column GTJA_SSgz_ZJGPYW.secuid
  is '股东代码';
comment on column GTJA_SSgz_ZJGPYW.market_code
  is '交易市场';
comment on column GTJA_SSgz_ZJGPYW.scr_code
  is '证券代码';
comment on column GTJA_SSgz_ZJGPYW.fundeffect
  is '资金发生数';
comment on column GTJA_SSgz_ZJGPYW.stkeffect
  is '股份发生数';
comment on column GTJA_SSgz_ZJGPYW.bank_acct
  is '银行账号';
comment on column GTJA_SSgz_ZJGPYW.provision_acct
  is '备付金账号';
comment on column GTJA_SSgz_ZJGPYW.check_status
  is '复核状态';
comment on column GTJA_SSgz_ZJGPYW.send_data
  is '发送时间';
comment on column GTJA_SSgz_ZJGPYW.send_status
  is '发送状态';
comment on column GTJA_SSgz_ZJGPYW.check_data
  is '复核时间';
comment on column GTJA_SSgz_ZJGPYW.oper
  is '操作人';
