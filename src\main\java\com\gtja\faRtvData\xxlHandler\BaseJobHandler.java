package com.gtja.faRtvData.xxlHandler;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class BaseJobHandler {

    public static String TAG_NOT_PRINT_TO_XXL = "dont_print_to_xxl_server";
    public static String TAG_IGNORE_LOG = "ignoreLog";

    /**
     * 向xxl-server 端动态发送执行日志
     * @param log 日志内容
     * @param ifSendLogToXxlServer 兼容非xxl调度时的日志发送失败问题
     * @param result 慎重使用，result.msg会直接显示在xxl-server监控页上，不可过多
     */
    void sendLogToXxlServer(String log, boolean ifSendLogToXxlServer, ReturnT<String> result){
        if(ifSendLogToXxlServer){
            XxlJobLogger.log(log);
            if(result != null ) {
                if(result.getMsg()!=null){
                    result.setMsg(result.getMsg() + "\r\n<br>" + log);
                }else {
                    result.setMsg(log);
                }
            }
        }
    }
}
