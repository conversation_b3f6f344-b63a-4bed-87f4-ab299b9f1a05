package com.gtja.faRtvData.downLoad;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.common.utils.ip.IPAddressUtils;
import com.gtja.faRtvData.dao.fbzx.RtvRjzjbdInfoDao;
import com.gtja.faRtvData.dao.sjzx.RtvFracResultDao;
import com.gtja.faRtvData.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.config.SaslConfigs;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import tools.DateTimeTools;

import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.Future;

/**
 * @ProjectName: com.gtja.faData.service
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2020/12/2114:04
 * @Version :1.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class KafkaProducerTest_rjzjbd {
    private final static String TOPIC_RJZJBD = "G_P_USER_ZCTG_RJZJBD";

    @Autowired
    public RtvRjzjbdInfoDao rtvRjzjbdInfoDao;
    @Autowired
    private   RtvFracResultDao rtvFracResultDao;

    @Test
    public void test002() {

        String json = "{\"TARGET\":\"ZCTG\",\"MSG_GROUP_SNO\":1,\"MSG_ID\":\"*****************01\",\"MSG_SUBTYPE\":\"FV_RESULT\",\"MSG_TYPE\":\"FV_RESULT\",\"MSG_GROUP_ID\":\"*****************\",\"SRC\":\"146\",\"SEND_TIME\":101131,\"SRC_IP\":\"***********\",\"CONTENT\":{\"fv_result_asset\":[{\"f606101\":0.0,\"f630299\":0.0,\"accting_entity\":\"************\",\"f410431\":0.0,\"f220501\":0.0,\"f820306\":0.0,\"algo_comb\":\"*\",\"f641101\":0.0,\"f102101\":0.0,\"account_set_no\":\"\",\"f220902\":0.0,\"f400103\":0.0,\"f220901\":82.4,\"f400101\":0.0,\"f220903\":0.0,\"f400102\":0.0,\"f670101\":0.0,\"f300331\":0.0,\"exch_rate\":0.0,\"f100201\":0.0,\"f300330\":0.0,\"f223201\":0.0,\"f160101\":0.0,\"f680201\":0.0,\"f110401\":0.0,\"f200101\":41006.28,\"f102111\":0.0,\"f102112\":0.0,\"f641199\":0.0,\"f102113\":0.0,\"f100211\":0.0,\"f122101\":0.0,\"f300321\":0.0,\"f640701\":213.**************,\"f300320\":0.0,\"f120201\":0.0,\"f100212\":0.0,\"f640301\":0.0,\"f100213\":0.0,\"f100214\":0.0,\"f222101\":0.0,\"f601198\":0.0,\"f111001\":0.0,\"f410411\":0.0,\"f220601\":0.0,\"f610101\":-228237.6,\"f220201\":0.0,\"oneid\":\"************\",\"f300318\":0.0,\"f300317\":0.0,\"ccy_code\":\"CNY\",\"f300319\":0.0,\"f300314\":0.0,\"f300313\":0.0,\"f300316\":0.0,\"f300315\":0.0,\"f300310\":0.0,\"f601104\":0.0,\"f300312\":0.0,\"busi_date\":********,\"f300311\":0.0,\"f601101\":0.0,\"f601102\":0.0,\"f224199\":0.0,\"f410421\":0.0,\"f410301\":0.0,\"f110301\":4000.0,\"f410302\":0.0,\"f300307\":0.0,\"f103101\":0.0,\"f151101\":0.0,\"f300306\":0.0,\"f400199\":0.0,\"f130301\":0.0,\"f300309\":0.0,\"accting_unit\":\"*\",\"f300308\":0.0,\"f300303\":0.0,\"f300302\":0.0,\"f300305\":0.0,\"f300304\":0.0,\"f160601\":0.0,\"f300301\":-243125.2400000001,\"f660599\":0.0,\"f601199\":0.0,\"f640401\":0.0,\"asset_unit\":\"*\",\"f224101\":0.0,\"f220701\":0.0,\"f220301\":0.0,\"f400301\":0.0,\"f103111\":0.0,\"f150101\":0.0,\"f120499\":0.0,\"f660501\":0.0,\"hold_unit\":\"*\",\"f410401\":0.0,\"f410402\":0.0,\"f110201\":40666.0,\"f610198\":0.0,\"f110601\":0.0,\"f401101\":0.0,\"f210101\":0.0,\"f103121\":0.0,\"f401102\":0.0,\"f170101\":0.0,\"f120401\":0.0,\"f640501\":0.0,\"f220801\":0.0,\"f611198\":0.0,\"f611199\":0.0,\"f220401\":0.0,\"f152101\":0.0,\"f103131\":0.0,\"f400201\":0.0,\"f611107\":0.0,\"f223101\":0.0,\"f611108\":0.0,\"f611101\":0.0,\"f611102\":0.0,\"f611106\":0.0,\"f110501\":11096.4,\"f611103\":0.0,\"f611104\":0.0,\"f110901\":0.0,\"f103141\":0.0,\"f120301\":0.0,\"f120701\":0.0,\"f640601\":0.0}],\"fv_result_hold_total\":13,\"fv_result_hold\":[{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":2.6,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":2.6,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"430090.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"430090\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"821001.BJ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.BJ-CNY-*\",\"scr_code\":\"821001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"018\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.BJ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"150080.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"150080\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":10000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.4,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":5.482,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.4,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9451.8,\"s93002\":0.0,\"scr_id\":\"518680.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"518680\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":548.2,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9451.8,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"019026.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"019026\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":201.**************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":201.**************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-182280.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":200000.0,\"s94003\":0.0,\"s70110\":17720.0,\"s90201\":2000.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":2000.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-182280.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":200000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":5.0,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":8.86,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"04\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":5.0,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-9114.0,\"s93002\":0.0,\"scr_id\":\"600000.SH\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"04-00-01-01-99-01-A102937563.SH-CNY-*\",\"scr_code\":\"600000\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":0.0,\"s94003\":0.0,\"s70110\":886.0,\"s90201\":0.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"001\",\"s61010\":-9114.0,\"s91002\":0.0,\"stk_acct\":\"A102937563.SH\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":1.****************,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":10.3,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":1.****************,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":-8970.0,\"s93002\":0.0,\"scr_id\":\"000001.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"000001\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":10000.0,\"s94003\":0.0,\"s70110\":1030.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":-8970.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":10000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0},{\"fnc_tool_class\":\"01\",\"position_direction\":\"01\",\"asset_unit\":\"*\",\"s60610\":0.0,\"s70060\":0.68,\"accting_entity\":\"************\",\"s11003\":0.0,\"s10311\":0.0,\"valu_fair_price\":0.0,\"algo_comb\":\"*\",\"account_set_no\":\"\",\"contract_code\":\"\",\"s67010\":0.0,\"remain_mark\":\"02\",\"s70010\":0.0,\"exch_rate\":0.0,\"create_date\":********,\"fair_price_type\":\"00\",\"s70013\":0.0,\"position_type\":\"00\",\"s70014\":0.0,\"s70011\":0.0,\"s70012\":0.0,\"trustee_seat\":\"010200\",\"hold_unit\":\"*\",\"s61113\":0.0,\"s61112\":0.0,\"s22310\":0.0,\"listed_circulate_situa\":\"01\",\"s61114\":0.0,\"s61111\":0.0,\"s64110\":0.0,\"s64070\":0.68,\"s70020\":0.0,\"s11021\":0.0,\"s93001\":0.0,\"s11020\":0.0,\"s93002\":0.0,\"scr_id\":\"102258.SZ\",\"s12030\":0.0,\"s93003\":0.0,\"s93004\":0.0,\"position_code\":\"02-00-01-01-99-01-**********.SZ-CNY-*\",\"scr_code\":\"102258\",\"oneid\":\"************\",\"s60110\":0.0,\"ccy_code\":\"CNY\",\"apply_way\":\"99\",\"s11032\":0.0,\"busi_date\":********,\"s11033\":0.0,\"s70030\":0.0,\"s90204\":0.0,\"s94001\":0.0,\"s90203\":0.0,\"s94002\":0.0,\"s11031\":0.0,\"s90202\":1000.0,\"s94003\":0.0,\"s70110\":1000.0,\"s90201\":100.0,\"s94004\":0.0,\"s12040\":0.0,\"s70050\":0.0,\"s10101\":100.0,\"due_date\":0,\"market_code\":\"002\",\"s61010\":0.0,\"s91002\":0.0,\"stk_acct\":\"**********.SZ\",\"s91001\":0.0,\"s91000\":0.0,\"accting_unit\":\"*\",\"s11001\":1000.0,\"s70040\":0.0,\"s95002\":0.0,\"s95001\":0.0,\"s95004\":0.0,\"s91005\":0.0,\"s95003\":0.0,\"s91004\":0.0,\"s91003\":0.0}],\"fv_result_hold_offset\":13},\"MSG_GROUP_CNT\":1,\"SEND_DATE\":********}";
        try {
            JSONObject jsonObject1 = JSONObject.parseObject(json);
            GtjaSsgzMqResultModel resultDetailModel = (GtjaSsgzMqResultModel) JSONObject.toJavaObject(jsonObject1, GtjaSsgzMqResultModel.class);
            GtjaSsgzMqRecLogModel logInfo = null;
            List<GtjaSsgzAssetModel> assetModels = resultDetailModel.getCONTENT().getFv_result_asset();
            List<GtjaSsgzPointModel> pointModels = resultDetailModel.getCONTENT().getFv_result_indic();
            List<GtjaSsgzPositionModel> positionModels = resultDetailModel.getCONTENT().getFv_result_hold();

            //rtvFracResultDao.insertMqLog();
//            rtvFracResultDao.insertAsset(assetModels,null);
//            rtvFracResultDao.insertPoint(pointModels,null);
//            rtvFracResultDao.insertPosition(positionModels,null);

            System.out.println(resultDetailModel);
        } catch (Exception e) {
            log.info("****数据转换失败***" + json.toString());
        }
    }




    @Test
    public void KafkaProducerTest() {

        Properties props = new Properties();
        // props.put("bootstrap.servers", "10.176.182.163:9092");
        props.put("bootstrap.servers", "10.187.65.42:9093,10.187.65.43:9093,10.187.65.44:9093");//163   10.187.65.42:9093    10.176.182.137:9092
        props.put("acks", "all");               //消息确认机制  0-不等待相应 1-消息会被写到本地日志中  all-leader会等待所有follower同步完成，确保消息不会动了丢
        props.put("retries", 0);                //大于0 的值 客户端会在消息发送失败 时冲重新发送
        props.put("batch.size", 16384);         //
        props.put("linger.ms", 1);
        props.put("buffer.memory", 33554432);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");

        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        props.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
        props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"sszcKafkaUser\" password=\"sszcKafkaUser@2024\";");

        KafkaProducer<String, String> producer = new KafkaProducer<>(props);
        //  this.topic = topicName;

        String ywlx = "ZQTZ";
       // String ywlx = "TA";
        sendMessage(producer, ywlx,null);

//        List<GtjaSsgzTaywModel> tayws = rtvRjzjbdInfoDao.queryRtvRjzjbdTaInfo();
//        KafkaProducer<String, String> finalProducer = producer;
//        tayws.parallelStream().forEach(info -> {
//            mqHandInit(info, ywlx, ywlxs_sub);
//            log.info("send-risk,MSG_ID:{} " + info.getMSG_ID());
//            String message = JSON.toJSONString(info);
//            finalProducer.send(new ProducerRecord<String, String>(TOPIC_RJZJBD, message));
//            System.out.printf("发送的信息：" + message);
////                ListenableFuture future = kafkaTemplate.send(TOPIC_RJZJBD, message);
////                future.addCallback(o -> System.out.println("send-risk消息发送成功：" + message)
////                        , throwable -> System.out.println("risk消息发送失败：" + message));
//        });

//        List<GtjaSsgzZjgpywModel> zjgpyw = rtvRjzjbdInfoDao.queryRtvRjzjbdZjgpInfo();
//        KafkaProducer<String, String> finalProducer = producer;
//        zjgpyw.stream().forEach(info -> {
//            RtvRjzjbdGpMqModel base = new RtvRjzjbdGpMqModel();
//            base.setContent(info);
//            mqHandInit(base, ywlx);
//            log.info("send-risk,MSG_ID:{} " + base.getMSG_ID());
//            String message = JSON.toJSONString(base);
//            // finalProducer.send(new ProducerRecord<String, String>(TOPIC_RJZJBD, message));
//            System.out.printf("发送的信息：" + message);
//        });
    }


    public void sendMessage(KafkaProducer<String, String> finalProducer, String ywlx,String busi_date) {
        if (Constants.RJZJBD_YWLX.TA.equals(ywlx)) {
            List<GtjaSsgzTaywModel> tayws = rtvRjzjbdInfoDao.queryRtvRjzjbdTaInfo(busi_date);
            tayws.stream().forEach(info -> { //parallelStream
                RtvRjzjbdTaMqModel base = new RtvRjzjbdTaMqModel();
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base, SerializerFeature.WriteNullStringAsEmpty,SerializerFeature.WriteNullNumberAsZero);
                Future<RecordMetadata> future = finalProducer.send(new ProducerRecord<String, String>(TOPIC_RJZJBD, message));
                System.out.printf("发送的信息：" + message);
                //  future.cancel(true -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
            });
        } else if (Constants.RJZJBD_YWLX.ZQTZ.equals(ywlx)) {
            List<GtjaSsgzZjgpywModel> zqtzYws = rtvRjzjbdInfoDao.queryRtvRjzjbdZjgpInfo(busi_date);
            zqtzYws.stream().forEach(info -> {
                RtvRjzjbdGpMqModel base = new RtvRjzjbdGpMqModel();
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base, SerializerFeature.WRITE_MAP_NULL_FEATURES,SerializerFeature.WriteNullNumberAsZero);
                Future<RecordMetadata> future = finalProducer.send(new ProducerRecord<String, String>(TOPIC_RJZJBD, message));
                System.out.printf("发送的信息：" + message);
                //   future.addCallback(o -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
            });
        }
    }

    private void mqHandInit(RtvRjzjbdGpMqModel info, String ywlx) {
        info.setSRC_IP(IPAddressUtils.getIPAddress());
        info.setMSG_ID(UUID.randomUUID().toString());
        info.setMSG_TYPE(Constants.RJZJBD_YWLX.COMMOM);
        info.setMSG_SUBTYPE(Constants.RJZJBD_YWLX.COMMOM + "_" + ywlx);
        info.setSEND_DATE(DateTimeTools.getDateNumNow());
        info.setSEND_TIME(DateTimeTools.getTimestampNow().substring(6, 6));
    }

    private void mqHandInit(RtvRjzjbdTaMqModel info, String ywlx) {
        info.setSRC_IP(IPAddressUtils.getIPAddress());
        info.setMSG_ID(UUID.randomUUID().toString());
        info.setMSG_TYPE(Constants.RJZJBD_YWLX.COMMOM);
        info.setMSG_SUBTYPE(Constants.RJZJBD_YWLX.COMMOM + "_" + ywlx);
        info.setSEND_DATE(DateTimeTools.getDateNumNow());
        info.setSEND_TIME(DateTimeTools.getTimestampNow().substring(6, 6));
    }
}
