package com.gtja.faRtvData.config.db;

import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;


@Configuration
@MapperScan(basePackages = "com.gtja.faRtvData.dao.sjzx", sqlSessionFactoryRef = "sjzxSqlSessionFactory")
public class DataSourceConfigsjzx {
    @Bean(name = "sjzxDataSource")
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.sjzx")
    public DataSource getDataSourcesjzx(){
        return DataSourceBuilder.create().build();
    }

    @Bean(name = "sjzxSqlSessionFactory")
    @Primary
    public SqlSessionFactory sjzxSqlSessionFactory(@Qualifier("sjzxDataSource") DataSource dataSource) throws Exception {
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(
                new PathMatchingResourcePatternResolver().getResources("classpath*:/mapper/sjzx/*.xml")
        );
        return bean.getObject();
    }

    @Bean("sjzxSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate sjzxSqlSessionTemplate(
        @Qualifier("sjzxSqlSessionFactory") SqlSessionFactory sessionFactory
    ){
        return new SqlSessionTemplate(sessionFactory);
    }

    @Bean("sjzxTransactionManager")
    @Primary
    public PlatformTransactionManager sjzxTransactionManager(@Qualifier("sjzxDataSource") DataSource dataSource){
        DataSourceTransactionManager dataSourceTransactionManager = new DataSourceTransactionManager(dataSource);
        return dataSourceTransactionManager;
    }


}
