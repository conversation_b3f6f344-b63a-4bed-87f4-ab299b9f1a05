package com.gtja.faRtvData.downLoad;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import tools.DateTimeTools;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.SocketAddress;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class Test {
    public static void main(String[] args) {
        System.out.println(DateTimeTools.getNowHours("08:30"));
        Test test = new Test();
        //test.fetchFile();
        String  createPath="D:\\Users\\User\\Desktop\\实时估值\\20240710\\SXA405\\BALANCE_SXA405_20240710.DBF";
       String a= createPath.replace("BALANCE_CPDM_YYYYMMDD.DBF","[\" 2+ \"]").concat("BALANCE_CPDM_YYYYMMDD.DBF");
        System.out.println(a);
    }

    public void fetchFile() {
//        try {
//            RestTemplate restTemplate = getRestTemplate();
//            restTemplate.getMessageConverters().add(new ByteArrayHttpMessageConverter());
//            HttpHeaders headers = new HttpHeaders();
//            headers.setAccept(Arrays.asList(MediaType.APPLICATION_OCTET_STREAM));
//            HttpEntity<String> entity = new HttpEntity<>(headers);
//            ResponseEntity<byte[]> response = restTemplate.exchange("https://www1.hkexnews.hk/listedco/listconews/sehk/2022/1121/2022112101111_c.pdf",
//                    HttpMethod.GET, entity, byte[].class, "1");
//            if (response.getStatusCode() == HttpStatus.OK) {
//                Files.write(Paths.get("D:\\2022112101111_c.pdf"), response.getBody());
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
    }

//    public SimpleClientHttpRequestFactory httpClientFactory() {
//        SimpleClientHttpRequestFactory httpRequestFactory = new SkipSslVerificationHttpRequestFactory();
//        httpRequestFactory.setReadTimeout(30000);
//        httpRequestFactory.setConnectTimeout(10000);
//        String host = "************";
//        int port = 3128;
//        SocketAddress address = new InetSocketAddress(host, port);
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, address);
//        httpRequestFactory.setProxy(proxy);
//        //设置环境变量
//        System.getProperties().setProperty("http.proxyHost", host);
//        System.getProperties().setProperty("http.proxyPort", port + "");
//
//        return httpRequestFactory;
//    }

//    public RestTemplate getRestTemplate() {
//        List<HttpMessageConverter<?>> messageConverters = new ArrayList<HttpMessageConverter<?>>();
//        messageConverters.add(new ByteArrayHttpMessageConverter());
//        RestTemplate restTemplate = new RestTemplate(messageConverters);
//        restTemplate.setRequestFactory(httpClientFactory());
//        return restTemplate;
//    }
}
