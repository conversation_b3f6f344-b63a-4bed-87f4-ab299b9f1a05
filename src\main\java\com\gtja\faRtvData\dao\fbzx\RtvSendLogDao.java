package com.gtja.faRtvData.dao.fbzx;

import com.gtja.faRtvData.model.GtjaSsgzTaywModel;
import com.gtja.faRtvData.model.GtjaSsgzZjgpywModel;
import com.gtja.faRtvData.model.RtvRjzjbdGpMqModel;
import com.gtja.faRtvData.model.SsgzMqSendLog;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * RealTimeValueDao 日间资金变动 ta
 */
@Repository
public interface RtvSendLogDao {
    int  insertSenderLog(SsgzMqSendLog log);
}
