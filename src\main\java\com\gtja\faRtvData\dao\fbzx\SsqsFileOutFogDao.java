package com.gtja.faRtvData.dao.fbzx;

import com.gtja.faRtvData.model.RtvCommonPdfModel;
import com.gtja.faRtvData.model.SsqsFileOutFogModel;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ProjectName: RealTimeValueDao.java
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2022/9/2211:32
 * @Version :1.0
 */
@Repository
public interface SsqsFileOutFogDao {

    void insertLog(SsqsFileOutFogModel param);

    void updateFileLog(String id);
    void updateFileLogByIds(@Param("infos") List<SsqsFileOutFogModel> infos);

    List<SsqsFileOutFogModel>   selectSsgzLog(String ywrq);
}
