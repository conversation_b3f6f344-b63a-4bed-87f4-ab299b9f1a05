package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *    概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvTqsDbfModel {
    @DbfAnnotation(fieldName = "CODE", fieldLength = 16)
    private String code;//broker_
    @DbfAnnotation(fieldName = "NAME", fieldLength = 64)
    private String name;
    @DbfAnnotation(fieldName = "FULL_NAME", fieldLength = 64)
    private String full_name;
}
