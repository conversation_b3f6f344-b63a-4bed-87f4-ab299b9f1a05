<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.WorkdayInfoDao">
    <select id="getTradeday" resultType="java.lang.String">
        WITH temp AS(
            SELECT
             row_number() OVER(ORDER BY cal_date) AS rn,
             t.cal_date
            FROM fbzx.ref_workday_info t
            WHERE t.workday_sign = 1
             OR t.cal_date = #{date}
         )
        SELECT t.cal_date FROM temp t
        WHERE EXISTS(
          SELECT 1 FROM temp a
          WHERE a.cal_date = #{date}
            AND t.rn = a.rn + (#{direction} * #{num})
        )
    </select>
    <select id="getTradeNumOfWeek" resultType="java.lang.String">
        SELECT t.cal_date
        FROM fbzx.ref_workday_info t
        WHERE t.week_order = #{num}
        AND t.firstday_week = (SELECT firstday_week
                         FROM fbzx.ref_workday_info
                         WHERE cal_date = #{date})
    </select>
    <select id="getTradeNumOfMonth" resultType="java.lang.String">
        SELECT t.cal_date
        FROM fbzx.ref_workday_info t
        WHERE t.month_order = #{num}
        AND t.firstday_month = (SELECT firstday_month
                         FROM fbzx.ref_workday_info
                         WHERE cal_date = #{date})
    </select>
    <select id="getTradeNumOfQuar" resultType="java.lang.String">
        SELECT t.cal_date
        FROM fbzx.ref_workday_info t
        WHERE t.quar_order = #{num}
        AND t.firstday_quar = (SELECT firstday_quar
                         FROM fbzx.ref_workday_info
                         WHERE cal_date = #{date})
    </select>
    <select id="getTradeNumOfYear" resultType="java.lang.String">
        SELECT t.cal_date
        FROM fbzx.ref_workday_info t
        WHERE t.workday_order = #{num}
        AND t.firstday_year = (SELECT firstday_year
                         FROM fbzx.ref_workday_info
                         WHERE cal_date = #{date})
    </select>

    <select id="getTradeday2" resultType="java.lang.String">
		<![CDATA[
        select to_char(tgcbs.sf_pub_addtradedays@tggz_query(#{type},to_date(#{date},'yyyyMMdd'), #{direction}, #{num}),'yyyyMMdd') lastTradeDay from dual
        ]]>
	</select>

    <resultMap id="workdayInfoMap" type="com.gtja.faRtvData.model.WorkdayInfo">
        <result column="cal_date" property="calDate"></result>
        <result column="workday_sign" property="workdaySign"></result>
        <result column="workday_order" property="workdayOrder"></result>
        <result column="recent_workday" property="recentWorkday"></result>
        <result column="last_workday" property="lastWorkday"></result>
        <result column="next_workday" property="nextWorkday"></result>
        <result column="is_lastworkday_week" property="isLastworkdayWeek"></result>
        <result column="is_lastworkday_month" property="isLastworkdayMonth"></result>
        <result column="is_lastworkday_quar" property="isLastworkdayQuar"></result>
        <result column="is_lastworkday_year" property="isLastworkdayYear"></result>
        <result column="is_firworkday_week" property="isFirworkdayWeek"></result>
        <result column="is_firworkday_month" property="isFirworkdayMonth"></result>
        <result column="is_firworkday_quar" property="isFirworkdayQuar"></result>
        <result column="is_firworkday_year" property="isFirworkdayYear"></result>
        <result column="firworkday_week" property="firworkdayWeek"></result>
        <result column="firworkday_month" property="firworkdayMonth"></result>
        <result column="firworkdayquar" property="firworkdayQuar"></result>
        <result column="firworkday_year" property="firworkdayYear"></result>
        <result column="lastworkday_week" property="lastworkdayWeek"></result>
        <result column="lastworkday_month" property="lastworkdayMonth"></result>
        <result column="lastworkday_quar" property="lastworkdayQuar"></result>
        <result column="lastworkday_year" property="lastworkdayYear"></result>
        <result column="firstday_week" property="firstdayWeek"></result>
        <result column="firstday_month" property="firstdayMonth"></result>
        <result column="firstday_quar" property="firstdayQuar"></result>
        <result column="firstday_year" property="firstdayYear"></result>
        <result column="lastday_week" property="lastdayWeek"></result>
        <result column="lastday_month" property="lastdayMonth"></result>
        <result column="lastday_quar" property="lastdayQuar"></result>
        <result column="lastday_year" property="lastdayYear"></result>
        <result column="week_order" property="weekOrder"></result>
        <result column="month_order" property="monthOrder"></result>
        <result column="quar_order" property="quarOrder"></result>
    </resultMap>
</mapper>
