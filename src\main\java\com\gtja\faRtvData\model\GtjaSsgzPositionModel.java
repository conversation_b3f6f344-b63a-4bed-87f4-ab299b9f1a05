package com.gtja.faRtvData.model;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class GtjaSsgzPositionModel {
    //private String msgId;
    String MSG_ID ;
    String MSG_GROUP_SNO ;
    String MSG_GROUP_ID ;

    private Integer busi_date;

    private String account_set_no;

    private String position_code;

    private String stk_acct;

    private String trustee_seat;

    private String market_code;

    private String scr_code;

    private String scr_id;

    private String contract_code;

    private String remain_mark;

    private String position_direction;

    private String position_type;

    private String fnc_tool_class;

    private String listed_circulate_situa;

    private String apply_way;

    private String ccy_code;

    private BigDecimal exch_rate;

    private String fair_price_type;

    private BigDecimal valu_fair_price;

    private BigDecimal s10101;

    private BigDecimal s10311;

    private BigDecimal s11001;

    private BigDecimal s11003;

    private BigDecimal s11020;

    private BigDecimal s11021;

    private BigDecimal s11031;

    private BigDecimal s11032;

    private BigDecimal s11033;

    private BigDecimal s12030;

    private BigDecimal s12040;

    private BigDecimal s22310;

    private BigDecimal s60110;

    private BigDecimal s60610;

    private BigDecimal s61010;

    private BigDecimal s61111;

    private BigDecimal s61112;

    private BigDecimal s61113;

    private BigDecimal s61114;

    private BigDecimal s64070;

    private BigDecimal s64110;

    private BigDecimal s67010;

    private BigDecimal s70010;

    private BigDecimal s70011;

    private BigDecimal s70012;

    private BigDecimal s70013;

    private BigDecimal s70014;

    private BigDecimal s70020;

    private BigDecimal s70030;

    private BigDecimal s70040;

    private BigDecimal s70050;

    private BigDecimal s70060;

    private BigDecimal s70110;

    private BigDecimal s90201;

    private BigDecimal s90202;

    private BigDecimal s90203;

    private BigDecimal s90204;

    private BigDecimal s90901;

    private BigDecimal s90902;

    private BigDecimal s90903;

    private BigDecimal s90904;

    private BigDecimal s90911;

    private BigDecimal s90912;

    private BigDecimal s90913;

    private BigDecimal s90914;

    private BigDecimal s90921;

    private BigDecimal s90922;

    private BigDecimal s90931;

    private BigDecimal s90932;

    private BigDecimal s90933;

    private BigDecimal s90934;

    private BigDecimal s90935;

    private BigDecimal s90936;

    private BigDecimal s91001;

    private BigDecimal s91002;

    private BigDecimal s91003;

    private BigDecimal s91004;

    private BigDecimal s91005;

    private BigDecimal due_date;

    private BigDecimal create_date;
}