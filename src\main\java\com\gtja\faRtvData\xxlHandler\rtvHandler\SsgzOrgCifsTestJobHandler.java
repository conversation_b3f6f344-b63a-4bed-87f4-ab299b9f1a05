//package com.gtja.faRtvData.xxlHandler.rtvHandler;
//
//import com.gtja.faRtvData.model.NfsInfoModel;
//import com.gtja.faRtvData.xxlHandler.BaseJobHandler;
//import com.xxl.job.core.biz.model.ReturnT;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import jcifs.CIFSContext;
//import jcifs.CIFSException;
//import jcifs.config.PropertyConfiguration;
//import jcifs.context.BaseContext;
//import jcifs.smb.NtlmPasswordAuthentication;
//import jcifs.smb.SmbException;
//import jcifs.smb.SmbFile;
//import jcifs.smb.SmbFileOutputStream;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import tools.RegularTools;
//
//import java.io.*;
//import java.net.MalformedURLException;
//import java.net.UnknownHostException;
//import java.util.Properties;
//
///**
// * @FileName: SsgzOrgCifsTestJobHandler.java
// * @anthor: Magic
// * @create: 2025/2/14- 18:07
// * @TODO: //TODO org cifs 是 cifs 的升级，smb 协议和smb1协议    end
// * @Version：1.0
// */
//@Slf4j
//@Component
//public class SsgzOrgCifsTestJobHandler extends BaseJobHandler {
//    protected static String SFTP_PATH = "/ssgz/";
//    @Autowired
//    NfsInfoModel nfsInfo;
//
//
//    /**
//     * @demoName: SsgzCifsTest001JobHandler
//     * @Anthor: Magic
//     * @Create: 2025/2/14- 16:32
//     * @TODO: //TODO   本地测试通过，再没有开通防火墙的情况下可以进行文件传输 end
//     * @Param:
//     * @Return:
//     */
//    @XxlJob("SsgzCifsTest001JobHandler")
//    public ReturnT<String> SsgzCifsTest001JobHandler(String param) {
//        ReturnT result = new ReturnT("实时估值-cifs测试流程)");
//        String localAbsoluteFile = "C:\\Users\\<USER>\\BALANCE_SCE364_20250213.DBF";
//        log.info("cifs-测试 ip+share");
//        SmbFileOutputStream smbOut = null;
//        BufferedInputStream bf = null;
//        CIFSContext cifs = null;
//
//
//        String smbUrl = "smb://" + nfsInfo.getIp() + "/" + nfsInfo.getShare() + "/" + "ssgzTest";
//        log.info("url:" + smbUrl);
//
//        Properties ps = new Properties();
//        ps.setProperty("jcifs.smb.client.domain", nfsInfo.ip);
//        ps.setProperty("jcifs.smb.client.username", nfsInfo.userName);
//        ps.setProperty("jcifs.smb.client.password", nfsInfo.passWord);
//        ps.setProperty("jcifs.smb.client.dfs.disabled", "true");
//
//        log.info("connect cifs start:ip:{};path:{}", nfsInfo.getIp(), nfsInfo.getPath());
//        SmbFile smbFolder = null;
//        try {
//            cifs = new BaseContext(new PropertyConfiguration(ps));
//            smbFolder = new SmbFile(smbUrl, cifs);
//
//            if (!smbFolder.exists()) {
//                smbFolder.mkdir();
//            }
//        } catch (CIFSException e) {
//           // throw new RuntimeException(e);
//            log.info("new mkdir 异常：" + e.getMessage());
//        } catch (MalformedURLException e) {
//            log.info("new SmbFile 异常：" + e.getMessage());
//        }
//        //向远程文件夹上传文件
//        //BALANCE_SCE364_20250213.DBF
//        File file = new File(localAbsoluteFile);
//        SmbFile smbFile = null;
//        try {
//            smbFile = new SmbFile(smbUrl + "/" + file.getName(), cifs);
//        } catch (MalformedURLException e) {
//            log.info("new SmbFile 异常：" + e.getMessage());
//        }
//        try {
//            smbOut = new SmbFileOutputStream(smbFile);
//            bf = new BufferedInputStream(new FileInputStream(file));
//            byte[] bt = new byte[8192];
//            int n = bf.read(bt);
//            while (n != -1) {
//                smbOut.write(bt, 0, n);
//                smbOut.flush();
//                n = bf.read(bt);
//            }
//        } catch (UnknownHostException ue) {
//            log.error("文件上传失败，失败原因:{}", ue);
//        } catch (Exception e) {
//            log.error("文件上传失败，失败原因:{}", e);
//        } finally {
//            if (smbOut != null) {
//                try {
//                    smbOut.close();
//                } catch (IOException e) {
//                    log.error("smbOut.close 失败原因:{}", e);
//                }
//            }
//            if (bf != null) {
//                try {
//                    bf.close();
//                } catch (IOException e) {
//                    log.error("bf.close 失败原因:{}", e);
//                }
//            }
//        }
//        return result;
//    }
//
//    @XxlJob("SsgzCifsTest003JobHandler")
//    public ReturnT<String> SsgzCifsTest003JobHandler(String param) {
//        ReturnT result = new ReturnT("实时估值-共享盘-测试流程)");
//        log.info("实时估值-共享盘-测试流程开始");
//        String localFile = RegularTools.getParamFromCmd(param, "localFile", "C:\\Users\\<USER>\\BALANCE_SCE364_20250213.DBF");
//        String targeFile = RegularTools.getParamFromCmd(param, "targeFile", "I:\\SSGZ\\Test\\");
//
//        FileOutputStream smbOut = null;
//        BufferedInputStream bf = null;
//        File file = new File(targeFile);
//        if (!file.isDirectory()) {
//            log.info("实时估值-测试流程开始:创建目录" + targeFile);
//            file.mkdirs();
//        }
//
//        try {
//            log.info("实时估值-测试流程开始:文件copy开始");
//            File outPutFile = new File(localFile);
//            smbOut = new FileOutputStream(targeFile + "/" + outPutFile.getName());
//            bf = new BufferedInputStream(new FileInputStream(localFile));
//            byte[] bt = new byte[8192];
//            int n = bf.read(bt);
//            while (n != -1) {
//                smbOut.write(bt, 0, n);
//                smbOut.flush();
//                n = bf.read(bt);
//            }
//            log.info("实时估值-测试流程开始:文件copy完成");
//        } catch (UnknownHostException ue) {
//            log.error("文件上传失败，失败原因:{}", ue);
//        } catch (Exception e) {
//            log.error("文件上传失败，失败原因:{}", e);
//        } finally {
//            if (smbOut != null) {
//                try {
//                    smbOut.close();
//                } catch (IOException e) {
//                    log.error("smbOut.close 失败原因:{}", e);
//                }
//            }
//            if (bf != null) {
//                try {
//                    bf.close();
//                } catch (IOException e) {
//                    log.error("bf.close 失败原因:{}", e);
//                }
//            }
//        }
//        return result;
//    }
//}
