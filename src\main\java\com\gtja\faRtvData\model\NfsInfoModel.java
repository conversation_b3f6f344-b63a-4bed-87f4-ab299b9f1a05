package com.gtja.faRtvData.model;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "ssgz.nfs")
@Component
public class NfsInfoModel {
    private static final long serialVersionUID = 1L;
    /**
     * cifs服务器ip
     */
    public String ip;

    /**
     * cifs 文件地址
     *
     */
    public String path;


    /**
     * cifs服务器用户名
     */
    public String userName;

    /**
     * cifs服务器密码
     **/
    public String passWord;

public String share;
}
