<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.sjzx.RtvFracResultDao">
    <resultMap id="BaseRecLogResultMap" type="com.gtja.faRtvData.model.GtjaSsgzMqRecLogModel">
        <result column="SRC" jdbcType="VARCHAR" property="SRC"/>
        <result column="SRC_IP" jdbcType="VARCHAR" property="SRC_IP"/>
        <result column="MSG_TYPE" jdbcType="VARCHAR" property="MSG_TYPE"/>
        <result column="MSG_SUBTYPE" jdbcType="VARCHAR" property="MSG_SUBTYPE"/>
        <result column="MSG_ID" jdbcType="VARCHAR" property="MSG_ID"/>
        <result column="MSG_GROUP_ID" jdbcType="DECIMAL" property="MSG_GROUP_ID"/>
        <result column="MSG_GROUP_SNO" jdbcType="DECIMAL" property="MSG_GROUP_SNO"/>
        <result column="MSG_GROUP_CNT" jdbcType="DECIMAL" property="MSG_GROUP_CNT"/>
        <result column="TARGET" jdbcType="VARCHAR" property="TARGET"/>
        <result column="SEND_DATE" jdbcType="DECIMAL" property="SEND_DATE"/>
        <result column="SEND_TIME" jdbcType="DECIMAL" property="SEND_TIME"/>
        <result column="LOAD_TIME" jdbcType="VARCHAR" property="LOAD_TIME"/>
        <result column="FV_RESULT_HOLD_TOTAL" jdbcType="DECIMAL" property="fv_result_hold_total"/>
        <result column="FV_RESULT_HOLD_OFFSET" jdbcType="DECIMAL" property="fv_result_hold_offset"/>
    </resultMap>

    <sql id="Base_RecLog_Column_List">
        SRC , SRC_IP, MSG_TYPE, MSG_SUBTYPE, MSG_ID, MSG_GROUP_ID, MSG_GROUP_SNO, MSG_GROUP_CNT,
         TARGET, SEND_DATE, SEND_TIME, LOAD_TIME,FV_RESULT_HOLD_TOTAL,FV_RESULT_HOLD_OFFSET
    </sql>

    <select id="selectRecLog" parameterType="com.gtja.faRtvData.model.GtjaSsgzMqRecLogModel"
            resultMap="BaseRecLogResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_RecLog_Column_List"/>
        from GTJA_SSGZ_MQ_REC_LOG
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteRecLog" parameterType="com.gtja.faRtvData.model.GtjaSsgzMqRecLogModel">
        delete
        from GTJA_SSGZ_MQ_REC_LOG
    </delete>

    <insert id="insertMqLog" parameterType="com.gtja.faRtvData.model.GtjaSsgzMqRecLogModel">
        insert into GTJA_SSGZ_MQ_REC_LOG (SRC, SRC_IP, MSG_TYPE,
                                          MSG_SUBTYPE, MSG_ID, MSG_GROUP_ID,
                                          MSG_GROUP_SNO, MSG_GROUP_CNT, TARGET,
                                          SEND_DATE, SEND_TIME, LOAD_TIME,
                                          FV_RESULT_HOLD_OFFSET,FV_RESULT_HOLD_TOTAL,OFFSET,CONTENT)
        values (#{SRC,jdbcType=VARCHAR}, #{SRC_IP,jdbcType=VARCHAR}, #{MSG_TYPE,jdbcType=VARCHAR},
                #{MSG_SUBTYPE,jdbcType=VARCHAR}, #{MSG_ID,jdbcType=VARCHAR}, #{MSG_GROUP_ID,jdbcType=DECIMAL},
                #{MSG_GROUP_SNO,jdbcType=DECIMAL}, #{MSG_GROUP_CNT,jdbcType=DECIMAL}, #{TARGET,jdbcType=VARCHAR},
                #{SEND_DATE,jdbcType=DECIMAL}, #{SEND_TIME,jdbcType=DECIMAL}, to_char(sysdate,'yyyymmdd.hh24miss'),
                 #{fv_result_hold_offset,jdbcType=DECIMAL}, #{fv_result_hold_total,jdbcType=VARCHAR} ,
                #{offset,jdbcType=DECIMAL}, #{content,jdbcType=VARCHAR}   )
    </insert>

    <update id="updateRecLog" parameterType="map">
        update GTJA_SSGZ_MQ_REC_LOG
        <set>
            <if test="record.src != null">
                SRC = #{record.src,jdbcType=VARCHAR},
            </if>
            <if test="record.srcIp != null">
                SRC_IP = #{record.srcIp,jdbcType=VARCHAR},
            </if>
            <if test="record.msgType != null">
                MSG_TYPE = #{record.msgType,jdbcType=VARCHAR},
            </if>
            <if test="record.msgSubtype != null">
                MSG_SUBTYPE = #{record.msgSubtype,jdbcType=VARCHAR},
            </if>
            <if test="record.msgId != null">
                MSG_ID = #{record.msgId,jdbcType=VARCHAR},
            </if>
            <if test="record.msgGroupId != null">
                MSG_GROUP_ID = #{record.msgGroupId,jdbcType=DECIMAL},
            </if>
            <if test="record.msgGroupSno != null">
                MSG_GROUP_SNO = #{record.msgGroupSno,jdbcType=DECIMAL},
            </if>
            <if test="record.msgGroupCnt != null">
                MSG_GROUP_CNT = #{record.msgGroupCnt,jdbcType=DECIMAL},
            </if>
            <if test="record.target != null">
                TARGET = #{record.target,jdbcType=VARCHAR},
            </if>
            <if test="record.sendDate != null">
                SEND_DATE = #{record.sendDate,jdbcType=DECIMAL},
            </if>
            <if test="record.sendTime != null">
                SEND_TIME = #{record.sendTime,jdbcType=DECIMAL},
            </if>
            <if test="record.loadTime != null">
                LOAD_TIME = #{record.loadTime,jdbcType=VARCHAR},
            </if>
        </set>
    </update>

    <update id="updateRecLog" parameterType="map">
        update GTJA_SSGZ_MQ_REC_LOG
        set SRC           = #{record.src,jdbcType=VARCHAR},
            SRC_IP        = #{record.srcIp,jdbcType=VARCHAR},
            MSG_TYPE      = #{record.msgType,jdbcType=VARCHAR},
            MSG_SUBTYPE   = #{record.msgSubtype,jdbcType=VARCHAR},
            MSG_ID        = #{record.msgId,jdbcType=VARCHAR},
            MSG_GROUP_ID  = #{record.msgGroupId,jdbcType=DECIMAL},
            MSG_GROUP_SNO = #{record.msgGroupSno,jdbcType=DECIMAL},
            MSG_GROUP_CNT = #{record.msgGroupCnt,jdbcType=DECIMAL},
            TARGET        = #{record.target,jdbcType=VARCHAR},
            SEND_DATE     = #{record.sendDate,jdbcType=DECIMAL},
            SEND_TIME     = #{record.sendTime,jdbcType=DECIMAL},
            LOAD_TIME     = #{record.loadTime,jdbcType=VARCHAR}
    </update>
</mapper>