package com.gtja.faRtvData.common.utils.ip;

import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;

@Slf4j
public class IPAddressUtils {
    public static String getIPAddress() {
        try {
            InetAddress inetAddress=InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (Exception var2) {
            log.error("获取本机IP地址异常", var2);
            throw new IllegalArgumentException("获取本机IP地址异常", var2);
        }
    }

}
