package com.gtja.faRtvData.common.utils;/**
 * @ProjectName: com.gtja.mailsort.utils.forEachSplit
 * @Description: TODO
 * <AUTHOR> mayangyang
 * @Date : 2023/5/1710:04
 * @Version :1.0
 */


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName
 * @Description TODO
 * <AUTHOR> @date
 * @Version 1.0
 */
@Service
@Slf4j
public class SplitListForEachUtil {


    /**
     * list  拆分方法
     *
     * @param splitLit List<T>
     * @param number
     * @return List<List       <       T>>
     */
    public <T> List<List<T>> splitList(List<T> splitLit, int number) {
        //获取 拆分 集合
        int MAX_NUMBER = number;
        // 计算分切分次数
        Integer limit = (splitLit.size() + MAX_NUMBER - 1) / MAX_NUMBER;
        List<List<T>> splitGitOfaset = Stream.iterate(0, n -> n + 1).limit(limit)
                .parallel().map(i -> splitLit
                        .stream()
                        .skip(i * MAX_NUMBER)
                        .limit(MAX_NUMBER).parallel()
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
        return splitGitOfaset;
    }

    public <T> List<List<T>> subList(List<T> list, int size) {
        List<List<T>> lists = new ArrayList<>();
        if (list != null && size > 0) {
            int listSize = list.size();
            if (listSize <= size) {
                lists.add(list);
                return lists;
            }
            int batchSize = listSize / size;
            int remain = listSize % size;
            for (int i = 0; i < batchSize; i++) {
                int formIndex = i * size;
                int toIndex = formIndex + size;
                lists.add(list.subList(formIndex, toIndex));
            }
            if (remain > 0) {
                lists.add(list.subList(listSize - remain, listSize));
            }
        }
        return lists;
    }
}
