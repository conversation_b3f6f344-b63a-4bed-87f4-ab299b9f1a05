package com.gtja.faRtvData.dao.fbzx;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/****
 * zhanghao
 * 20201126
 */
@Repository
public interface CommonDao {

    /***
     * 判断当前是否工作日（yyyymmdd）
     * @param ywrq
     * @return
     */
    Boolean isTradeDay(String ywrq);

    /**
     * 根据当前日期计算上一交易日期
     */
    String getLastTradeDay(@Param("date") String date);

    /***
     * 获取date时间往direction(方向)num天工作日
     * direction -1 向前  +1 向后
     * num 间隔多少个交易日
     * @param date
     * @param num
     * @return
     */
    String getTradeday(@Param("date") String date, @Param("direction") String direction, @Param("num") Integer num);

    String getNextNumDay(@Param("date") String date,@Param("num") Integer num);
}

