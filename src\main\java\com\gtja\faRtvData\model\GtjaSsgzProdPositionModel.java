package generator.create;

import java.math.BigDecimal;

public class GtjaSsgzProdPositionModel {
    private String msgId;

    private BigDecimal busiDate;

    private String accountSetNo;

    private String positionCode;

    private String stkAcct;

    private String trusteeSeat;

    private String marketCode;

    private String scrCode;

    private String scrId;

    private String contractCode;

    private String remainMark;

    private String positionDirection;

    private String positionType;

    private String fncToolClass;

    private String listedCirculateSitua;

    private String applyWay;

    private String ccyCode;

    private BigDecimal exchRate;

    private String fairPriceType;

    private BigDecimal valuFairPrice;

    private BigDecimal s10101;

    private BigDecimal s10311;

    private BigDecimal s11001;

    private BigDecimal s11003;

    private BigDecimal s11020;

    private BigDecimal s11021;

    private BigDecimal s11031;

    private BigDecimal s11032;

    private BigDecimal s11033;

    private BigDecimal s12030;

    private BigDecimal s12040;

    private BigDecimal s22310;

    private BigDecimal s60110;

    private BigDecimal s60610;

    private BigDecimal s61010;

    private BigDecimal s61111;

    private BigDecimal s61112;

    private BigDecimal s61113;

    private BigDecimal s61114;

    private BigDecimal s64070;

    private BigDecimal s64110;

    private BigDecimal s67010;

    private BigDecimal s70010;

    private BigDecimal s70011;

    private BigDecimal s70012;

    private BigDecimal s70013;

    private BigDecimal s70014;

    private BigDecimal s70020;

    private BigDecimal s70030;

    private BigDecimal s70040;

    private BigDecimal s70050;

    private BigDecimal s70060;

    private BigDecimal s70110;

    private BigDecimal s90201;

    private BigDecimal s90202;

    private BigDecimal s90203;

    private BigDecimal s90204;

    private BigDecimal s90901;

    private BigDecimal s90902;

    private BigDecimal s90903;

    private BigDecimal s90904;

    private BigDecimal s90911;

    private BigDecimal s90912;

    private BigDecimal s90913;

    private BigDecimal s90914;

    private BigDecimal s90921;

    private BigDecimal s90922;

    private BigDecimal s90931;

    private BigDecimal s90932;

    private BigDecimal s90933;

    private BigDecimal s90934;

    private BigDecimal s90935;

    private BigDecimal s90936;

    private BigDecimal s91001;

    private BigDecimal s91002;

    private BigDecimal s91003;

    private BigDecimal s91004;

    private BigDecimal s91005;

    private BigDecimal dueDate;

    private BigDecimal createDate;

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public BigDecimal getBusiDate() {
        return busiDate;
    }

    public void setBusiDate(BigDecimal busiDate) {
        this.busiDate = busiDate;
    }

    public String getAccountSetNo() {
        return accountSetNo;
    }

    public void setAccountSetNo(String accountSetNo) {
        this.accountSetNo = accountSetNo;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getStkAcct() {
        return stkAcct;
    }

    public void setStkAcct(String stkAcct) {
        this.stkAcct = stkAcct;
    }

    public String getTrusteeSeat() {
        return trusteeSeat;
    }

    public void setTrusteeSeat(String trusteeSeat) {
        this.trusteeSeat = trusteeSeat;
    }

    public String getMarketCode() {
        return marketCode;
    }

    public void setMarketCode(String marketCode) {
        this.marketCode = marketCode;
    }

    public String getScrCode() {
        return scrCode;
    }

    public void setScrCode(String scrCode) {
        this.scrCode = scrCode;
    }

    public String getScrId() {
        return scrId;
    }

    public void setScrId(String scrId) {
        this.scrId = scrId;
    }

    public String getContractCode() {
        return contractCode;
    }

    public void setContractCode(String contractCode) {
        this.contractCode = contractCode;
    }

    public String getRemainMark() {
        return remainMark;
    }

    public void setRemainMark(String remainMark) {
        this.remainMark = remainMark;
    }

    public String getPositionDirection() {
        return positionDirection;
    }

    public void setPositionDirection(String positionDirection) {
        this.positionDirection = positionDirection;
    }

    public String getPositionType() {
        return positionType;
    }

    public void setPositionType(String positionType) {
        this.positionType = positionType;
    }

    public String getFncToolClass() {
        return fncToolClass;
    }

    public void setFncToolClass(String fncToolClass) {
        this.fncToolClass = fncToolClass;
    }

    public String getListedCirculateSitua() {
        return listedCirculateSitua;
    }

    public void setListedCirculateSitua(String listedCirculateSitua) {
        this.listedCirculateSitua = listedCirculateSitua;
    }

    public String getApplyWay() {
        return applyWay;
    }

    public void setApplyWay(String applyWay) {
        this.applyWay = applyWay;
    }

    public String getCcyCode() {
        return ccyCode;
    }

    public void setCcyCode(String ccyCode) {
        this.ccyCode = ccyCode;
    }

    public BigDecimal getExchRate() {
        return exchRate;
    }

    public void setExchRate(BigDecimal exchRate) {
        this.exchRate = exchRate;
    }

    public String getFairPriceType() {
        return fairPriceType;
    }

    public void setFairPriceType(String fairPriceType) {
        this.fairPriceType = fairPriceType;
    }

    public BigDecimal getValuFairPrice() {
        return valuFairPrice;
    }

    public void setValuFairPrice(BigDecimal valuFairPrice) {
        this.valuFairPrice = valuFairPrice;
    }

    public BigDecimal getS10101() {
        return s10101;
    }

    public void setS10101(BigDecimal s10101) {
        this.s10101 = s10101;
    }

    public BigDecimal getS10311() {
        return s10311;
    }

    public void setS10311(BigDecimal s10311) {
        this.s10311 = s10311;
    }

    public BigDecimal getS11001() {
        return s11001;
    }

    public void setS11001(BigDecimal s11001) {
        this.s11001 = s11001;
    }

    public BigDecimal getS11003() {
        return s11003;
    }

    public void setS11003(BigDecimal s11003) {
        this.s11003 = s11003;
    }

    public BigDecimal getS11020() {
        return s11020;
    }

    public void setS11020(BigDecimal s11020) {
        this.s11020 = s11020;
    }

    public BigDecimal getS11021() {
        return s11021;
    }

    public void setS11021(BigDecimal s11021) {
        this.s11021 = s11021;
    }

    public BigDecimal getS11031() {
        return s11031;
    }

    public void setS11031(BigDecimal s11031) {
        this.s11031 = s11031;
    }

    public BigDecimal getS11032() {
        return s11032;
    }

    public void setS11032(BigDecimal s11032) {
        this.s11032 = s11032;
    }

    public BigDecimal getS11033() {
        return s11033;
    }

    public void setS11033(BigDecimal s11033) {
        this.s11033 = s11033;
    }

    public BigDecimal getS12030() {
        return s12030;
    }

    public void setS12030(BigDecimal s12030) {
        this.s12030 = s12030;
    }

    public BigDecimal getS12040() {
        return s12040;
    }

    public void setS12040(BigDecimal s12040) {
        this.s12040 = s12040;
    }

    public BigDecimal getS22310() {
        return s22310;
    }

    public void setS22310(BigDecimal s22310) {
        this.s22310 = s22310;
    }

    public BigDecimal getS60110() {
        return s60110;
    }

    public void setS60110(BigDecimal s60110) {
        this.s60110 = s60110;
    }

    public BigDecimal getS60610() {
        return s60610;
    }

    public void setS60610(BigDecimal s60610) {
        this.s60610 = s60610;
    }

    public BigDecimal getS61010() {
        return s61010;
    }

    public void setS61010(BigDecimal s61010) {
        this.s61010 = s61010;
    }

    public BigDecimal getS61111() {
        return s61111;
    }

    public void setS61111(BigDecimal s61111) {
        this.s61111 = s61111;
    }

    public BigDecimal getS61112() {
        return s61112;
    }

    public void setS61112(BigDecimal s61112) {
        this.s61112 = s61112;
    }

    public BigDecimal getS61113() {
        return s61113;
    }

    public void setS61113(BigDecimal s61113) {
        this.s61113 = s61113;
    }

    public BigDecimal getS61114() {
        return s61114;
    }

    public void setS61114(BigDecimal s61114) {
        this.s61114 = s61114;
    }

    public BigDecimal getS64070() {
        return s64070;
    }

    public void setS64070(BigDecimal s64070) {
        this.s64070 = s64070;
    }

    public BigDecimal getS64110() {
        return s64110;
    }

    public void setS64110(BigDecimal s64110) {
        this.s64110 = s64110;
    }

    public BigDecimal getS67010() {
        return s67010;
    }

    public void setS67010(BigDecimal s67010) {
        this.s67010 = s67010;
    }

    public BigDecimal getS70010() {
        return s70010;
    }

    public void setS70010(BigDecimal s70010) {
        this.s70010 = s70010;
    }

    public BigDecimal getS70011() {
        return s70011;
    }

    public void setS70011(BigDecimal s70011) {
        this.s70011 = s70011;
    }

    public BigDecimal getS70012() {
        return s70012;
    }

    public void setS70012(BigDecimal s70012) {
        this.s70012 = s70012;
    }

    public BigDecimal getS70013() {
        return s70013;
    }

    public void setS70013(BigDecimal s70013) {
        this.s70013 = s70013;
    }

    public BigDecimal getS70014() {
        return s70014;
    }

    public void setS70014(BigDecimal s70014) {
        this.s70014 = s70014;
    }

    public BigDecimal getS70020() {
        return s70020;
    }

    public void setS70020(BigDecimal s70020) {
        this.s70020 = s70020;
    }

    public BigDecimal getS70030() {
        return s70030;
    }

    public void setS70030(BigDecimal s70030) {
        this.s70030 = s70030;
    }

    public BigDecimal getS70040() {
        return s70040;
    }

    public void setS70040(BigDecimal s70040) {
        this.s70040 = s70040;
    }

    public BigDecimal getS70050() {
        return s70050;
    }

    public void setS70050(BigDecimal s70050) {
        this.s70050 = s70050;
    }

    public BigDecimal getS70060() {
        return s70060;
    }

    public void setS70060(BigDecimal s70060) {
        this.s70060 = s70060;
    }

    public BigDecimal getS70110() {
        return s70110;
    }

    public void setS70110(BigDecimal s70110) {
        this.s70110 = s70110;
    }

    public BigDecimal getS90201() {
        return s90201;
    }

    public void setS90201(BigDecimal s90201) {
        this.s90201 = s90201;
    }

    public BigDecimal getS90202() {
        return s90202;
    }

    public void setS90202(BigDecimal s90202) {
        this.s90202 = s90202;
    }

    public BigDecimal getS90203() {
        return s90203;
    }

    public void setS90203(BigDecimal s90203) {
        this.s90203 = s90203;
    }

    public BigDecimal getS90204() {
        return s90204;
    }

    public void setS90204(BigDecimal s90204) {
        this.s90204 = s90204;
    }

    public BigDecimal getS90901() {
        return s90901;
    }

    public void setS90901(BigDecimal s90901) {
        this.s90901 = s90901;
    }

    public BigDecimal getS90902() {
        return s90902;
    }

    public void setS90902(BigDecimal s90902) {
        this.s90902 = s90902;
    }

    public BigDecimal getS90903() {
        return s90903;
    }

    public void setS90903(BigDecimal s90903) {
        this.s90903 = s90903;
    }

    public BigDecimal getS90904() {
        return s90904;
    }

    public void setS90904(BigDecimal s90904) {
        this.s90904 = s90904;
    }

    public BigDecimal getS90911() {
        return s90911;
    }

    public void setS90911(BigDecimal s90911) {
        this.s90911 = s90911;
    }

    public BigDecimal getS90912() {
        return s90912;
    }

    public void setS90912(BigDecimal s90912) {
        this.s90912 = s90912;
    }

    public BigDecimal getS90913() {
        return s90913;
    }

    public void setS90913(BigDecimal s90913) {
        this.s90913 = s90913;
    }

    public BigDecimal getS90914() {
        return s90914;
    }

    public void setS90914(BigDecimal s90914) {
        this.s90914 = s90914;
    }

    public BigDecimal getS90921() {
        return s90921;
    }

    public void setS90921(BigDecimal s90921) {
        this.s90921 = s90921;
    }

    public BigDecimal getS90922() {
        return s90922;
    }

    public void setS90922(BigDecimal s90922) {
        this.s90922 = s90922;
    }

    public BigDecimal getS90931() {
        return s90931;
    }

    public void setS90931(BigDecimal s90931) {
        this.s90931 = s90931;
    }

    public BigDecimal getS90932() {
        return s90932;
    }

    public void setS90932(BigDecimal s90932) {
        this.s90932 = s90932;
    }

    public BigDecimal getS90933() {
        return s90933;
    }

    public void setS90933(BigDecimal s90933) {
        this.s90933 = s90933;
    }

    public BigDecimal getS90934() {
        return s90934;
    }

    public void setS90934(BigDecimal s90934) {
        this.s90934 = s90934;
    }

    public BigDecimal getS90935() {
        return s90935;
    }

    public void setS90935(BigDecimal s90935) {
        this.s90935 = s90935;
    }

    public BigDecimal getS90936() {
        return s90936;
    }

    public void setS90936(BigDecimal s90936) {
        this.s90936 = s90936;
    }

    public BigDecimal getS91001() {
        return s91001;
    }

    public void setS91001(BigDecimal s91001) {
        this.s91001 = s91001;
    }

    public BigDecimal getS91002() {
        return s91002;
    }

    public void setS91002(BigDecimal s91002) {
        this.s91002 = s91002;
    }

    public BigDecimal getS91003() {
        return s91003;
    }

    public void setS91003(BigDecimal s91003) {
        this.s91003 = s91003;
    }

    public BigDecimal getS91004() {
        return s91004;
    }

    public void setS91004(BigDecimal s91004) {
        this.s91004 = s91004;
    }

    public BigDecimal getS91005() {
        return s91005;
    }

    public void setS91005(BigDecimal s91005) {
        this.s91005 = s91005;
    }

    public BigDecimal getDueDate() {
        return dueDate;
    }

    public void setDueDate(BigDecimal dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getCreateDate() {
        return createDate;
    }

    public void setCreateDate(BigDecimal createDate) {
        this.createDate = createDate;
    }
}