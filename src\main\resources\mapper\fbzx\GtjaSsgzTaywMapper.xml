<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gtja.faRtvData.dao.fbzx.RtvRjzjbdInfoDao">
    <resultMap id="TaywResultMap" type="com.gtja.faRtvData.model.GtjaSsgzTaywModel">
        <result column="TYPE" jdbcType="VARCHAR" property="type"/>
        <result column="CREATE_DATE" jdbcType="DATE" property="create_date"/>
        <result column="LV_PROD_CODE" jdbcType="VARCHAR" property="lv_prod_code"/>
        <result column="BUSI_DATE" jdbcType="DECIMAL" property="busi_date"/>
        <result column="PROD_CODE" jdbcType="VARCHAR" property="prod_code"/>
        <result column="BUSI_FLOW_NO" jdbcType="DECIMAL" property="busi_flow_no"/>
        <result column="ACCOUNT_SET_NO" jdbcType="VARCHAR" property="account_set_no"/>
        <result column="DIGEST_ID" jdbcType="VARCHAR" property="digest_id"/>
        <result column="CCY_CODE" jdbcType="CHAR" property="ccy_code"/>
        <result column="TRD_APPLY_DATE" jdbcType="DECIMAL" property="trd_apply_date"/>
        <result column="CONFIRM_DATE" jdbcType="DECIMAL" property="confirm_date"/>
        <result column="SETTLE_DATE" jdbcType="DECIMAL" property="settle_date"/>
        <result column="MATCH_SHARE" jdbcType="DECIMAL" property="match_share"/>
        <result column="MATCH_AMT" jdbcType="DECIMAL" property="match_amt"/>
        <result column="CONFIRM_AMT" jdbcType="DECIMAL" property="confirm_amt"/>
        <result column="DIVD_AMT" jdbcType="DECIMAL" property="divd_amt"/>
        <result column="FEE_BCF" jdbcType="DECIMAL" property="fee_bcf"/>
        <result column="FEE_GHF" jdbcType="DECIMAL" property="fee_ghf"/>
        <result column="FEE_GJJZC" jdbcType="DECIMAL" property="fee_gjjzc"/>
        <result column="BACK_APPLY_FEE" jdbcType="DECIMAL" property="back_apply_fee"/>
        <result column="FEE_JYF" jdbcType="DECIMAL" property="fee_jyf"/>
        <result column="FEE_QTF_USE" jdbcType="DECIMAL" property="fee_qtf_use"/>
        <result column="FRZ_AMT" jdbcType="DECIMAL" property="frz_amt"/>
        <result column="INC_TAX_AMT" jdbcType="DECIMAL" property="inc_tax_amt"/>
        <result column="MINUS_AMT" jdbcType="DECIMAL" property="minus_amt"/>
        <result column="PAY_PERFORMANCE" jdbcType="DECIMAL" property="pay_performance"/>
        <result column="PLUS_AMT" jdbcType="DECIMAL" property="plus_amt"/>
        <result column="THEN_INVEST_BONUS_CASH_AMT" jdbcType="DECIMAL" property="then_invest_bonus_cash_amt"/>
        <result column="THEN_INVEST_PRICE" jdbcType="DECIMAL" property="then_invest_price"/>
        <result column="THEN_INVEST_SHARE" jdbcType="DECIMAL" property="then_invest_share"/>
        <result column="SHARE_BAL" jdbcType="DECIMAL" property="share_bal"/>
        <result column="PROFIT_LOSS_EQUALIZ_UN_REALIZE" jdbcType="DECIMAL" property="profit_loss_equaliz_un_realize"/>
        <result column="FEE_YHS" jdbcType="DECIMAL" property="fee_yhs"/>
        <result column="TRANFIN_YIELD" jdbcType="DECIMAL" property="tranfin_yield"/>
        <result column="TRANFIN_SHARE" jdbcType="DECIMAL" property="tranfin_share"/>
        <result column="TRANF_OUT_YIELD" jdbcType="DECIMAL" property="tranf_out_yield"/>
        <result column="TRANF_OUT_SHARE" jdbcType="DECIMAL" property="tranf_out_share"/>
        <result column="UN_PAY_YIELD" jdbcType="DECIMAL" property="un_pay_yield"/>
        <result column="SHARE_LV" jdbcType="CHAR" property="share_lv"/>
        <result column="SHARE_TYPE" jdbcType="CHAR" property="share_type"/>
        <result column="CHECK_STATUS" jdbcType="VARCHAR" property="check_status"/>
        <result column="SEND_DATA" jdbcType="DATE" property="send_data"/>
        <result column="SEND_STATUS" jdbcType="VARCHAR" property="send_status"/>
        <result column="CHECK_DATA" jdbcType="DATE" property="check_data"/>
        <result column="OPER" jdbcType="VARCHAR" property="oper"/>
    </resultMap>

    <sql id="Tayw_Column_List">
        BUSI_DATE
        , BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID, CCY_CODE,
    TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT, 
    FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT, 
    MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE, 
    THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD, 
    TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE, 
    CHECK_STATUS, SEND_DATA, SEND_STATUS, CHECK_DATA, OPER,CREATE_DATE
    </sql>
    <sql id="Tayw_Mq_Column_List">
        BUSI_DATE
        , BUSI_FLOW_NO, ACCOUNT_SET_NO,  PROD_CODE,LV_PROD_CODE, DIGEST_ID, CCY_CODE,
    TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT,
    FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT,
    MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE,
    THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD,
    TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE
    </sql>

    <select id="queryRtvRjzjbdTaInfo" parameterType="java.lang.String"
            resultMap="TaywResultMap">
        select BUSI_DATE
                , BUSI_FLOW_NO, ACCOUNT_SET_NO,cpdm  as   PROD_CODE,fjcpdm  LV_PROD_CODE, DIGEST_ID, CCY_CODE,
               TRD_APPLY_DATE, CONFIRM_DATE, SETTLE_DATE, MATCH_SHARE, MATCH_AMT, CONFIRM_AMT, DIVD_AMT,
               FEE_BCF, FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE, FEE_JYF, FEE_QTF_USE, FRZ_AMT, INC_TAX_AMT,
               MINUS_AMT, PAY_PERFORMANCE, PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE,
               THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE, FEE_YHS, TRANFIN_YIELD,
               TRANFIN_SHARE, TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD, SHARE_LV, SHARE_TYPE
        from fbzx.gtja_ssgz_tayw t
        where  t.BUSI_DATE = #{busi_date,jdbcType=VARCHAR}
          AND (t.send_status is null or t.send_status &lt;>'1')
    </select>

    <delete id="deleteByExample" parameterType="com.gtja.faRtvData.model.GtjaSsgzTaywModel">
        delete
        from GTJA_SSGZ_TAYW
    </delete>

    <insert id="insert" parameterType="com.gtja.faRtvData.model.GtjaSsgzTaywModel">
        insert into GTJA_SSGZ_TAYW (TYPE, CREATE_DATE, BUSI_DATE,
                                    BUSI_FLOW_NO, ACCOUNT_SET_NO, DIGEST_ID,
                                    CCY_CODE, TRD_APPLY_DATE, CONFIRM_DATE,
                                    SETTLE_DATE, MATCH_SHARE, MATCH_AMT,
                                    CONFIRM_AMT, DIVD_AMT, FEE_BCF,
                                    FEE_GHF, FEE_GJJZC, BACK_APPLY_FEE,
                                    FEE_JYF, FEE_QTF_USE, FRZ_AMT,
                                    INC_TAX_AMT, MINUS_AMT, PAY_PERFORMANCE,
                                    PLUS_AMT, THEN_INVEST_BONUS_CASH_AMT, THEN_INVEST_PRICE,
                                    THEN_INVEST_SHARE, SHARE_BAL, PROFIT_LOSS_EQUALIZ_UN_REALIZE,
                                    FEE_YHS, TRANFIN_YIELD, TRANFIN_SHARE,
                                    TRANF_OUT_YIELD, TRANF_OUT_SHARE, UN_PAY_YIELD,
                                    SHARE_LV, SHARE_TYPE, CHECK_STATUS,
                                    SEND_DATA, SEND_STATUS, CHECK_DATA,
                                    OPER)
        values (#{type,jdbcType=VARCHAR}, #{createDate,jdbcType=DATE}, #{busiDate,jdbcType=DECIMAL},
                #{busiFlowNo,jdbcType=DECIMAL}, #{accountSetNo,jdbcType=VARCHAR}, #{digestId,jdbcType=VARCHAR},
                #{ccyCode,jdbcType=CHAR}, #{trdApplyDate,jdbcType=DECIMAL}, #{confirmDate,jdbcType=DECIMAL},
                #{settleDate,jdbcType=DECIMAL}, #{matchShare,jdbcType=DECIMAL}, #{matchAmt,jdbcType=DECIMAL},
                #{confirmAmt,jdbcType=DECIMAL}, #{divdAmt,jdbcType=DECIMAL}, #{feeBcf,jdbcType=DECIMAL},
                #{feeGhf,jdbcType=DECIMAL}, #{feeGjjzc,jdbcType=DECIMAL}, #{backApplyFee,jdbcType=DECIMAL},
                #{feeJyf,jdbcType=DECIMAL}, #{feeQtfUse,jdbcType=DECIMAL}, #{frzAmt,jdbcType=DECIMAL},
                #{incTaxAmt,jdbcType=DECIMAL}, #{minusAmt,jdbcType=DECIMAL}, #{payPerformance,jdbcType=DECIMAL},
                #{plusAmt,jdbcType=DECIMAL}, #{thenInvestBonusCashAmt,jdbcType=DECIMAL},
                #{thenInvestPrice,jdbcType=DECIMAL},
                #{thenInvestShare,jdbcType=DECIMAL}, #{shareBal,jdbcType=DECIMAL},
                #{profitLossEqualizUnRealize,jdbcType=DECIMAL},
                #{feeYhs,jdbcType=DECIMAL}, #{tranfinYield,jdbcType=DECIMAL}, #{tranfinShare,jdbcType=DECIMAL},
                #{tranfOutYield,jdbcType=DECIMAL}, #{tranfOutShare,jdbcType=DECIMAL}, #{unPayYield,jdbcType=DECIMAL},
                #{shareLv,jdbcType=CHAR}, #{shareType,jdbcType=CHAR}, #{checkStatus,jdbcType=VARCHAR},
                #{sendData,jdbcType=DATE}, #{sendStatus,jdbcType=VARCHAR}, #{checkData,jdbcType=DATE},
                #{oper,jdbcType=VARCHAR})
    </insert>
     <update id="updateRtvRjzjbdTaInfo" >
         update GTJA_SSGZ_TAYW
         set
         SEND_DATA = sysdate,
         SEND_STATUS = '1',
         OPER = 'SYS'
         <where>
             <if test="busiDate != null">
                 and BUSI_DATE = #{busiDate,jdbcType=DECIMAL}
             </if>
             <if test="busiFlowNo != null">
                 and BUSI_FLOW_NO = #{busiFlowNo,jdbcType=DECIMAL}
             </if>
         </where>
     </update>
    <update id="updateByExample" parameterType="map">
        update GTJA_SSGZ_TAYW
        set TYPE                           = #{type,jdbcType=VARCHAR},
            CREATE_DATE                    = #{createDate,jdbcType=DATE},
            BUSI_DATE                      = #{busiDate,jdbcType=DECIMAL},
            BUSI_FLOW_NO                   = #{busiFlowNo,jdbcType=DECIMAL},
            ACCOUNT_SET_NO                 = #{accountSetNo,jdbcType=VARCHAR},
            DIGEST_ID                      = #{digestId,jdbcType=VARCHAR},
            CCY_CODE                       = #{ccyCode,jdbcType=CHAR},
            TRD_APPLY_DATE                 = #{trdApplyDate,jdbcType=DECIMAL},
            CONFIRM_DATE                   = #{confirmDate,jdbcType=DECIMAL},
            SETTLE_DATE                    = #{settleDate,jdbcType=DECIMAL},
            MATCH_SHARE                    = #{matchShare,jdbcType=DECIMAL},
            MATCH_AMT                      = #{matchAmt,jdbcType=DECIMAL},
            CONFIRM_AMT                    = #{confirmAmt,jdbcType=DECIMAL},
            DIVD_AMT                       = #{divdAmt,jdbcType=DECIMAL},
            FEE_BCF                        = #{feeBcf,jdbcType=DECIMAL},
            FEE_GHF                        = #{feeGhf,jdbcType=DECIMAL},
            FEE_GJJZC                      = #{feeGjjzc,jdbcType=DECIMAL},
            BACK_APPLY_FEE                 = #{backApplyFee,jdbcType=DECIMAL},
            FEE_JYF                        = #{feeJyf,jdbcType=DECIMAL},
            FEE_QTF_USE                    = #{feeQtfUse,jdbcType=DECIMAL},
            FRZ_AMT                        = #{frzAmt,jdbcType=DECIMAL},
            INC_TAX_AMT                    = #{incTaxAmt,jdbcType=DECIMAL},
            MINUS_AMT                      = #{minusAmt,jdbcType=DECIMAL},
            PAY_PERFORMANCE                = #{payPerformance,jdbcType=DECIMAL},
            PLUS_AMT                       = #{plusAmt,jdbcType=DECIMAL},
            THEN_INVEST_BONUS_CASH_AMT     = #{thenInvestBonusCashAmt,jdbcType=DECIMAL},
            THEN_INVEST_PRICE              = #{thenInvestPrice,jdbcType=DECIMAL},
            THEN_INVEST_SHARE              = #{thenInvestShare,jdbcType=DECIMAL},
            SHARE_BAL                      = #{shareBal,jdbcType=DECIMAL},
            PROFIT_LOSS_EQUALIZ_UN_REALIZE = #{profitLossEqualizUnRealize,jdbcType=DECIMAL},
            FEE_YHS                        = #{feeYhs,jdbcType=DECIMAL},
            TRANFIN_YIELD                  = #{tranfinYield,jdbcType=DECIMAL},
            TRANFIN_SHARE                  = #{tranfinShare,jdbcType=DECIMAL},
            TRANF_OUT_YIELD                = #{tranfOutYield,jdbcType=DECIMAL},
            TRANF_OUT_SHARE                = #{tranfOutShare,jdbcType=DECIMAL},
            UN_PAY_YIELD                   = #{unPayYield,jdbcType=DECIMAL},
            SHARE_LV                       = #{shareLv,jdbcType=CHAR},
            SHARE_TYPE                     = #{shareType,jdbcType=CHAR},
            CHECK_STATUS                   = #{checkStatus,jdbcType=VARCHAR},
            SEND_DATA                      = #{sendData,jdbcType=DATE},
            SEND_STATUS                    = #{sendStatus,jdbcType=VARCHAR},
            CHECK_DATA                     = #{checkData,jdbcType=DATE},
            OPER                           = #{oper,jdbcType=VARCHAR}

    </update>
</mapper>