package com.gtja.faRtvData.service.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.gtja.faRtvData.common.ienum.Constants;
import com.gtja.faRtvData.common.ienum.TopicConstans;
import com.gtja.faRtvData.common.utils.ip.IPAddressUtils;
import com.gtja.faRtvData.dao.fbzx.RtvRjzjbdInfoDao;
import com.gtja.faRtvData.dao.fbzx.RtvSendLogDao;
import com.gtja.faRtvData.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import tools.DateTimeTools;

import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.Future;

/**
 * 实时估值消息发送
 */
@Slf4j
@Service
public class MessageSender {
    @Value("${ssgz.kafka.servers}")
    private String BOOTSTRAP_SERVERS;
    @Autowired
    private RtvRjzjbdInfoDao rtvRjzjbdInfoDao;
    @Autowired
    private RtvSendLogDao rtvSendLogDao;


    /**
     * @demoName: sendMessage
     * @Anthor: Magic
     * @Create: 2025/1/22- 11:08
     * @TODO: //TODO  username="sszcKafkaUser" password="sszcKafkaUser@2024\  生产待定 end
     * @Param:
     * @Return:
     */
    public String sendMessage(String ywlx, String busi_date) {
        Properties props = new Properties();
        props.put("bootstrap.servers", BOOTSTRAP_SERVERS);//163   10.187.65.42:9093    10.176.182.137:9092
        props.put("acks", "all");               //消息确认机制  0-不等待相应 1-消息会被写到本地日志中  all-leader会等待所有follower同步完成，确保消息不会动了丢
        props.put("retries", 0);                //大于0 的值 客户端会在消息发送失败 时冲重新发送
        props.put("batch.size", 16384);         //
        props.put("linger.ms", 1);
        props.put("buffer.memory", 33554432);
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        props.put(SaslConfigs.SASL_MECHANISM, "SCRAM-SHA-512");
        props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"zctgKafkaUser\" password=\"zctgKafkaUser@2025\";");
        // props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"sszcKafkaUser\" password=\"sszcKafkaUser@2024\";");
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);
        try {
            log.info("SSGZ-sendMessage :" + "开始发送实时估值消息");
            sendMessage(producer, ywlx, busi_date);
            log.info("SSGZ-sendMessage :" + "发送实时估值消息完成");
        } catch (Exception e) {
            log.info("SSGZ-sendMessage 发送异常{} :", e.getMessage());
            return e.getMessage();
            // throw new RuntimeException(e);
        }
        return "业务类型" + ywlx + "消息发送成功";
    }


    public void sendMessage(KafkaProducer<String, String> finalProducer, String ywlx, String busi_date) {
        log.info("开始发送kafka消息，业务类型{},日期{}", busi_date, ywlx);
        if (Constants.RJZJBD_YWLX.TA.equals(ywlx)) {
            List<GtjaSsgzTaywModel> tayws = rtvRjzjbdInfoDao.queryRtvRjzjbdTaInfo(busi_date);
            RtvRjzjbdTaMqModel base = new RtvRjzjbdTaMqModel();
            SsgzMqSendLog logInfo = new SsgzMqSendLog();
            tayws.stream().forEach(info -> { //parallelStream
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                BeanUtils.copyProperties(base, logInfo);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base, SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullNumberAsZero);
                Future<RecordMetadata> future = finalProducer.send(new ProducerRecord<String, String>(TopicConstans.TOPIC_RJZJBD, message));
                System.out.printf("发送的信息：" + message);
                //记录日志
                // future.cancel(true -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
                logInfo.setMESSAGE(message);
                try {
                    rtvSendLogDao.insertSenderLog(logInfo);
                    rtvRjzjbdInfoDao.updateRtvRjzjbdTaInfo(info.getBusi_flow_no(),busi_date);
                } catch (Exception e) {
                    log.info("日志记录异常{}：" + logInfo.toString()+e.getMessage());
                }
            });
        } else if (Constants.RJZJBD_YWLX.ZQTZ.equals(ywlx)) {
            List<GtjaSsgzZjgpywModel> zqtzYws = rtvRjzjbdInfoDao.queryRtvRjzjbdZjgpInfo(busi_date);
            RtvRjzjbdGpMqModel base = new RtvRjzjbdGpMqModel();
            SsgzMqSendLog logInfo = new SsgzMqSendLog();
            zqtzYws.stream().forEach(info -> {
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                BeanUtils.copyProperties(base, logInfo);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base, SerializerFeature.WRITE_MAP_NULL_FEATURES, SerializerFeature.WriteNullNumberAsZero);
                Future<RecordMetadata> future = finalProducer.send(new ProducerRecord<String, String>(TopicConstans.TOPIC_RJZJBD, message));
                System.out.printf("发送的信息：" + message);
                //   future.addCallback(o -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
                logInfo.setMESSAGE(message);
                try {
                    rtvSendLogDao.insertSenderLog(logInfo);
                    rtvRjzjbdInfoDao.updateRtvRjzjbdZjgpInfo(info.getBusi_flow_no(),busi_date);
                } catch (Exception e) {
                    log.info("日志记录异常{}：" + logInfo.toString()+e.getMessage());
                }
            });
        }
    }

    /**
     * mq报文头设置
     *
     * @param info
     * @param ywlx
     */
    private void mqHandInit(RtvRjzjbdGpMqModel info, String ywlx) {
        info.setSRC_IP(IPAddressUtils.getIPAddress());
        info.setMSG_ID(UUID.randomUUID().toString());
        info.setMSG_TYPE(Constants.RJZJBD_YWLX.COMMOM);
        info.setMSG_SUBTYPE(Constants.RJZJBD_YWLX.COMMOM + "_" + ywlx);
        info.setSEND_DATE(DateTimeTools.getDateNumNow());
        info.setSEND_TIME(DateTimeTools.getTimestampNow().substring(8, 14));
    }

    private void mqHandInit(RtvRjzjbdTaMqModel info, String ywlx) {
        info.setSRC_IP(IPAddressUtils.getIPAddress());
        info.setMSG_ID(UUID.randomUUID().toString());
        info.setMSG_TYPE(Constants.RJZJBD_YWLX.COMMOM);
        info.setMSG_SUBTYPE(Constants.RJZJBD_YWLX.COMMOM + "_" + ywlx);
        info.setSEND_DATE(DateTimeTools.getDateNumNow());
        info.setSEND_TIME(DateTimeTools.getTimestampNow().substring(8, 14));
    }

    private void test001(String ywlx, String busi_date) {
        if (Constants.RJZJBD_YWLX.TA.equals(ywlx)) {
            List<GtjaSsgzTaywModel> tayws = rtvRjzjbdInfoDao.queryRtvRjzjbdTaInfo(busi_date);
            tayws.parallelStream().forEach(info -> {
                RtvRjzjbdTaMqModel base = new RtvRjzjbdTaMqModel();
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base);
                //ListenableFuture future = kafkaTemplate.send(TopicConstans.TOPIC_RJZJBD, message);
                // future.addCallback(o -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
            });
        } else if (Constants.RJZJBD_YWLX.ZQTZ.equals(ywlx)) {
            List<GtjaSsgzZjgpywModel> zqtzYws = rtvRjzjbdInfoDao.queryRtvRjzjbdZjgpInfo(busi_date);
            zqtzYws.parallelStream().forEach(info -> {
                RtvRjzjbdGpMqModel base = new RtvRjzjbdGpMqModel();
                base.setCONTENT(info);
                mqHandInit(base, ywlx);
                log.info("send-risk,MSG_ID:{} ", base.getMSG_ID());
                String message = JSON.toJSONString(base);
                //ListenableFuture future = kafkaTemplate.send(TopicConstans.TOPIC_RJZJBD, message);
                // future.addCallback(o -> log.info("send-risk消息发送成功：" + message), throwable -> log.info("risk消息发送失败：" + message));
            });
        }

    }

}
