package com.gtja.faRtvData.xxlHandler.rtvSftpHandler;

import com.gtja.faRtvData.model.SftpInfoModel;
import com.gtja.faRtvData.service.sftp.FtpSsgzFileDealService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @FileName:  FtpSsgzFilePutHandler.java
 * @anthor: Magic
 * @create: 2025/1/21- 16:45
 * @TODO:   //TODO 第二版 使用ftp 直接进行文件传输 ，受限于文件生成速度 ，不建议使用 ，作废  end
 * @Version：1.0
 */
@Slf4j
@Component
public class FtpSsgzFilePutHandler {

    private String RTV_BASE_PATH = "D:\\Users\\User\\Desktop\\SSGZ\\YYYYMMDD\\";
    @Value("${baseFilePath.sftpTargetPath}")
    protected static String SFTP_PATH ;
   // protected static String SFTP_PATH = "/SSGZ/";
    @Autowired
    SftpInfoModel dbinfo;
    @Autowired
    private FtpSsgzFileDealService ftpSsgzFileDealService;

    /**
     * 把实时估值文件夹下的文件copy （put）到sftp 目标路径下
     *
     * @param param
     * @return
     */
    @XxlJob("FtpSsgzFilePutHandler")
    public ReturnT<String> SftpSsgzFilePut(String param) {
        String handlerName = new Exception().getStackTrace()[1].getClassName();
        log.info("##实时估值ftp 文件处理 handler start, handler name = {}. ##", handlerName);
        ReturnT<String> result = new ReturnT<>(handlerName);
        try {
            //String message = ftpSsgzFileDealService.FtpSsgzFilePut(RTV_BASE_PATH, SFTP_PATH, null, dbinfo);
            String message = ftpSsgzFileDealService.FtpSsgzFilePut("D:\\Users\\User\\Desktop\\实时估值\\20241024\\", "D:\\Users\\User\\Desktop\\SSGZ\\20241024New\\", null, dbinfo);
            result.setMsg(message);
        } catch (Exception e) {
            e.printStackTrace();
            result.setCode(ReturnT.FAIL_CODE);
            result.setMsg("ftp文件落库失败：" + e.toString());
            log.info("ftp文件落库失败:{}", e.toString());
        } finally {
            return result;
        }
    }
}
