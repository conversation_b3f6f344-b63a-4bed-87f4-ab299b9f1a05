package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 * 概要说明：经纪商名称、经纪商编码
 */
@Data
public class RtvTzqxxModel {

    @DbfAnnotation(fieldName = "L_ZQNM", fieldLength = 12)
    private String l_zqnm;
    @DbfAnnotation(fieldName = "VC_ZQDM", fieldLength = 20)
    private String vc_zqdm;
    @DbfAnnotation(fieldName = "L_ZQLB", fieldLength = 4)
    private String l_zqlb;
    @DbfAnnotation(fieldName = "L_ZQLBMX1", fieldLength = 4)
    private String l_zqlbmx1;
    @DbfAnnotation(fieldName = "L_ZQLBMX2", fieldLength = 4)
    private String l_zqlbmx2;
    @DbfAnnotation(fieldName = "L_SCLB", fieldLength = 4)
    private String l_sclb;
    @DbfAnnotation(fieldName = "VC_ZQJC", fieldLength = 120)
    private String vc_zqjc;
    @DbfAnnotation(fieldName = "VC_JSBZ", fieldLength = 3)
    private String vc_jsbz;
    @DbfAnnotation(fieldName = "EN_MGMZ", fieldLength = 50)
    private String en_mgmz;
    @DbfAnnotation(fieldName = "D_FXRQ", fieldLength = 12)
    private String d_fxrq;
    @DbfAnnotation(fieldName = "D_DQRQ", fieldLength = 12)
    private String d_dqrq;
    @DbfAnnotation(fieldName = "C_ZQZT", fieldLength = 1)
    private String c_zqzt;
    @DbfAnnotation(fieldName = "L_HGTS", fieldLength = 4)
    private String l_hgts;
    @DbfAnnotation(fieldName = "L_NJXTS", fieldLength = 4)
    private String l_njxts;
    @DbfAnnotation(fieldName = "EN_NLL", fieldLength = 32)
    private String en_nll;
    @DbfAnnotation(fieldName = "D_QXR", fieldLength = 12)
    private String d_qxr;
    @DbfAnnotation(fieldName = "L_FXFS", fieldLength = 4)
    private String l_fxfs;
    @DbfAnnotation(fieldName = "D_MODIFY", fieldLength = 12)
    private String d_modify;
    @DbfAnnotation(fieldName = "L_JXFS", fieldLength = 1)
    private String l_jxfs;
    @DbfAnnotation(fieldName = "EN_FXJG", fieldLength = 32)
    private String en_fxjg;
    @DbfAnnotation(fieldName = "VC_ZQQC", fieldLength = 254)
    private String vc_zqqc;
    @DbfAnnotation(fieldName = "L_HYYF", fieldLength = 4)
    private String l_hyyf;
    @DbfAnnotation(fieldName = "EN_BZJL", fieldLength = 32)
    private String en_bzjl;
    @DbfAnnotation(fieldName = "D_JGRQ", fieldLength = 12)
    private String d_jgrq;
    @DbfAnnotation(fieldName = "L_JGFS", fieldLength = 4)
    private String l_jgfs;
    @DbfAnnotation(fieldName = "L_HYZT", fieldLength = 4)
    private String l_hyzt;
    @DbfAnnotation(fieldName = "VC_MBBZ", fieldLength = 3)
    private String vc_mbbz;
    @DbfAnnotation(fieldName = "EN_HYCS", fieldLength = 20)
    private String en_hycs;
    @DbfAnnotation(fieldName = "C_QJJJ", fieldLength = 1)
    private String c_qjjj;
    @DbfAnnotation(fieldName = "L_LXJSFF", fieldLength = 2)
    private String l_lxjsff;
    @DbfAnnotation(fieldName = "D_ZGQKS", fieldLength = 12)
    private String d_zgqks;
    @DbfAnnotation(fieldName = "D_ZGQJS", fieldLength = 12)
    private String d_zgqjs;
    @DbfAnnotation(fieldName = "VC_HQBZ", fieldLength = 4)
    private String vc_hqbz;
    @DbfAnnotation(fieldName = "L_DQFXFS", fieldLength = 4)
    private String l_dqfxfs;
    @DbfAnnotation(fieldName = "L_YMFXBZ", fieldLength = 1)
    private String l_ymfxbz;
    @DbfAnnotation(fieldName = "L_GLZQNM", fieldLength = 12)
    private String l_glzqnm;

}
