package com.gtja.faRtvData.model;

import fileTools.dbf.DbfAnnotation;
import lombok.Data;

/**
 *   概要说明：科目名称、科目代码、科目要素信息
 */
@Data
public class RtvTaccountDbfModel {
    @DbfAnnotation(fieldName = "L_FUNDID", fieldLength = 6)
    private String l_fundid;
    @DbfAnnotation(fieldName = "VC_CODE", fieldLength = 32)
    private String vc_code;
    @DbfAnnotation(fieldName = "VC_NAME", fieldLength = 254)
    private String vc_name;
    @DbfAnnotation(fieldName = "L_KIND", fieldLength = 4)
    private String l_kind;
    @DbfAnnotation(fieldName = "VC_CODE_HS", fieldLength = 32)
    private String vc_code_hs;
    @DbfAnnotation(fieldName = "L_LEVEL", fieldLength = 2)
    private String l_level;
    @DbfAnnotation(fieldName = "VC_PARENT", fieldLength = 32)
    private String vc_parent;
    @DbfAnnotation(fieldName = "L_LEAF", fieldLength = 1)
    private String l_leaf;
    @DbfAnnotation(fieldName = "D_CREATE", fieldLength = 16)
    private String d_create;
    @DbfAnnotation(fieldName = "L_QUANTITY", fieldLength = 4)
    private String l_quantity;
    @DbfAnnotation(fieldName = "VC_FZHS", fieldLength = 9)
    private String vc_fzhs;
    @DbfAnnotation(fieldName = "L_SCLB", fieldLength = 4)
    private String l_sclb;
}
